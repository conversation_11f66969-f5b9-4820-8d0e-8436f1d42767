# 知识库管理功能说明

## 📚 功能概述

知识库管理功能已成功整合到统一的后台管理系统中，提供完整的知识库管理、查询测试、统计分析等功能。

## 🎯 主要功能

### 1. 概览统计
- **文档数量统计**：总文档数、唯一文档数
- **分类统计**：角色类型、分类数量
- **分布图表**：角色分布饼图、分类分布柱状图
- **详细统计表**：角色和分类的详细数据

### 2. 文档管理
- **文档浏览**：分页浏览所有知识库文档
- **高级筛选**：按角色、分类筛选文档
- **内容搜索**：全文搜索文档内容
- **文档详情**：查看完整文档内容和元数据

### 3. 查询测试
- **智能查询**：测试知识库搜索功能
- **参数调节**：调整返回数量、相似度阈值
- **角色筛选**：按角色筛选查询结果
- **结果分析**：查看相似度评分和元数据

### 4. 统计分析
- **内容分析**：文档长度分布、统计指标
- **角色统计**：各角色文档数量和占比
- **分类统计**：各分类文档数量和占比
- **交叉分析**：角色-分类交叉分布矩阵

### 5. 系统状态
- **连接状态**：ChromaDB连接状态监控
- **配置信息**：数据库路径、集合名称、嵌入模型
- **组件状态**：各组件运行状态检查
- **健康检查**：系统整体健康状态

## 🚀 使用方法

### 启动系统

#### 方法一：使用启动脚本（推荐）
```bash
# 启动包含知识库管理的后台管理系统
./start_admin.sh
```

#### 方法二：手动启动
```bash
# 1. 安装后端依赖
cd admin-backend
pip install -r requirements.txt

# 2. 测试知识库服务
python3 test_knowledge_base.py

# 3. 启动后端服务
python3 main.py

# 4. 启动前端服务（另一个终端）
cd admin-frontend
npm install
npm start
```

### 访问地址
- **前端管理界面**：http://localhost:3000
- **后端API**：http://localhost:8002
- **API文档**：http://localhost:8002/docs

### 导航路径
在后台管理系统中，点击左侧导航菜单的"知识库管理"即可访问所有功能。

## 🔧 技术架构

### 后端架构
```
admin-backend/
├── admin_api/routers/knowledge_base.py     # API路由
├── admin_services/knowledge_base_service.py # 业务逻辑
└── test_knowledge_base.py                  # 测试脚本
```

### 前端架构
```
admin-frontend/src/
├── pages/KnowledgeBase/
│   ├── index.tsx                          # 主页面
│   └── components/
│       ├── OverviewTab.tsx               # 概览标签页
│       ├── DocumentsTab.tsx              # 文档管理标签页
│       ├── QueryTab.tsx                  # 查询测试标签页
│       ├── StatisticsTab.tsx             # 统计分析标签页
│       └── StatusTab.tsx                 # 系统状态标签页
└── services/knowledgeBase.ts             # API服务
```

### API接口
- `GET /api/admin/knowledge-base/overview` - 获取概览统计
- `GET /api/admin/knowledge-base/documents` - 获取文档列表
- `POST /api/admin/knowledge-base/query` - 查询知识库
- `GET /api/admin/knowledge-base/statistics` - 获取统计分析
- `GET /api/admin/knowledge-base/status` - 获取系统状态
- `GET /api/admin/knowledge-base/filters` - 获取筛选选项
- `GET /api/admin/knowledge-base/health` - 健康检查

## 📋 依赖要求

### 后端依赖
- FastAPI >= 0.104.1
- pandas >= 2.1.4
- chromadb >= 0.4.18
- sentence-transformers >= 2.2.2

### 前端依赖
- React >= 18.0.0
- Ant Design >= 5.0.0
- TypeScript >= 4.9.0

## 🔍 故障排除

### 常见问题

#### 1. ChromaDB连接失败
**症状**：系统状态显示"ChromaDB连接异常"
**解决方案**：
- 检查知识库配置文件路径
- 确认ChromaDB数据库文件存在
- 验证嵌入模型是否正确安装

#### 2. 依赖安装失败
**症状**：启动时报模块导入错误
**解决方案**：
```bash
# 重新安装依赖
cd admin-backend
pip install -r requirements.txt --force-reinstall
```

#### 3. 前端页面无法加载
**症状**：知识库管理页面显示空白或错误
**解决方案**：
- 检查后端服务是否正常运行
- 确认API接口返回正常
- 查看浏览器控制台错误信息

### 测试验证
```bash
# 运行知识库服务测试
cd admin-backend
python3 test_knowledge_base.py
```

## 📈 性能优化建议

1. **数据分页**：大量文档时使用分页加载
2. **缓存策略**：对统计数据进行适当缓存
3. **查询优化**：合理设置相似度阈值和返回数量
4. **资源监控**：定期检查ChromaDB性能

## 🔄 更新日志

### v1.0.0 (当前版本)
- ✅ 完成知识库管理功能整合
- ✅ 实现概览统计、文档管理、查询测试功能
- ✅ 添加统计分析和系统状态监控
- ✅ 集成到统一后台管理系统
- ✅ 提供完整的API接口和前端界面

## 📞 技术支持

如遇到问题，请：
1. 查看系统状态页面的详细信息
2. 运行测试脚本进行诊断
3. 检查日志文件获取错误详情
4. 参考故障排除指南

---

**注意**：知识库管理功能依赖现有的ChromaDB数据库和配置，请确保相关服务正常运行。
