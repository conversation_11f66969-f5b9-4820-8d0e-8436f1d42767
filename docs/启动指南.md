# 系统启动指南

## 🚀 快速启动

### 主要服务启动

#### 1. 主系统（需求采集）
```bash
# 后端API服务
python run_api.py                    # 端口: 8000

# 前端用户界面
cd frontend && npm run dev           # 端口: 5173
```

#### 2. 管理后台（含知识库管理）
```bash
# 一键启动管理后台
./start_admin.sh

# 或手动启动
cd admin-backend && python main.py  # 端口: 8002
cd admin-frontend && npm start      # 端口: 3000
```

## 🌐 访问地址

### 用户界面
- **主应用**: http://localhost:5173
- **API文档**: http://localhost:8000/docs

### 管理界面
- **管理后台**: http://localhost:3000
- **管理API**: http://localhost:8002/docs
- **健康检查**: http://localhost:8002/health

## 📋 端口说明

| 服务 | 端口 | 用途 |
|------|------|------|
| 主API | 8000 | 需求采集系统后端 |
| 用户前端 | 5173 | 用户交互界面 |
| 管理API | 8002 | 后台管理接口 |
| 管理前端 | 3000 | 管理员界面 |

## 🔧 环境配置

### 必需配置
```bash
# 1. 复制环境变量模板
cp .env.example .env

# 2. 编辑配置文件，添加API密钥
# DEEPSEEK_API_KEY=your-key-here
# DOUBAO_API_KEY=your-key-here
# QWEN_API_KEY=your-key-here
```

### 依赖安装
```bash
# Python依赖
pip install -r requirements.txt

# 前端依赖
cd frontend && npm install
cd ../admin-frontend && npm install
```

## 🛠️ 开发模式

### 开发环境启动
```bash
# 后端开发模式（自动重载）
uvicorn backend.api.main:app --reload --port 8000

# 前端开发模式（热更新）
cd frontend && npm run dev
```

### 调试模式
```bash
# 启用调试日志
export DEBUG=true
export LOG_LEVEL=DEBUG

# 启动服务
python run_api.py
```

### 测试和覆盖率
```bash
# 运行测试
python -m pytest tests/

# 生成覆盖率报告
python -m pytest --cov=backend --cov-report=html --cov-report=term

# 查看HTML覆盖率报告
open htmlcov/index.html  # macOS
# 或在浏览器中打开 htmlcov/index.html
```

## 🚨 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查看端口占用
   lsof -i :8000
   lsof -i :5173
   
   # 杀死占用进程
   kill -9 <PID>
   ```

2. **依赖问题**
   ```bash
   # 重新安装Python依赖
   pip install -r requirements.txt --force-reinstall
   
   # 重新安装Node依赖
   cd frontend && rm -rf node_modules && npm install
   ```

3. **环境变量未生效**
   ```bash
   # 检查.env文件是否存在
   ls -la .env
   
   # 重启服务使配置生效
   ```

4. **数据库问题**
   ```bash
   # 检查数据库文件
   ls -la backend/data/aidatabase.db
   
   # 重新创建数据库表
   python backend/scripts/create_tables.py
   ```

## 📚 相关文档

- [开发者快速参考卡片](./开发者快速参考卡片.md)
- [配置管理指南](./development/配置管理指南.md)
- [知识库管理功能说明](./知识库管理功能说明.md)
- [项目架构文档](./项目架构和模块关系文档.md)
