# 管理后台系统文档

本目录包含管理后台系统的需求、设计和实施文档。

## 📋 文档列表

### 📋 需求规划
- **[管理后台系统总体规划](./admin-system-master-plan.md)** - 管理后台的整体规划和架构
  - 系统目标和定位
  - 功能模块划分
  - 技术架构选型

- **[管理后台需求文档](./admin-dashboard-requirements.md)** - 详细的功能需求说明
  - 用户角色定义
  - 功能需求清单
  - 非功能性需求

### 🔧 技术设计
- **[管理后台API设计](./admin-api-design.md)** - 后端API接口设计
  - 接口规范
  - 数据模型
  - 安全机制

- **[管理前端架构](./admin-frontend-architecture.md)** - 前端技术架构设计
  - 技术栈选择
  - 组件设计
  - 状态管理

### 📚 功能模块
- **[知识库管理功能说明](../知识库管理功能说明.md)** - 知识库管理完整指南 🆕
  - 概览统计和数据分析
  - 文档管理和搜索功能
  - 智能查询测试工具
  - 统计分析和可视化
  - 系统状态监控
  - 使用方法和故障排除

## 🎯 系统概览

### 核心功能
- **配置管理**: LLM配置、场景参数、业务规则管理
- **模板管理**: 消息模板的增删改查和版本控制
- **知识库管理**: 文档管理、智能查询、统计分析、系统监控 🆕
- **数据库维护**: 数据备份、恢复、清理功能
- **系统监控**: 性能监控、日志查看、健康检查

### 技术特点
- **前后端分离**: React + FastAPI架构
- **权限控制**: 基于角色的访问控制
- **实时更新**: WebSocket实时数据推送
- **响应式设计**: 支持多设备访问

## 👥 用户角色

### 系统管理员
- **权限**: 完整的系统管理权限
- **职责**: 系统配置、用户管理、安全维护
- **工具**: 完整的管理界面和工具

### 配置管理员
- **权限**: 配置相关的管理权限
- **职责**: LLM配置、业务规则、模板管理
- **工具**: 配置管理界面和批量操作工具

### 运维人员
- **权限**: 监控和维护权限
- **职责**: 系统监控、日志分析、性能优化
- **工具**: 监控面板和分析工具

## 🚀 部署指南

### 开发环境
1. **后端启动**: `python admin-backend/main.py`
2. **前端启动**: `cd admin-frontend && npm start`
3. **访问地址**: http://localhost:3000

### 生产环境
1. **后端部署**: 使用Docker容器化部署
2. **前端部署**: 构建静态文件部署到CDN
3. **反向代理**: 使用Nginx进行反向代理

### 配置要求
- **Python**: 3.11+
- **Node.js**: 18+
- **数据库**: SQLite/PostgreSQL
- **内存**: 最低2GB

## 🔧 开发指南

### 后端开发
- **框架**: FastAPI
- **数据库**: SQLAlchemy ORM
- **认证**: JWT Token
- **文档**: 自动生成API文档

### 前端开发
- **框架**: React 18
- **UI库**: Ant Design
- **状态管理**: Redux Toolkit
- **路由**: React Router

### 代码规范
- **后端**: 遵循PEP 8规范
- **前端**: 使用ESLint和Prettier
- **提交**: 遵循Conventional Commits

## 📊 监控和维护

### 性能监控
- **响应时间**: API响应时间监控
- **错误率**: 系统错误率统计
- **资源使用**: CPU、内存使用监控

### 日志管理
- **访问日志**: 用户访问记录
- **错误日志**: 系统错误记录
- **操作日志**: 管理操作审计

### 备份策略
- **数据备份**: 定期数据库备份
- **配置备份**: 配置文件版本控制
- **代码备份**: Git版本管理

## 🔄 文档维护

- **更新频率**: 功能变更时及时更新
- **维护责任**: 管理后台开发团队
- **版本同步**: 与代码版本保持同步

---

**最后更新**: 2025年7月30日
