# 知识库管理快速参考

## 🚀 快速启动

### 启动知识库管理系统
```bash
# 方法一：使用启动脚本（推荐）
./start_admin.sh

# 方法二：手动启动
cd admin-backend && python3 main.py    # 后端服务 :8002
cd admin-frontend && npm start         # 前端服务 :3000
```

### 访问地址
- **管理界面**: http://localhost:3000
- **API文档**: http://localhost:8002/docs
- **健康检查**: http://localhost:8002/api/admin/knowledge-base/health

## 📊 核心功能

### 1. 概览统计
- 📈 文档数量统计（总数、唯一数）
- 👥 角色分布分析（company、developer、general）
- 🏷️ 分类分布统计
- 📊 可视化图表展示

### 2. 文档管理
- 📄 分页浏览所有文档
- 🔍 全文搜索功能
- 🏷️ 按角色/分类筛选
- 👁️ 文档详情查看

### 3. 查询测试
- 🤖 智能知识库查询
- ⚙️ 参数调节（Top-K、相似度阈值）
- 🎯 角色筛选查询
- 📋 查询结果分析

### 4. 统计分析
- 📏 内容长度分布分析
- 📊 角色/分类统计表格
- 🔄 角色-分类交叉分析
- 📈 详细统计指标

### 5. 系统状态
- 🔗 ChromaDB连接状态
- ⚙️ 配置信息展示
- 🧩 组件状态监控
- ❤️ 系统健康检查

## 🔌 API接口

### 核心接口
```bash
# 概览统计
GET /api/admin/knowledge-base/overview

# 文档列表（支持分页和筛选）
GET /api/admin/knowledge-base/documents?page=1&limit=20&role=developer

# 知识库查询
POST /api/admin/knowledge-base/query
{
  "query_text": "如何集成API",
  "top_k": 5,
  "similarity_threshold": 0.7,
  "role_filter": "developer"
}

# 统计分析
GET /api/admin/knowledge-base/statistics

# 系统状态
GET /api/admin/knowledge-base/status

# 健康检查
GET /api/admin/knowledge-base/health
```

### 筛选选项
```bash
# 获取可用的角色和分类选项
GET /api/admin/knowledge-base/filters
```

## 🧪 测试和调试

### 快速测试
```bash
# 健康检查
curl -X GET "http://localhost:8002/api/admin/knowledge-base/health"

# 概览统计
curl -X GET "http://localhost:8002/api/admin/knowledge-base/overview"

# 查询测试
curl -X POST "http://localhost:8002/api/admin/knowledge-base/query" \
  -H "Content-Type: application/json" \
  -d '{"query_text":"API集成","top_k":3,"similarity_threshold":0.0}'
```

### 添加示例数据
```bash
cd admin-backend
python3 add_sample_documents.py
```

### 服务测试
```bash
cd admin-backend
python3 test_knowledge_base.py
```

## 📁 关键文件

| 文件路径 | 用途 | 说明 |
|---------|------|------|
| `admin-backend/admin_services/knowledge_base_service.py` | 核心服务 | 知识库管理业务逻辑 |
| `admin-backend/admin_api/routers/knowledge_base.py` | API路由 | RESTful接口定义 |
| `admin-frontend/src/pages/KnowledgeBase/` | 前端页面 | 管理界面组件 |
| `admin-frontend/src/services/knowledgeBase.ts` | 前端服务 | API调用封装 |
| `admin-backend/test_knowledge_base.py` | 测试脚本 | 功能验证 |
| `admin-backend/add_sample_documents.py` | 示例数据 | 添加测试文档 |

## 🔧 配置说明

### ChromaDB配置
```yaml
# backend/config/knowledge_base_config.yaml
chroma_db:
  path: "backend/data/chroma_db"
  collection_name: "hybrid_knowledge_base"
  embedding_model: "moka-ai/m3e-base"
```

### 依赖要求
```txt
# admin-backend/requirements.txt
pandas>=2.1.4
chromadb>=0.4.18
sentence-transformers>=2.2.2
```

## 🐛 故障排除

### 常见问题

#### 1. ChromaDB连接失败
**症状**: 系统状态显示"ChromaDB连接异常"
```bash
# 检查数据库路径
ls -la backend/data/chroma_db/

# 重新初始化
cd admin-backend && python3 test_knowledge_base.py
```

#### 2. 查询结果为空
**症状**: 查询测试返回空结果
```bash
# 降低相似度阈值
curl -X POST "http://localhost:8002/api/admin/knowledge-base/query" \
  -d '{"query_text":"测试","similarity_threshold":0.0}'

# 检查文档数量
curl -X GET "http://localhost:8002/api/admin/knowledge-base/overview"
```

#### 3. 前端页面报错
**症状**: 统计分析页面显示错误
```bash
# 检查后端服务状态
curl -X GET "http://localhost:8002/api/admin/knowledge-base/health"

# 重新构建前端
cd admin-frontend && npm run build
```

#### 4. 依赖安装失败
```bash
# 重新安装依赖
cd admin-backend
pip install -r requirements.txt --force-reinstall

# 检查Python版本（需要3.8+）
python3 --version
```

## 📊 数据格式

### 文档元数据结构
```json
{
  "doc_id": "doc_001",
  "title": "文档标题",
  "role": "company|developer|general",
  "category": "分类名称",
  "source_path": "文件路径",
  "chunk_index": 0,
  "created_at": "2025-08-14T10:00:00",
  "updated_at": "2025-08-14T10:00:00",
  "version": 1
}
```

### 查询响应格式
```json
{
  "query": "查询内容",
  "results": [
    {
      "rank": 1,
      "content": "文档内容",
      "content_preview": "内容预览...",
      "similarity_score": 0.85,
      "metadata": {
        "title": "文档标题",
        "role": "developer",
        "category": "API文档"
      }
    }
  ],
  "total_results": 3,
  "parameters": {
    "top_k": 5,
    "similarity_threshold": 0.7
  }
}
```

## 📚 相关文档

- [知识库管理功能说明](知识库管理功能说明.md) - 完整使用指南
- [后台管理系统总体规划](admin/admin-system-master-plan.md) - 系统架构
- [开发者快速参考卡片](开发者快速参考卡片.md) - 开发指南

## 💡 使用技巧

### 1. 查询优化
- 使用具体的关键词而非泛泛的描述
- 适当调整相似度阈值（0.3-0.8）
- 利用角色筛选缩小搜索范围

### 2. 文档管理
- 使用搜索功能快速定位文档
- 利用筛选功能按需查看
- 查看文档详情了解完整信息

### 3. 系统监控
- 定期检查系统状态页面
- 关注ChromaDB连接状态
- 监控文档数量变化

### 4. 性能优化
- 合理设置查询参数
- 避免过于频繁的API调用
- 定期清理无用数据
