# 项目文档总览

本目录包含了项目的所有文档，按照功能和用途进行了系统性的组织。

## 🎯 核心文档（必读）

### 📋 [项目架构和模块关系文档](项目架构和模块关系文档.md)
**⭐ 重要程度：★★★★★**

包含完整的项目结构、模块说明和调用关系：
- 🏗️ 详细的目录结构说明
- 🔧 核心模块功能详解
- 🔄 模块间调用关系图
- 📊 依赖关系矩阵
- 🚀 系统启动顺序
- 🔧 开发维护指南

**适用人群**: 所有开发者（新人必读）

### 🏗️ [系统架构图](architecture/系统架构图.md)
**⭐ 重要程度：★★★★★**

可视化的系统架构说明：
- 🔄 整体系统架构图
- 📊 核心业务流程图
- 🏛️ 分层架构详解
- 🔧 模块依赖关系图
- 📈 数据流架构图
- 🔐 安全和部署架构

**适用人群**: 架构师、高级开发者

### 🔌 [模块接口和调用规范](development/模块接口和调用规范.md)
**⭐ 重要程度：★★★★☆**

详细的模块接口说明：
- 🔧 核心模块接口定义
- 🔄 模块间调用规范
- 📊 数据格式规范
- 🔌 集成接口规范
- 🔍 调试和测试接口
- ⚠️ 最佳实践指南

**适用人群**: 开发者、集成工程师

### 🚀 [Agent实例池优化系统](development/tools/agent-instance-pool.md) 🆕
**⭐ 重要程度：★★★★☆**

系统性能优化的核心组件：
- 🏊 会话级Agent实例缓存
- 🔄 组件实例复用机制
- ⚙️ 配置驱动的缓存管理
- 📊 详细的性能监控统计
- 🧵 线程安全的并发处理
- 💾 智能的内存管理

**适用人群**: 性能优化工程师、运维人员

## 📁 目录结构

### 🏗️ architecture/ - 系统架构文档
- 系统整体架构设计
- 统一决策引擎架构
- 核心组件设计文档

### 💻 development/ - 开发相关文档
- **standards/** - 开发规范和标准
- **guides/** - 开发指南和最佳实践
- **configuration/** - 配置相关文档
- **tools/** - 开发工具文档
  - `agent-instance-pool.md` - Agent实例池优化系统 🆕
  - `performance-monitoring.md` - 性能监控使用指南

### 🚀 projects/ - 项目实施文档
- **hardcode-elimination/** - 硬编码消除项目
- **intent-management/** - 意图管理统一化项目
- **keyword-config/** - 关键词配置重构项目

### 🔧 admin/ - 管理系统文档
- 后台管理系统设计
- API文档和前端架构

### 📚 [知识库管理功能说明](知识库管理功能说明.md) 🆕
**⭐ 重要程度：★★★★☆**

完整的知识库管理系统使用指南：
- 📊 概览统计和数据分析
- 📄 文档管理和搜索功能
- 🔍 智能查询测试工具
- 📈 统计分析和可视化
- 🔧 系统状态监控
- 🚀 使用方法和故障排除

**适用人群**: 管理员、运维人员、产品经理

### ⚡ [知识库管理快速参考](知识库管理快速参考.md) 🆕
**⭐ 重要程度：★★★★★**

知识库管理系统的快速参考卡片：
- 🚀 快速启动命令
- 🔌 核心API接口
- 🧪 测试和调试方法
- 🐛 故障排除指南
- 💡 使用技巧和优化建议

**适用人群**: 开发者、测试人员、运维人员

### 📦 archive/ - 归档文档
- **completed-projects/** - 已完成项目的临时文档
- **analysis-reports/** - 历史分析报告
- **legacy/** - 历史遗留文档

## 🔍 快速导航

### 新开发者入门
1. [开发规范和标准](development/standards/)
2. [开发指南](development/guides/)
3. [系统架构概览](architecture/)

### 配置管理
1. [配置化开发规范](development/standards/配置化开发规范.md)
2. [配置相关文档](development/configuration/)

### 项目历史
1. [硬编码消除项目](projects/hardcode-elimination/)
2. [意图管理统一化](projects/intent-management/)
3. [关键词配置重构](projects/keyword-config/)

## 📋 文档维护

- 文档整理时间: 2025-08-10 18:30:00
- 整理原则: 按功能分类，便于查找和维护
- 更新频率: 随项目开发进度更新

---

💡 **提示**: 如果找不到特定文档，可以查看 [archive/](archive/) 目录中的归档文档。
