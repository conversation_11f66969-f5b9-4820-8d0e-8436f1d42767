# 更新日志

## [v3.4.0] - 2025-08-15

### ✨ 新功能 (Features)

- **新增决策日志系统**:
  - 创建了一个全新的、人类易读的决策日志 `logs/decision_trace.log`。
  - 该日志清晰地记录了AI处理每个请求时的完整决策链，包括：用户输入、意图识别、领域分类、信息提取、追问生成、最终回复等。
  - 每个步骤都包含耗时、使用的AI模型、置信度等关键元数据，极大地提升了AI行为的可观测性和可调试性。
  - 通过在 `backend/utils/logging_config.py` 中添加独立的 `decision_logger` 实现，与现有日志系统解耦，性能影响小。

### 🐛 修复 (Bug Fixes)

- **修复**: 解决了 `intent_classification_llm` 中因LLM返回额外文本而导致的JSON解析失败问题，通过增强解析逻辑，使其能从脏数据中稳定提取JSON。
- **修复**: 修复了在实现决策日志时，因方法名错误 (`AttributeError`) 导致的“信息提取”流程中断问题。
- **修复**: 解决了在开发服务器热重载环境下，新增的日志处理器无法被正确加载的问题。

## [v3.3.0] - 2025-08-14

### 🚀 知识库管理功能整合

#### 统一后台管理系统集成
- **✅ 完整功能整合**: 将原有Streamlit知识库仪表板完全整合到统一后台管理系统
- **✅ 五大核心模块**: 概览统计、文档管理、查询测试、统计分析、系统状态
- **✅ 响应式设计**: 与现有后台管理保持一致的设计风格和用户体验
- **✅ 实时监控**: ChromaDB连接状态、组件健康检查、配置信息展示

#### 后端服务架构
- **新增服务**: `KnowledgeBaseService` - 知识库管理核心业务逻辑
- **新增路由**: `/api/admin/knowledge-base/*` - 完整的RESTful API接口
- **智能集合管理**: 自动创建ChromaDB集合，处理连接异常
- **数据验证**: 完整的请求参数验证和错误处理机制

#### 前端界面功能
- **概览统计**: 文档数量统计、角色分布图表、分类分布可视化
- **文档管理**: 分页浏览、高级筛选、全文搜索、文档详情查看
- **查询测试**: 智能查询、参数调节、相似度分析、结果展示
- **统计分析**: 内容长度分析、角色分类统计、交叉分析矩阵
- **系统状态**: 实时状态监控、配置信息、组件状态检查

#### 示例数据和测试
- **示例文档**: 添加8个涵盖不同角色和分类的示例文档
- **测试脚本**: `test_knowledge_base.py` - 完整的功能验证脚本
- **启动脚本**: `start_admin.sh` - 一键启动脚本

### 📚 文档更新
- **功能说明**: 新增 `docs/知识库管理功能说明.md` 完整使用指南
- **API文档**: 更新后台管理API文档，包含知识库管理接口
- **故障排除**: 详细的问题诊断和解决方案

## [v3.2.0] - 2025-08-14

### 🚀 重大性能优化

#### Agent实例池系统
- **新增组件**: `AgentInstancePool` - 会话级Agent实例缓存管理器
- **缓存机制**: 实现会话级Agent实例复用，避免重复创建
- **组件优化**: 优化DynamicReplyGenerator等组件的依赖注入
- **配置驱动**: 完全配置化的缓存策略和生命周期管理
- **线程安全**: 支持多用户并发访问的线程安全设计

#### 性能监控扩展
- **实例池统计**: 新增 `/api/system/agent-pool-stats` API接口
- **缓存指标**: 缓存命中率、实例复用率等详细统计
- **内存管理**: 实时监控内存使用和自动清理机制
- **并发监控**: 跟踪同时处理的请求数量和峰值

### 🔧 系统优化

#### 重复初始化问题解决
- **✅ 减少组件重复创建**: MessageReplyManager、DynamicReplyGenerator等组件复用
- **✅ 配置文件缓存**: 避免重复读取配置文件
- **✅ 依赖注入优化**: 通过依赖注入减少重复初始化

#### 配置系统增强
- **Agent缓存配置**: 在 `unified_config.yaml` 中新增 `agent_cache` 配置节
- **生命周期管理**: 可配置的会话超时、缓存容量、清理间隔
- **内存控制**: 可配置的内存使用限制和监控间隔
- **监控开关**: 可配置的性能统计和报告功能

### 📊 性能提升

#### 响应时间优化
- **Agent创建**: 从每次创建(~0.12秒)减少到缓存命中(毫秒级)
- **缓存命中率**: 实测达到50%以上的缓存命中率
- **内存使用**: 显著减少重复对象创建的内存占用

#### 并发处理能力
- **多用户支持**: 线程安全的并发访问设计
- **会话隔离**: 用户间数据完全隔离，确保安全性
- **负载均衡**: 智能的缓存清理和内存管理

## [v3.1.0] - 2025-08-13

### 🆕 新增功能

#### 结构化意图分类系统
- **新增组件**: `IntentClassificationLLM` - 专门的结构化意图分类器
- **架构升级**: `SimplifiedDecisionEngine` 升级为混合架构
- **模板系统**: 新增 `structured_intent_classification.md` 提示词模板
- **智能调度**: 根据复杂度自动选择最佳处理方式
- **复合意图支持**: 精确识别和处理复合意图

#### 配置系统扩展
- **功能开关**: `system.use_structured_classification` 控制新功能启用
- **LLM配置**: 新增结构化分类专用模型配置
- **参数调优**: 专门的温度、令牌数等参数配置
- **阈值管理**: 结构化分类置信度阈值配置

### 🔧 改进优化

#### 意图识别准确性
- **✅ 解决价格咨询误判**: "我对设计这张海报具体需要多少钱没有概念，您能给我建议吗？" 现在正确识别为 `business_requirement`
- **✅ 复合意图精确识别**: 能够区分真正的复合意图和单一意图的多种表述
- **✅ 语义理解增强**: 从关键词依赖升级为语义理解

#### 系统稳定性
- **回退机制**: LLM失败时自动回退到传统关键词匹配
- **断路器保护**: 防止LLM服务异常影响系统稳定性
- **错误处理**: 完善的异常处理和日志记录
- **向后兼容**: 保持原有API接口不变

### 📊 性能指标

| 指标 | 目标值 | 实际效果 |
|------|--------|----------|
| 意图识别准确率 | > 85% | 显著提升 |
| 复合意图识别率 | > 80% | 新增能力 |
| 价格咨询误判率 | < 5% | 大幅降低 |
| 平均响应时间 | < 3秒 | 基本保持 |
| 系统可用性 | > 99% | 稳定可靠 |

### 🏗️ 架构变更

#### 新增文件
```
backend/agents/intent_classification_llm.py    # 结构化意图分类器
backend/prompts/structured_intent_classification.md  # 分类模板
docs/architecture/结构化意图分类架构.md        # 架构文档
docs/development/结构化意图分类实施记录.md      # 实施记录
```

#### 修改文件
```
backend/agents/simplified_decision_engine.py   # 集成混合架构
backend/config/unified_config.yaml            # 扩展配置支持
tests/price_intent_test.py                    # 增强测试用例
```

#### 更新文档
```
docs/architecture/统一决策引擎架构设计.md      # 反映最新架构
docs/项目架构和模块关系文档.md                # 添加新组件说明
docs/开发者快速参考卡片.md                   # 更新使用示例
docs/development/guides/统一决策引擎用户指南.md # 添加新功能说明
```

### 🔄 迁移指南

#### 对于开发者
1. **无需修改现有代码** - 保持向后兼容
2. **推荐使用新API** - `get_simplified_decision_engine()` 获得更好性能
3. **配置新功能** - 设置 `system.use_structured_classification: true`

#### 对于运维人员
1. **配置API密钥** - 确保LLM服务正常工作
2. **监控新指标** - 关注结构化分类成功率和回退率
3. **调整参数** - 根据实际使用情况优化配置

### 🐛 修复问题

- **修复**: 价格咨询被误识别为复合意图的问题
- **修复**: 关键词匹配无法覆盖所有表达方式的问题
- **修复**: 复合意图识别不准确的问题
- **修复**: 意图识别缺乏语义理解的问题

### 📝 技术债务

- **重构**: 意图识别架构从关键词依赖升级为语义理解
- **优化**: 决策引擎性能和准确性
- **标准化**: 意图分类输出格式和处理流程
- **文档化**: 完善架构文档和使用指南

### 🔮 后续计划

#### 短期优化 (1-2周)
- 根据实际使用数据调整模板和参数
- 优化LLM调用策略降低成本
- 增加更多测试用例验证准确性

#### 中期规划 (1-2月)
- 支持多语言意图识别
- 实现意图识别结果缓存
- 添加用户反馈学习机制

#### 长期愿景 (3-6月)
- 基于用户历史的个性化意图识别
- 实时模型微调和优化
- 跨领域意图识别能力扩展

---

## [v3.0.0] - 2025-07-20

### 🏗️ 重大架构升级
- 统一配置系统实施
- 决策引擎重构
- 模块化架构优化

### 🆕 新增功能
- 配置监控系统
- 性能监控工具
- 日志系统优化

---

## [v2.x.x] - 历史版本

详细的历史版本信息请参考 `docs/archive/` 目录下的相关文档。

---

**注意**: 本更新日志遵循 [Keep a Changelog](https://keepachangelog.com/) 规范。