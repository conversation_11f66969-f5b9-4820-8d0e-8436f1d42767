# 硬编码预防开发指南

## 概述

本指南旨在帮助开发者在日常开发中避免引入硬编码问题，建立良好的配置化开发习惯。

## 🎯 核心原则

### 1. 配置优先原则
- **所有可变的值都应该通过配置管理**
- **代码中不应出现业务相关的字面量**
- **为所有配置提供合理的默认值**

### 2. 分层配置原则
```python
# 业务规则 - 影响业务逻辑的配置
priority = config_manager.get_business_rule("priority.document_modification", 7)

# 技术阈值 - 影响算法和性能的配置
threshold = config_manager.get_threshold("extraction.completeness_threshold", 0.7)

# 消息模板 - 用户界面相关的文本
message = config_manager.get_message_template("error.general.unknown_error")

# 数据库查询 - SQL语句模板
query = config_manager.get_database_query("focus_points.check_exists")
```

### 3. 默认值安全原则
- **总是提供默认值确保系统稳定运行**
- **默认值应该是安全和合理的**
- **在配置缺失时系统应该能够正常工作**

## 🛠️ 开发工作流

### 1. 开发前检查
```bash
# 运行硬编码检测
python tools/hardcode_detector.py backend/

# 验证配置文件
python tools/config_validator.py
```

### 2. 开发中实践

#### ✅ 正确的做法
```python
# 使用配置管理器
def process_request(self, request):
    timeout = config_manager.get_threshold("request.timeout", 30)
    max_retries = config_manager.get_business_rule("retry.max_attempts", 3)
    
    for attempt in range(max_retries):
        try:
            response = self.call_api(request, timeout=timeout)
            return response
        except TimeoutError:
            if attempt == max_retries - 1:
                error_msg = config_manager.get_message_template("error.timeout")
                raise Exception(error_msg)
```

#### ❌ 错误的做法
```python
# 硬编码数值和消息
def process_request(self, request):
    timeout = 30  # ❌ 硬编码超时时间
    max_retries = 3  # ❌ 硬编码重试次数
    
    for attempt in range(max_retries):
        try:
            response = self.call_api(request, timeout=timeout)
            return response
        except TimeoutError:
            if attempt == max_retries - 1:
                raise Exception("请求超时，请稍后重试")  # ❌ 硬编码错误消息
```

### 3. 提交前检查
```bash
# 运行 pre-commit hooks
pre-commit run --all-files

# 手动检查关键文件
python tools/hardcode_detector.py backend/agents/conversation_flow.py
```

## 📝 配置文件管理

### 1. 添加新配置

#### 步骤1：确定配置类型
- **业务规则**: 影响业务逻辑的配置 → `business_rules.yaml`
- **技术阈值**: 算法参数、性能参数 → `settings.py` (BUSINESS_THRESHOLDS)
- **消息模板**: 用户界面文本 → `unified_config.yaml` (message_templates 部分)
- **数据库查询**: SQL语句模板 → `database_queries.yaml`

#### 步骤2：添加配置项
```yaml
# business_rules.yaml
retry:
  max_attempts: 3
  backoff_factor: 2.0
```

```python
# settings.py - BUSINESS_THRESHOLDS
BUSINESS_THRESHOLDS = {
    "performance": {
        "response_time_threshold": 5.0,
        "memory_usage_threshold": 0.8
    }
}
```

#### 步骤3：在代码中使用
```python
max_attempts = config_manager.get_business_rule("retry.max_attempts", 3)
response_threshold = config_manager.get_threshold("performance.response_time_threshold", 5.0)
```

### 2. 配置命名规范

#### 层次化命名
```yaml
# 使用点号分隔的层次结构
llm:
  temperature:
    default: 0.7
    creative: 0.9
    conservative: 0.3
  
# 对应的访问方式
config_manager.get_threshold("llm.temperature.default", 0.7)
```

#### 语义化命名
```yaml
# ✅ 好的命名
extraction:
  completeness_threshold: 0.7
  confidence_threshold: 0.8

# ❌ 不好的命名
values:
  val1: 0.7
  val2: 0.8
```

## 🔧 常见场景处理

### 1. 错误消息处理
```python
# ✅ 正确方式
def handle_error(self, error_type: str):
    if error_type == "timeout":
        return config_manager.get_message_template("error.timeout")
    elif error_type == "network":
        return config_manager.get_message_template("error.network")
    else:
        return config_manager.get_message_template("error.general.unknown_error")

# ❌ 错误方式
def handle_error(self, error_type: str):
    if error_type == "timeout":
        return "请求超时，请稍后重试"
    elif error_type == "network":
        return "网络连接失败"
    else:
        return "发生未知错误"
```

### 2. 数据库查询处理
```python
# ✅ 正确方式
def get_user_by_id(self, user_id: str):
    query = config_manager.get_database_query("users.get_by_id")
    return self.db.execute(query, (user_id,))

# ❌ 错误方式
def get_user_by_id(self, user_id: str):
    query = "SELECT * FROM users WHERE id = ?"
    return self.db.execute(query, (user_id,))
```

### 3. LLM参数处理
```python
# ✅ 正确方式
def call_llm(self, prompt: str, mode: str = "default"):
    temperature = config_manager.get_threshold(f"llm.temperature.{mode}", 0.7)
    max_tokens = config_manager.get_threshold(f"llm.max_tokens.{mode}", 1000)
    
    return self.llm_client.generate(
        prompt=prompt,
        temperature=temperature,
        max_tokens=max_tokens
    )

# ❌ 错误方式
def call_llm(self, prompt: str, mode: str = "default"):
    if mode == "creative":
        temperature = 0.9
        max_tokens = 1500
    else:
        temperature = 0.7
        max_tokens = 1000
    
    return self.llm_client.generate(
        prompt=prompt,
        temperature=temperature,
        max_tokens=max_tokens
    )
```

## 🚨 常见陷阱和解决方案

### 1. 临时硬编码
**问题**: 开发过程中为了快速测试而添加的临时硬编码
```python
# ❌ 临时硬编码
def test_function():
    threshold = 0.8  # TODO: 临时值，稍后配置化
    return process_data(threshold)
```

**解决方案**: 立即使用配置管理器，即使是临时值
```python
# ✅ 立即配置化
def test_function():
    threshold = config_manager.get_threshold("test.threshold", 0.8)
    return process_data(threshold)
```

### 2. 条件硬编码
**问题**: 在条件判断中使用硬编码值
```python
# ❌ 条件硬编码
if user.role == "admin":  # 硬编码角色名
    return admin_view()
```

**解决方案**: 使用配置定义角色
```python
# ✅ 配置化角色
admin_role = config_manager.get_business_rule("roles.admin", "admin")
if user.role == admin_role:
    return admin_view()
```

### 3. 循环硬编码
**问题**: 在循环中使用硬编码限制
```python
# ❌ 循环硬编码
for i in range(10):  # 硬编码循环次数
    if process_item(items[i]):
        break
```

**解决方案**: 使用配置定义限制
```python
# ✅ 配置化限制
max_items = config_manager.get_business_rule("processing.max_items", 10)
for i in range(max_items):
    if process_item(items[i]):
        break
```

## 📊 质量监控

### 1. 定期检查
```bash
# 每周运行完整检查
python tools/hardcode_detector.py backend/ > weekly_hardcode_report.txt

# 检查新增的硬编码
git diff HEAD~1 --name-only | grep "\.py$" | xargs python tools/hardcode_detector.py
```

### 2. 指标跟踪
- **硬编码密度**: 每1000行代码中的硬编码问题数量
- **配置覆盖率**: 使用配置管理器的比例
- **修复时间**: 发现问题到修复的平均时间

### 3. 团队培训
- 定期分享硬编码检查结果
- 组织配置化最佳实践培训
- 建立代码审查文化

## 🎓 最佳实践总结

1. **开发前思考**: 这个值将来可能会变化吗？
2. **配置优先**: 优先考虑使用配置而不是硬编码
3. **合理默认**: 提供安全合理的默认值
4. **语义命名**: 使用有意义的配置键名
5. **文档记录**: 为配置项添加注释和文档
6. **定期检查**: 使用工具定期检查硬编码问题
7. **团队协作**: 在代码审查中重点关注硬编码问题

---

## 🚀 工具安装和使用

### 1. 安装 Pre-commit Hooks
```bash
# 安装 pre-commit
pip install pre-commit

# 安装项目的 hooks
pre-commit install

# 手动运行所有检查
pre-commit run --all-files
```

### 2. 使用硬编码检测工具
```bash
# 检查单个文件
python tools/hardcode_detector.py backend/agents/conversation_flow.py

# 检查整个目录
python tools/hardcode_detector.py backend/

# 生成JSON格式报告
python tools/hardcode_detector.py backend/ json
```

### 3. 使用配置验证工具
```bash
# 验证所有配置文件
python tools/config_validator.py

# 指定配置目录
python tools/config_validator.py --config-dir backend/config/

# 严格模式（警告也视为错误）
python tools/config_validator.py --strict
```

### 4. CI/CD 集成
项目已配置 GitHub Actions 工作流，会在每次 Pull Request 时自动运行：
- 硬编码检测
- 配置文件验证
- 代码质量检查
- 集成测试

---

**记住**: 配置化是一种思维方式，不仅仅是技术实现。养成配置化思维，让代码更加灵活和可维护。
