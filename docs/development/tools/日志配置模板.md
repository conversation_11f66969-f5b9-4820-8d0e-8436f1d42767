# 模版日志配置功能

## 概述

现在可以通过日志配置一键控制所有模版内容的输出，无需在每个调用处单独设置 `log_content` 参数。

## 功能特性

### ✅ **全局配置控制**
- 通过 `configure_logging(template_logging_enabled=True/False)` 一键开启/关闭所有模版输出
- 默认启用模版日志输出

### ✅ **灵活覆盖机制**
- 全局配置作为默认值
- 可以在具体调用时通过 `log_content` 参数覆盖全局配置

### ✅ **自动应用到所有模版**
- `question_polisher` - 问题优化器模版
- `information_extraction` - 信息提取模版
- `domain_guidance` - 领域引导模版
- `clarification_question` - 追问生成模版

## 使用方法

### 1. **启用所有模版日志输出**
```python
from backend.config.logging_config import configure_logging

# 启用模版日志输出（默认）
configure_logging(template_logging_enabled=True)
```

### 2. **禁用所有模版日志输出**
```python
from backend.config.logging_config import configure_logging

# 禁用模版日志输出
configure_logging(template_logging_enabled=False)
```

### 3. **检查当前配置状态**
```python
from backend.utils.logging_config import is_template_logging_enabled

# 检查是否启用了模版日志
enabled = is_template_logging_enabled()
print(f"模版日志是否启用: {enabled}")
```

### 4. **覆盖全局配置**
```python
from backend.utils.prompt_loader import PromptLoader

loader = PromptLoader()

# 即使全局禁用，也强制输出这个模版
prompt = loader.load_prompt("question_polisher", variables, log_content=True)

# 即使全局启用，也不输出这个模版
prompt = loader.load_prompt("question_polisher", variables, log_content=False)

# 使用全局配置（推荐）
prompt = loader.load_prompt("question_polisher", variables)
```

## 日志输出格式

当启用模版日志时，输出格式如下：

```
[模板内容] {template_name} 替换占位符后的完整内容:
--------------------------------------------------
{完整的替换后模版内容}
--------------------------------------------------
```

## 配置建议

### **开发环境**
```python
# 启用模版日志，便于调试和优化
configure_logging(
    enable_console=True,
    enable_file=True,
    template_logging_enabled=True
)
```

### **生产环境**
```python
# 禁用模版日志，减少日志量
configure_logging(
    enable_console=True,
    enable_file=True,
    template_logging_enabled=False
)
```

### **调试特定问题**
```python
# 临时启用模版日志进行问题排查
configure_logging(template_logging_enabled=True)

# 调试完成后关闭
configure_logging(template_logging_enabled=False)
```

## 优势

1. **集中管理**: 一个配置控制所有模版输出
2. **便于调试**: 快速开启/关闭模版内容查看
3. **性能友好**: 生产环境可以完全关闭，减少日志量
4. **向下兼容**: 现有代码无需修改，自动使用全局配置
5. **灵活覆盖**: 特殊情况下可以单独控制某个模版的输出

## 实际应用场景

- **模版开发**: 启用日志查看模版替换效果
- **问题排查**: 当LLM回复异常时，查看实际发送的提示词
- **性能优化**: 生产环境关闭模版日志，减少I/O开销
- **A/B测试**: 对比不同模版版本的实际内容
