# Agent实例缓存配置指南

## 📋 概述

Agent实例缓存配置用于控制系统的Agent实例池行为，通过合理配置可以显著提升系统性能，减少重复初始化开销。

## 🔧 配置文件位置

配置位于 `backend/config/unified_config.yaml` 文件中的 `performance.agent_cache` 节点：

```yaml
performance:
  agent_cache:
    # 缓存策略配置
    enable_session_cache: true
    enable_component_cache: true
    
    # 生命周期管理
    session_timeout_minutes: 30
    max_cached_sessions: 100
    cleanup_interval_minutes: 5
    
    # 内存控制
    max_memory_mb: 500
    memory_check_interval: 10
    
    # 监控配置
    enable_metrics: true
    metrics_report_interval: 50
```

## 📊 配置参数详解

### 缓存策略配置

#### `enable_session_cache`
- **类型**: `boolean`
- **默认值**: `true`
- **说明**: 是否启用会话级Agent缓存
- **影响**: 关闭后每次请求都会创建新的Agent实例

```yaml
# 启用缓存（推荐）
enable_session_cache: true

# 禁用缓存（调试时使用）
enable_session_cache: false
```

#### `enable_component_cache`
- **类型**: `boolean`
- **默认值**: `true`
- **说明**: 是否启用组件级缓存复用
- **影响**: 关闭后组件（如DynamicReplyGenerator）不会复用

### 生命周期管理

#### `session_timeout_minutes`
- **类型**: `integer`
- **默认值**: `30`
- **单位**: 分钟
- **说明**: Agent实例在缓存中的超时时间
- **调优建议**:
  - 高频使用场景: `60-120` 分钟
  - 一般场景: `30-60` 分钟
  - 内存受限: `15-30` 分钟

```yaml
# 不同场景的配置示例
session_timeout_minutes: 15   # 内存受限环境
session_timeout_minutes: 30   # 标准配置
session_timeout_minutes: 60   # 高频使用场景
session_timeout_minutes: 120  # 长会话场景
```

#### `max_cached_sessions`
- **类型**: `integer`
- **默认值**: `100`
- **说明**: 最大同时缓存的会话数量
- **内存影响**: 每个缓存的Agent实例约占用5-10MB内存
- **调优建议**:
  - 高并发: `200-500`
  - 标准: `100-200`
  - 低内存: `50-100`

```yaml
# 不同规模的配置示例
max_cached_sessions: 50    # 小规模部署
max_cached_sessions: 100   # 标准配置
max_cached_sessions: 200   # 中等规模
max_cached_sessions: 500   # 大规模部署
```

#### `cleanup_interval_minutes`
- **类型**: `integer`
- **默认值**: `5`
- **单位**: 分钟
- **说明**: 后台清理任务的执行间隔
- **调优建议**:
  - 高频清理: `2-3` 分钟
  - 标准: `5-10` 分钟
  - 低频清理: `10-15` 分钟

### 内存控制

#### `max_memory_mb`
- **类型**: `integer`
- **默认值**: `500`
- **单位**: MB
- **说明**: Agent实例池的最大内存使用限制
- **注意**: 这是软限制，用于监控告警

#### `memory_check_interval`
- **类型**: `integer`
- **默认值**: `10`
- **说明**: 内存使用检查的频率（每N次请求检查一次）

### 监控配置

#### `enable_metrics`
- **类型**: `boolean`
- **默认值**: `true`
- **说明**: 是否启用详细的性能统计
- **影响**: 关闭后无法获取缓存命中率等统计信息

#### `metrics_report_interval`
- **类型**: `integer`
- **默认值**: `50`
- **说明**: 统计报告的输出间隔（每N次请求输出一次）

## 🎯 场景化配置示例

### 高并发生产环境
```yaml
performance:
  agent_cache:
    enable_session_cache: true
    enable_component_cache: true
    session_timeout_minutes: 60
    max_cached_sessions: 300
    cleanup_interval_minutes: 3
    max_memory_mb: 1000
    memory_check_interval: 5
    enable_metrics: true
    metrics_report_interval: 100
```

### 开发测试环境
```yaml
performance:
  agent_cache:
    enable_session_cache: true
    enable_component_cache: true
    session_timeout_minutes: 15
    max_cached_sessions: 50
    cleanup_interval_minutes: 2
    max_memory_mb: 200
    memory_check_interval: 5
    enable_metrics: true
    metrics_report_interval: 10
```

### 内存受限环境
```yaml
performance:
  agent_cache:
    enable_session_cache: true
    enable_component_cache: false
    session_timeout_minutes: 10
    max_cached_sessions: 30
    cleanup_interval_minutes: 1
    max_memory_mb: 100
    memory_check_interval: 3
    enable_metrics: false
    metrics_report_interval: 20
```

### 调试环境（禁用缓存）
```yaml
performance:
  agent_cache:
    enable_session_cache: false
    enable_component_cache: false
    session_timeout_minutes: 5
    max_cached_sessions: 10
    cleanup_interval_minutes: 1
    max_memory_mb: 50
    memory_check_interval: 1
    enable_metrics: true
    metrics_report_interval: 1
```

## 🔍 性能调优指南

### 缓存命中率优化
1. **延长超时时间**: 增加 `session_timeout_minutes`
2. **增加缓存容量**: 提高 `max_cached_sessions`
3. **减少清理频率**: 适当增加 `cleanup_interval_minutes`

### 内存使用优化
1. **减少缓存容量**: 降低 `max_cached_sessions`
2. **缩短超时时间**: 减少 `session_timeout_minutes`
3. **增加清理频率**: 减少 `cleanup_interval_minutes`
4. **禁用组件缓存**: 设置 `enable_component_cache: false`

### 响应时间优化
1. **启用所有缓存**: 确保 `enable_session_cache` 和 `enable_component_cache` 都为 `true`
2. **合理设置容量**: 根据并发量设置合适的 `max_cached_sessions`
3. **监控命中率**: 通过 API 监控缓存效果

## 📊 监控和调试

### 查看缓存统计
```bash
curl -X GET "http://localhost:8002/api/system/agent-pool-stats" | jq .
```

### 关键指标解读
- `cache_hit_rate`: 缓存命中率，目标 > 70%
- `reuse_rate`: 实例复用率，目标 > 60%
- `current_cached_count`: 当前缓存数量，不应超过 `max_cached_sessions`
- `avg_creation_time`: 平均创建时间，应保持稳定

### 性能问题排查
1. **命中率低**: 检查会话ID一致性，调整超时时间
2. **内存使用高**: 减少缓存容量，缩短超时时间
3. **响应慢**: 检查是否启用缓存，监控创建时间

## 🚨 注意事项

### 配置变更
- 配置变更需要重启应用才能生效
- 建议在低峰期进行配置调整
- 变更前备份当前配置

### 内存管理
- 监控系统总内存使用情况
- 避免设置过大的缓存容量
- 定期检查内存泄漏

### 并发安全
- 系统已实现线程安全，无需额外配置
- 多实例部署时每个实例独立管理缓存
- 负载均衡器应启用会话粘性

## 📚 相关文档

- [Agent实例池使用指南](../tools/agent-instance-pool.md)
- [性能监控使用指南](../tools/performance-monitoring.md)
- [配置化开发规范](../standards/配置化开发规范.md)
