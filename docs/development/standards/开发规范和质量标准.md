# 开发规范和质量标准

## 🎯 目标

基于我们完成的五重重构经验，建立一套完整的开发规范和质量标准，防止配置分散、代码冗余等问题再次出现，确保系统长期保持高质量和可维护性。

## 📋 核心原则

### 1. 配置驱动原则
- **统一配置源**：所有配置必须来自统一配置文件
- **禁止硬编码**：严禁在代码中硬编码任何配置值
- **分层配置**：按功能模块分层组织配置
- **环境隔离**：不同环境使用不同配置文件

### 2. 代码质量原则
- **DRY原则**：Don't Repeat Yourself，避免重复代码
- **单一职责**：每个模块只负责一个功能
- **依赖注入**：使用依赖注入而非直接创建依赖
- **接口隔离**：定义清晰的接口边界

### 3. 架构一致性原则
- **统一决策**：使用统一的决策引擎
- **标准化组件**：使用标准化的组件创建方式
- **配置缓存**：合理使用配置缓存提升性能
- **错误处理**：统一的错误处理机制

## 🚫 禁止事项

### 配置相关禁止事项
```python
# ❌ 禁止：硬编码配置值
temperature = 0.7
max_tokens = 4000
timeout = 30

# ❌ 禁止：重复调用配置
config1 = get_unified_config()
config2 = get_unified_config()
config3 = get_unified_config()

# ❌ 禁止：在多个地方定义相同配置
# file1.py
DEFAULT_TEMPERATURE = 0.7
# file2.py  
DEFAULT_TEMP = 0.7

# ❌ 禁止：直接读取配置文件
with open('config.yaml') as f:
    config = yaml.load(f)
```

### 代码质量禁止事项
```python
# ❌ 禁止：未使用的导入
import os  # 但代码中没有使用os
from typing import Dict, List, Optional  # 只使用了List

# ❌ 禁止：重复的代码逻辑
def process_user_data():
    # 相同的验证逻辑
    if not user_id:
        return "用户ID不能为空"
    # ...

def handle_user_request():
    # 相同的验证逻辑
    if not user_id:
        return "用户ID不能为空"
    # ...

# ❌ 禁止：通配符导入
from utils import *
```

## ✅ 推荐做法

### 配置管理最佳实践
```python
# ✅ 推荐：使用统一配置服务
class MyService:
    def __init__(self):
        # 在初始化时缓存配置
        self.config = get_unified_config()
    
    def process(self):
        # 使用缓存的配置
        temperature = self.config.get_threshold("confidence.default", 0.7)
        max_tokens = self.config.get_threshold("limits.max_tokens", 4000)

# ✅ 推荐：配置键使用层级结构
# unified_config.yaml
thresholds:
  confidence:
    default: 0.7
    high: 0.9
  performance:
    timeout:
      default: 30
      llm_service: 60
```

### 代码质量最佳实践
```python
# ✅ 推荐：精确导入
from typing import List, Dict
from backend.config.unified_config_loader import get_unified_config

# ✅ 推荐：提取公共逻辑
def validate_user_id(user_id: str) -> str:
    """统一的用户ID验证逻辑"""
    if not user_id:
        return "用户ID不能为空"
    return None

# ✅ 推荐：使用依赖注入
class UserService:
    def __init__(self, config_service, validator):
        self.config_service = config_service
        self.validator = validator
```

## 📁 文件组织规范

### 配置文件结构
```
backend/config/
├── unified_config.yaml          # 主配置文件（包含 message_templates、部分 thresholds）
├── unified_config.defaults.yaml # 默认配置
├── business_rules.yaml         # 业务规则
├── thresholds.yaml             # 阈值配置
├── database_queries.yaml       # 数据库查询
├── model_configs/              # 模型配置目录
│   ├── deepseek.yaml
│   ├── qwen.yaml
│   └── doubao.yaml
└── environments/               # 环境配置
    ├── development.yaml
    ├── testing.yaml
    └── production.yaml
```

### 代码文件组织
```
backend/
├── agents/                     # 智能体模块
│   ├── __init__.py
│   ├── factory.py             # 工厂类
│   └── strategies/            # 策略模块
├── config/                    # 配置模块
│   ├── __init__.py
│   ├── service.py            # 配置服务
│   └── unified_config_loader.py
├── handlers/                  # 处理器模块
├── utils/                     # 工具模块
└── services/                  # 服务模块
```

## 🔍 代码审查检查清单

### 配置相关检查
- [ ] 是否有硬编码的配置值？
- [ ] 是否使用了统一配置服务？
- [ ] 配置是否在初始化时缓存？
- [ ] 配置键是否使用了标准命名？
- [ ] 是否有重复的配置定义？

### 代码质量检查
- [ ] 是否有未使用的导入？
- [ ] 是否有重复的代码逻辑？
- [ ] 是否使用了通配符导入？
- [ ] 是否遵循了单一职责原则？
- [ ] 错误处理是否统一？

### 架构一致性检查
- [ ] 是否使用了统一的决策引擎？
- [ ] 组件创建是否标准化？
- [ ] 是否正确使用了依赖注入？
- [ ] 接口定义是否清晰？

## 🛠️ 自动化工具

### 代码质量检查工具
```bash
# 运行冗余代码检查
python scripts/cleanup_redundant_code.py

# 运行配置一致性检查
python scripts/check_config_consistency.py

# 运行未使用导入检查
python scripts/check_unused_imports.py

# 运行完整的预提交检查
python scripts/pre_commit_check.py
```

### Git钩子设置
```bash
# 设置Git钩子
python scripts/setup_git_hooks.py

# 手动运行预提交检查
python scripts/pre_commit_check.py

# 自动修复未使用导入
python scripts/check_unused_imports.py --fix
```

### 质量监控仪表板
```bash
# 生成质量报告
python scripts/generate_quality_dashboard.py

# 查看质量趋势
python scripts/quality_trend_analysis.py
```

## 📊 质量监控

### 定期检查任务
- **每日**：运行自动化代码质量检查
- **每周**：审查新增配置的合理性
- **每月**：全面的架构一致性检查
- **每季度**：技术债务评估和清理

### 质量指标
- **配置集中度**：所有配置来自统一源的比例
- **代码重复率**：重复代码的比例
- **导入清洁度**：未使用导入的比例
- **架构一致性**：符合标准的组件比例

## 🎓 培训和文档

### 新人培训内容
1. **配置管理**：如何正确使用统一配置
2. **代码质量**：编写高质量代码的标准
3. **架构原则**：系统架构的核心原则
4. **工具使用**：自动化工具的使用方法

### 文档维护
- **开发指南**：详细的开发指导文档
- **最佳实践**：经过验证的最佳实践集合
- **常见问题**：FAQ和解决方案
- **架构文档**：系统架构的详细说明

## 🚨 违规处理

### 违规等级
- **轻微违规**：未使用的导入、小范围重复代码
- **中等违规**：硬编码配置、重复配置调用
- **严重违规**：破坏架构一致性、大范围重复代码

### 处理流程
1. **自动检测**：通过工具自动发现违规
2. **通知开发者**：及时通知相关开发者
3. **修复跟踪**：跟踪修复进度
4. **经验总结**：总结经验，完善规范

## 📈 持续改进

### 规范更新机制
- **定期评估**：每季度评估规范的有效性
- **反馈收集**：收集开发团队的反馈
- **版本管理**：规范文档的版本管理
- **培训更新**：根据规范更新培训内容

### 工具完善
- **检查工具**：持续完善自动化检查工具
- **IDE集成**：集成到开发环境中
- **CI/CD集成**：集成到持续集成流程
- **报告优化**：优化质量报告的可读性

这套规范将确保我们的系统长期保持高质量和可维护性，防止配置分散和代码冗余问题的再次出现。
