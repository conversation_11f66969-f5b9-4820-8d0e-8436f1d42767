# 安全与合规设计文档

## 概述

本文档描述了需求采集系统的安全与合规设计，包括内容审查、用户输入过滤、安全事件记录等功能。

## 系统架构

### 核心组件

1. **ContentModerator** (`backend/safety/content_moderation.py`)
   - 内容审查核心引擎
   - 基于关键词的违规检测
   - 可配置的处理策略
   - 安全事件记录

2. **关键词系统** (`backend/config/keywords_config.yaml`)
   - 扩展现有关键词系统支持安全词汇
   - 按违规类型分类管理
   - 支持缓存和热重载

3. **API集成** (`backend/api/main.py`)
   - 在 `/chat` 和 `/autogen/chat` 接口集成审查
   - 最小侵入式设计
   - 保持向后兼容

## 功能特性

### 内容检测类别

| 类别 | 描述 | 默认动作 | 示例 |
|------|------|----------|------|
| profanity | 脏话/粗俗语言 | WARN | 傻逼、你妈的 |
| hate_speech | 仇恨言论 | BLOCK | 种族歧视、性别歧视 |
| sexual_content | 性相关内容 | BLOCK | 性骚扰、色情 |
| violence | 暴力威胁 | WARN | 杀死、威胁 |
| self_harm | 自残/自杀 | WARN | 自杀、轻生 |
| pii_patterns | 个人敏感信息 | MASK | 身份证、银行卡 |
| jailbreak | 恶意越狱提示 | WARN | 忽略指令、角色扮演 |

### 处理动作

- **ALLOW**: 正常通过，无需特殊处理
- **WARN**: 记录警告日志，在响应中添加提示，继续处理
- **MASK**: 对敏感信息进行掩码处理，继续处理
- **BLOCK**: 阻止处理，直接返回安全提示
- **ESCALATE**: 标记为高风险事件（预留）

## 配置说明

### 安全策略配置 (`unified_config.yaml`)

```yaml
security:
  content_moderation:
    enabled: true                    # 启用内容审查
    default_action: "WARN"           # 默认动作
    
    # 按类别配置动作
    actions:
      profanity: "WARN"              # 脏话：警告
      hate_speech: "BLOCK"           # 仇恨言论：阻止
      sexual_content: "BLOCK"        # 性内容：阻止
      violence: "WARN"               # 暴力：警告
      self_harm: "WARN"              # 自残：警告
      pii_patterns: "MASK"           # 个人信息：掩码
      jailbreak: "WARN"              # 越狱：警告
    
    # 掩码配置
    masking:
      replacement: "*"               # 替换字符
      keep_first_char: true          # 保留首字符
      min_mask_length: 2             # 最小掩码长度
    
    # 日志配置
    logging:
      log_violations: true           # 记录违规事件
      log_level: "INFO"              # 日志级别
      include_original_text: false   # 不在日志中包含原始文本
```

### 安全关键词配置 (`keywords_config.yaml`)

```yaml
safety_keywords:
  profanity:
    - "傻逼"
    - "你妈的"
    # ... 更多脏话词汇
  
  hate_speech:
    - "种族歧视"
    - "性别歧视"
    # ... 更多仇恨言论
  
  # ... 其他类别
```

### 安全提示模板 (`unified_config.yaml`)

```yaml
message_templates:
  error:
    safety:
      blocked_profanity: "为了保持文明与安全的沟通环境，请避免使用不当用语..."
      warning_profanity: "检测到不当用语，已为您做适当处理。以下是对您需求的回复："
      self_harm_support: |
        我注意到您可能遇到了困难。如果您正在经历情绪困扰，建议您：
        🆘 全国心理危机干预热线：400-161-9995
        # ... 更多帮助信息
```

## 使用方式

### 程序化调用

```python
from backend.safety import get_content_moderator, ModerationAction

# 获取审查器实例
moderator = get_content_moderator()

# 检查文本内容
result = moderator.check(
    text="用户输入文本",
    user_id="user123",
    session_id="session456"
)

# 根据结果处理
if result.action == ModerationAction.BLOCK:
    return error_response(result.reason)
elif result.action == ModerationAction.WARN:
    # 记录警告并继续处理
    processed_text = result.processed_text
```

### API集成

系统已自动在以下接口集成内容审查：

- `POST /chat` - 主要聊天接口
- `POST /autogen/chat` - AutoGen兼容接口

审查流程：
1. 接收用户输入
2. 执行内容审查
3. 根据审查结果决定处理方式
4. 记录安全事件日志
5. 返回响应（可能包含安全警告）

## 安全事件日志

### 日志格式

```json
{
  "event_type": "content_moderation",
  "action": "WARN",
  "category": "profanity",
  "matched_terms_count": 1,
  "user_id": "user123",
  "session_id": "session456",
  "timestamp": 1755274389.497,
  "confidence": 1.0,
  "text_length": 10,
  "matched_terms": ["违规词汇"]
}
```

### 隐私保护

- 默认不在日志中记录原始文本内容
- 仅记录文本长度和匹配的违规词汇
- 可通过配置启用原始文本记录（仅用于调试）

## 性能考虑

### 优化措施

1. **关键词缓存**: 利用现有关键词系统的缓存机制
2. **最小检测**: 仅对必要的违规类型进行检测
3. **快速失败**: 检测到严重违规时立即返回
4. **异步日志**: 安全事件记录不阻塞主流程

### 性能指标

- 单次检测耗时: < 5ms
- 内存占用: < 10MB（包含关键词缓存）
- 缓存命中率: > 95%

## 扩展指南

### 添加新的违规类别

1. 在 `keywords_config.yaml` 中添加新类别和关键词
2. 在 `unified_config.yaml` 中配置处理动作
3. 添加相应的提示模板
4. 更新文档

### 集成外部服务

系统设计支持集成外部内容审查服务：

```python
class ExternalModerationService:
    async def check_content(self, text: str) -> ModerationResult:
        # 调用外部API
        pass

# 在 ContentModerator 中集成
moderator.add_external_service(ExternalModerationService())
```

## 合规要求

### 数据保护

- 遵循最小化原则，仅记录必要信息
- 支持用户数据删除请求
- 敏感信息自动掩码处理

### 审计要求

- 完整的安全事件记录
- 可追溯的处理决策
- 定期的安全报告生成

## 监控与告警

### 关键指标

- 违规检测率
- 误报率
- 处理延迟
- 系统可用性

### 告警规则

- 违规率异常增长
- 系统错误率过高
- 处理延迟超阈值

## 测试策略

### 单元测试

- 关键词匹配准确性
- 策略决策正确性
- 文本处理功能

### 集成测试

- API接口集成
- 日志记录完整性
- 配置热重载

### 安全测试

- 绕过尝试检测
- 边界条件处理
- 性能压力测试

## 维护指南

### 关键词维护

- 定期更新违规词库
- 监控误报和漏报
- 根据用户反馈调整

### 配置调优

- 根据业务需求调整策略
- 优化性能参数
- 更新提示模板

### 版本升级

- 保持向后兼容
- 渐进式功能发布
- 完整的回滚计划
