# 配置管理指南

## 📋 概述

本项目采用分层配置管理架构，将敏感信息、业务配置和默认配置分离管理，确保安全性和可维护性。

## 🏗️ 配置架构

### 配置文件层次结构

```
项目根目录/
├── .env                                    # 环境变量（敏感信息）
├── .env.example                           # 环境变量模板
└── backend/config/
    ├── unified_config.yaml                # 主配置文件
    ├── unified_config.defaults.yaml       # 默认配置文件
    ├── unified_config_loader.py           # 配置加载器
    └── service.py                         # 配置服务接口
```

### 加载优先级

1. **环境变量** (`.env`) - 最高优先级
2. **主配置文件** (`unified_config.yaml`) - 业务配置
3. **默认配置** (`unified_config.defaults.yaml`) - 兜底配置

## 🔧 环境变量配置

### .env 文件

存放敏感信息和环境特定配置：

```bash
# API密钥
DEEPSEEK_API_KEY=your-deepseek-api-key
DOUBAO_API_KEY=your-doubao-api-key
QWEN_API_KEY=your-qwen-api-key
OPENROUTER_API_KEY=your-openrouter-api-key

# 性能配置
LLM_TIMEOUT=45.0
LLM_MAX_RETRIES=5
LLM_CIRCUIT_BREAKER_FAILURE_THRESHOLD=8

# 功能开关
ENABLE_CACHE=true
ENABLE_MONITORING=true
DEBUG=false

# 性能模式配置
HIGH_CONCURRENCY_MODE=false

# 系统优化配置 (可选)
ENABLE_INIT_TRACKING=true
ENABLE_CONFIG_CACHE=true
DUPLICATE_WARNING_THRESHOLD=2

# 服务配置
API_PORT=8000
LOG_LEVEL=INFO
```

### 环境变量使用规范

1. **敏感信息必须使用环境变量**：API密钥、数据库密码等
2. **环境特定配置**：端口号、调试开关、日志级别等
3. **命名规范**：使用大写字母和下划线，如 `API_KEY`、`MAX_RETRIES`

## 📄 YAML配置文件

### 主配置文件 (unified_config.yaml)

包含业务逻辑配置和环境变量引用：

```yaml
# LLM模型配置
llm:
  models:
    deepseek-chat:
      provider: "deepseek"
      api_key: "${DEEPSEEK_API_KEY}"        # 引用环境变量
      api_base: "https://api.deepseek.com"
      temperature: 0.7
      max_tokens: 4000

# 消息模板
message_templates:
  greeting:
    basic: "您好！我是智能需求采集助手"
  error:
    technical_issue: "系统遇到技术问题，请稍后重试"

# 业务规则
business_rules:
  requirement_collection:
    completion_threshold: 0.8
    max_iterations: 5

# 阈值配置
thresholds:
  keyword_match_threshold: 0.8
  semantic_similarity_threshold: 0.7
```

### 环境变量引用语法

在YAML文件中引用环境变量：

```yaml
# 基本引用
api_key: "${API_KEY}"

# 带默认值的引用
timeout: "${TIMEOUT:-30}"

# 复杂配置中的引用
database:
  url: "sqlite:///${DATABASE_PATH:-backend/data/aidatabase.db}"
```

## 🔄 配置加载机制

### 统一配置加载器

```python
from backend.config.unified_config_loader import get_unified_config

# 获取配置实例
config = get_unified_config()

# 获取配置值
api_key = config.get_config_value("llm.models.deepseek-chat.api_key")
template = config.get_message_template("greeting.basic")
threshold = config.get_threshold("keyword_match_threshold", 0.8)
```

### 配置服务接口

```python
from backend.config import config_service

# 推荐使用方式 - 通过服务接口
llm_config = config_service.get_llm_config("intent_recognition")
business_rule = config_service.get_business_rule("retry.max_attempts", 3)
template = config_service.get_message_template("error.technical_issue")
```

## 🛠️ 开发指南

### 添加新配置

1. **确定配置类型**：
   - 敏感信息 → 添加到 `.env`
   - 业务配置 → 添加到 `unified_config.yaml`
   - 默认值 → 添加到 `unified_config.defaults.yaml`

2. **更新配置文件**：
   ```yaml
   # 在 unified_config.yaml 中添加
   new_feature:
     api_key: "${NEW_FEATURE_API_KEY}"
     timeout: 30
     enabled: true
   ```

3. **更新环境变量模板**：
   ```bash
   # 在 .env.example 中添加
   NEW_FEATURE_API_KEY=your-api-key-here
   ```

4. **在代码中使用**：
   ```python
   config = get_unified_config()
   api_key = config.get_config_value("new_feature.api_key")
   ```

### 配置验证

```python
# 检查必需的环境变量
required_env_vars = [
    "DEEPSEEK_API_KEY",
    "DOUBAO_API_KEY", 
    "QWEN_API_KEY"
]

for var in required_env_vars:
    if not os.getenv(var):
        raise ValueError(f"必需的环境变量 {var} 未设置")
```

## 🔒 安全最佳实践

1. **永远不要在代码中硬编码敏感信息**
2. **使用 `.gitignore` 排除 `.env` 文件**
3. **提供 `.env.example` 作为配置模板**
4. **定期轮换API密钥**
5. **在生产环境中使用强密码和密钥**

## 🚨 常见问题

### Q: 环境变量未生效？
A: 检查 `.env` 文件是否存在，变量名是否正确，应用是否重启。

### Q: 配置文件修改后未生效？
A: 配置文件修改后需要重启应用服务。

### Q: 如何调试配置加载问题？
A: 启用详细日志：`LOG_LEVEL=DEBUG`，查看配置加载日志。

## 📚 相关文档

- [开发者快速参考卡片](../开发者快速参考卡片.md)
- [项目架构和模块关系文档](../项目架构和模块关系文档.md)
- [API接口文档](../api/)
