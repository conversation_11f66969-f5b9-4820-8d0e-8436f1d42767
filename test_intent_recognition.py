#!/usr/bin/env python3
"""
测试意图识别修复效果
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

from backend.agents.simplified_decision_engine import SimplifiedDecisionEngine
from backend.utils.intent_manager import IntentManager

async def test_intent_recognition():
    """测试意图识别修复效果"""
    
    print("🔍 测试意图识别修复效果")
    print("=" * 50)
    
    # 初始化组件
    intent_manager = IntentManager()
    decision_engine = SimplifiedDecisionEngine(intent_manager)
    
    # 测试用例
    test_cases = [
        {
            "message": "这些价格有什么不同",
            "state": "COLLECTING_INFO",
            "expected": "process_answer",
            "description": "在收集信息状态下询问价格差异"
        },
        {
            "message": "这些选项有什么区别",
            "state": "COLLECTING_INFO", 
            "expected": "process_answer",
            "description": "在收集信息状态下询问选项区别"
        },
        {
            "message": "120cm×240cm",
            "state": "COLLECTING_INFO",
            "expected": "process_answer",
            "description": "在收集信息状态下提供具体尺寸"
        },
        {
            "message": "这个问题是什么意思",
            "state": "COLLECTING_INFO",
            "expected": "request_clarification",
            "description": "在收集信息状态下询问问题含义（应该是澄清）"
        },
        {
            "message": "这些价格有什么不同",
            "state": "IDLE",
            "expected": "request_clarification",
            "description": "在空闲状态下询问价格差异（应该是澄清）"
        }
    ]
    
    # 执行测试
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['description']}")
        print(f"   输入: '{test_case['message']}'")
        print(f"   状态: {test_case['state']}")
        print(f"   期望: {test_case['expected']}")
        
        try:
            # 构建上下文
            context = [{
                "session_id": "test_session",
                "user_id": "test_user", 
                "current_state": test_case['state'],
                "domain": None,
                "category": None,
                "history": [],
                "conversation_history": []
            }]
            
            # 执行意图识别
            result = await decision_engine.analyze(test_case['message'], context)
            actual_intent = result.get('decision', {}).get('intent', 'unknown')
            
            print(f"   实际: {actual_intent}")
            
            # 检查结果
            if actual_intent == test_case['expected']:
                print(f"   ✅ 测试通过")
            else:
                print(f"   ❌ 测试失败！期望 {test_case['expected']}，实际 {actual_intent}")
                
        except Exception as e:
            print(f"   ❌ 测试出错: {e}")
    
    print(f"\n{'='*50}")
    print("测试完成！")

if __name__ == "__main__":
    asyncio.run(test_intent_recognition())
