# 端到端测试复合意图识别
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath('.'))

from backend.agents.simplified_decision_engine import SimplifiedDecisionEngine
from backend.agents.integrated_reply_system import IntegratedReplySystem, DecisionContext

async def test_end_to_end_composite_intent():
    print('=== 端到端复合意图识别测试 ===')
    
    # 1. 测试SimplifiedDecisionEngine的复合意图识别
    engine = SimplifiedDecisionEngine()
    
    test_message = '想先支持Apple Pay、Google Pay。对了，您们平台支持哪些支付方式？'
    context = [{
        'current_state': 'IDLE',
        'conversation_history': []
    }]
    
    print(f'输入消息: {test_message}')
    print()
    
    # 测试意图分析
    analysis_result = await engine.analyze(test_message, context)
    
    print('--- 意图分析结果 ---')
    print(f'主意图: {analysis_result.get("intent", "未知")}')
    print(f'子意图: {analysis_result.get("sub_intent", "无")}')
    print(f'情感: {analysis_result.get("emotion", "未知")}')
    print(f'Action: {analysis_result.get("decision", {}).get("action", "未知")}')
    print()
    
    # 2. 测试IntegratedReplySystem的策略获取
    try:
        reply_system = IntegratedReplySystem()
        
        decision_context = DecisionContext(
            intent=analysis_result.get('intent', 'unknown'),
            emotion=analysis_result.get('emotion', 'neutral'),
            current_state='IDLE',
            session_id='test_session',
            user_id='test_user',
            user_message=test_message,
            sub_intent=analysis_result.get('sub_intent', None)
        )
        
        print('--- DecisionContext ---')
        print(f'Intent: {decision_context.intent}')
        print(f'Sub_intent: {decision_context.sub_intent}')
        print(f'Emotion: {decision_context.emotion}')
        print()
        
        # 测试策略获取
        strategy = reply_system.decision_engine.get_strategy(
            decision_context.intent,
            decision_context.emotion,
            {'current_state': decision_context.current_state},
            decision_context.sub_intent
        )
        
        print('--- 最终策略配置 ---')
        import json
        print(json.dumps(strategy, ensure_ascii=False, indent=2))
        
    except Exception as e:
        print(f'IntegratedReplySystem测试失败: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 运行测试
    asyncio.run(test_end_to_end_composite_intent())
