import React, { useState } from 'react';
import { 
  Card, 
  // Table,
  Button,
  Modal,
  // Form,
  Space,
  message,
  Tag,
  // Descriptions,
  // Tooltip,
  Tree,
  Input,
  Select
} from 'antd';
import { 
  EditOutlined,
  ReloadOutlined,
  FileTextOutlined,
  FolderOutlined,
  FileOutlined
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { templateApi } from '../../api/services';
import MonacoEditor from '@monaco-editor/react';

const { Option } = Select;
// const { TextArea } = Input;

interface MessageTemplate {
  path: string;
  content: any;
  template_type: string;
}

interface TreeNode {
  title: string;
  key: string;
  children?: TreeNode[];
  isLeaf?: boolean;
  content?: any;
  template_type?: string;
}

const MessageTemplates: React.FC = () => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<MessageTemplate | null>(null);
  const [selectedNode, setSelectedNode] = useState<string>('');
  const [jsonContent, setJsonContent] = useState<string>('');
  
  const queryClient = useQueryClient();

  // 获取所有消息模板
  const { data: templates = [], isLoading } = useQuery({
    queryKey: ['message-templates'],
    queryFn: async () => {
      const response = await templateApi.getMessageTemplates();
      return response.data;
    }
  });

  // 更新消息模板
  const updateMutation = useMutation({
    mutationFn: ({ path, content }: { path: string; content: string }) => 
      templateApi.updateMessageTemplate(path, content),
    onSuccess: () => {
      message.success('消息模板更新成功');
      queryClient.invalidateQueries({ queryKey: ['message-templates'] });
      setIsModalVisible(false);
    },
    onError: (error: any) => {
      message.error(`更新失败: ${error.response?.data?.detail || error.message}`);
    }
  });

  // 将扁平数据转换为树形结构
  const buildTreeData = (templates: MessageTemplate[]): TreeNode[] => {
    const tree: TreeNode[] = [];
    const map = new Map<string, TreeNode>();

    templates.forEach((template: MessageTemplate) => {
      const parts = template.path.split('.');
      let currentPath = '';
      let currentNode: TreeNode | undefined;

      parts.forEach((part: string, index: number) => {
        currentPath = currentPath ? `${currentPath}.${part}` : part;
        
        if (!map.has(currentPath)) {
          const isLeaf = index === parts.length - 1;
          const newNode: TreeNode = {
            title: part,
            key: currentPath,
            isLeaf,
            ...(isLeaf ? { 
              content: template.content, 
              template_type: template.template_type 
            } : {})
          };
          
          map.set(currentPath, newNode);
          
          if (index === 0) {
            tree.push(newNode);
          } else {
            const parentPath = parts.slice(0, index).join('.');
            const parent = map.get(parentPath);
            if (parent) {
              parent.children = parent.children || [];
              parent.children.push(newNode);
            }
          }
        }
        
        currentNode = map.get(currentPath);
      });
    });

    return tree;
  };

  const treeData = buildTreeData(templates);

  const handleTreeSelect = (selectedKeys: any[], info: any) => {
    if (selectedKeys && selectedKeys.length > 0) {
      const key = selectedKeys[0] as string;
      setSelectedNode(key);
      
      // 查找对应的模板
      const template = templates.find((t: MessageTemplate) => t.path === key);
      if (template) {
        setJsonContent(typeof template.content === 'string' 
          ? template.content 
          : JSON.stringify(template.content, null, 2));
      }
    }
  };

  const handleEdit = (template: MessageTemplate) => {
    setEditingTemplate(template);
    setJsonContent(typeof template.content === 'string' 
      ? template.content 
      : JSON.stringify(template.content, null, 2));
    setIsModalVisible(true);
  };

  const handleModalOk = () => {
    if (editingTemplate) {
      try {
        // 尝试解析JSON，如果是对象则保持原样，如果是字符串则直接使用
        let parsedContent;
        try {
          parsedContent = JSON.parse(jsonContent);
        } catch {
          parsedContent = jsonContent;
        }
        
        updateMutation.mutate({ 
          path: editingTemplate.path, 
          content: jsonContent 
        });
      } catch (error) {
        message.error('JSON格式错误，请检查格式');
      }
    }
  };

  const handleReload = () => {
    queryClient.invalidateQueries({ queryKey: ['message-templates'] });
    message.success('消息模板已刷新');
  };

  const getTemplateTypeTag = (type: string) => {
    switch (type) {
      case 'string':
        return <Tag color="green">字符串</Tag>;
      case 'object':
        return <Tag color="blue">对象</Tag>;
      case 'array':
        return <Tag color="orange">数组</Tag>;
      default:
        return <Tag>{type}</Tag>;
    }
  };

  const renderTreeNode = (node: TreeNode) => {
    const icon = node.isLeaf ? <FileOutlined /> : <FolderOutlined />;
    return (
      <span>
        {icon} {node.title}
        {node.template_type && (
          <span style={{ marginLeft: 8 }}>
            {getTemplateTypeTag(node.template_type)}
          </span>
        )}
      </span>
    );
  };

  return (
    <div>
      <Card 
        title="消息模板管理" 
        extra={
          <Space>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={handleReload}
            >
              刷新
            </Button>
          </Space>
        }
      >
        <div style={{ display: 'flex', gap: 16 }}>
          <div style={{ flex: 1, minWidth: 300 }}>
            <h3>模板结构</h3>
            <Tree
              treeData={treeData}
              showIcon
              defaultExpandAll
              onSelect={handleTreeSelect}
              titleRender={renderTreeNode}
              style={{ 
                border: '1px solid #f0f0f0', 
                borderRadius: 4, 
                padding: 8,
                maxHeight: 600,
                overflow: 'auto'
              }}
            />
          </div>
          
          <div style={{ flex: 2 }}>
            <h3>模板内容</h3>
            {selectedNode ? (
              <Card 
                size="small"
                title={
                  <Space>
                    <FileTextOutlined />
                    {selectedNode}
                    {templates.find((t: MessageTemplate) => t.path === selectedNode)?.template_type && (
                      <span>
                        {getTemplateTypeTag(
                          templates.find((t: MessageTemplate) => t.path === selectedNode)!.template_type
                        )}
                      </span>
                    )}
                  </Space>
                }
                extra={
                  <Button
                    type="primary"
                    icon={<EditOutlined />}
                    size="small"
                    onClick={() => {
                      const template = templates.find((t: MessageTemplate) => t.path === selectedNode);
                      if (template) handleEdit(template);
                    }}
                  >
                    编辑
                  </Button>
                }
              >
                <pre style={{ 
                  background: '#f5f5f5', 
                  padding: 16, 
                  borderRadius: 4,
                  maxHeight: 500,
                  overflow: 'auto',
                  whiteSpace: 'pre-wrap',
                  fontSize: 14
                }}>
                  {jsonContent}
                </pre>
              </Card>
            ) : (
              <div style={{ 
                padding: 40, 
                textAlign: 'center', 
                color: '#999',
                border: '1px dashed #d9d9d9',
                borderRadius: 4
              }}>
                <FileTextOutlined style={{ fontSize: 48, marginBottom: 16 }} />
                <p>请从左侧选择一个消息模板查看详情</p>
              </div>
            )}
          </div>
        </div>
      </Card>

      {/* 编辑模态框 */}
      <Modal
        title={`编辑消息模板: ${editingTemplate?.path}`}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={null}
        width={800}
        style={{ top: 20 }}
      >
        <div style={{ marginBottom: 16 }}>
          <Tag color="blue">JSON/YAML格式</Tag>
          <span style={{ marginLeft: 8, color: '#666' }}>
            请确保格式正确，系统将自动验证
          </span>
        </div>
        
        <MonacoEditor
          height="400px"
          language="json"
          theme="vs-dark"
          value={jsonContent}
          onChange={(value) => {
            if (value !== undefined) {
              setJsonContent(value);
            }
          }}
          options={{
            minimap: { enabled: false },
            wordWrap: 'on',
            lineNumbers: 'on',
            scrollBeyondLastLine: false,
            automaticLayout: true,
            tabSize: 2,
            fontSize: 14
          }}
        />
        
        <div style={{ marginTop: 16, textAlign: 'right' }}>
          <Space>
            <Button onClick={() => setIsModalVisible(false)}>取消</Button>
            <Button 
              type="primary" 
              onClick={handleModalOk}
              loading={updateMutation.isPending}
            >
              保存
            </Button>
          </Space>
        </div>
      </Modal>
    </div>
  );
};

export default MessageTemplates;
