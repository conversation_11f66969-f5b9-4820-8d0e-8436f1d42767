import React, { useState } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Modal, 
  Form, 
  Select, 
  InputNumber, 
  Space, 
  message, 
  Tag,
  Descriptions,
  // Tooltip,
  Input
} from 'antd';
import { 
  EditOutlined, 
  PlayCircleOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { scenarioApi, llmConfigApi } from '../../api/services';

const { Option } = Select;

interface ScenarioMapping {
  scenario_name: string;
  model_name: string;
  parameters: {
    temperature?: number;
    max_tokens?: number;
    timeout?: number;
    max_retries?: number;
  };
}

const ScenarioMapping: React.FC = () => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingScenario, setEditingScenario] = useState<ScenarioMapping | null>(null);
  const [isTestModalVisible, setIsTestModalVisible] = useState(false);
  const [testingScenario, setTestingScenario] = useState<string>('');
  const [testInput, setTestInput] = useState('这是一个测试输入');
  const [testResult, setTestResult] = useState<any>(null);
  
  const queryClient = useQueryClient();

  // 获取所有场景映射
  const { data: mappings = [], isLoading: mappingsLoading } = useQuery({
    queryKey: ['scenario-mappings'],
    queryFn: async () => {
      const response = await scenarioApi.getAllMappings();
      return response.data;
    }
  });

  // 获取可用模型列表
  const { data: availableModels = [] } = useQuery({
    queryKey: ['available-models'],
    queryFn: async () => {
      const response = await scenarioApi.getAvailableModels();
      return response.data.models || [];
    }
  });

  // 更新场景映射
  const updateMutation = useMutation({
    mutationFn: ({ name, mapping }: { name: string; mapping: any }) => 
      scenarioApi.updateScenarioMapping(name, mapping),
    onSuccess: () => {
      message.success('场景映射更新成功');
      queryClient.invalidateQueries({ queryKey: ['scenario-mappings'] });
      setIsModalVisible(false);
    },
    onError: (error: any) => {
      message.error(`更新失败: ${error.response?.data?.detail || error.message}`);
    }
  });

  // 测试场景配置
  const testMutation = useMutation({
    mutationFn: ({ name, input }: { name: string; input: string }) => 
      scenarioApi.testScenarioConfig(name, input),
    onSuccess: (response) => {
      setTestResult(response.data);
      if (response.data.success) {
        message.success('场景测试成功');
      } else {
        message.error('场景测试失败');
      }
    },
    onError: (error: any) => {
      message.error(`测试失败: ${error.response?.data?.detail || error.message}`);
    }
  });

  const columns = [
    {
      title: '场景名称',
      dataIndex: 'scenario_name',
      key: 'scenario_name',
      render: (text: string) => <strong>{text}</strong>
    },
    {
      title: '映射模型',
      dataIndex: 'model_name',
      key: 'model_name',
      render: (modelName: string) => (
        <Tag color="blue">{modelName}</Tag>
      )
    },
    {
      title: '参数配置',
      key: 'parameters',
      render: (_: any, record: ScenarioMapping) => (
        <Space direction="vertical" size={0}>
          {record.parameters.temperature && <span>温度: {record.parameters.temperature}</span>}
          {record.parameters.max_tokens && <span>最大token: {record.parameters.max_tokens}</span>}
          {record.parameters.timeout && <span>超时: {record.parameters.timeout}s</span>}
          {record.parameters.max_retries && <span>重试: {record.parameters.max_retries}次</span>}
        </Space>
      )
    },
    {
      title: '操作',
      key: 'actions',
      render: (_: any, record: ScenarioMapping) => (
        <Space>
          <Button
            type="primary"
            icon={<EditOutlined />}
            size="small"
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            icon={<PlayCircleOutlined />}
            size="small"
            onClick={() => handleTest(record.scenario_name)}
          >
            测试
          </Button>
        </Space>
      )
    }
  ];

  const handleEdit = (scenario: ScenarioMapping) => {
    setEditingScenario(scenario);
    setIsModalVisible(true);
  };

  const handleTest = (scenarioName: string) => {
    setTestingScenario(scenarioName);
    setIsTestModalVisible(true);
    setTestResult(null);
  };

  const handleModalOk = (values: any) => {
    if (editingScenario) {
      updateMutation.mutate({ 
        name: editingScenario.scenario_name, 
        mapping: values 
      });
    }
  };

  const handleTestModalOk = () => {
    testMutation.mutate({ name: testingScenario, input: testInput });
  };

  const handleReload = () => {
    queryClient.invalidateQueries({ queryKey: ['scenario-mappings'] });
    message.success('场景映射已刷新');
  };

  // 场景描述映射
  const scenarioDescriptions: Record<string, string> = {
    'category_classification': '需求分类识别',
    'intent_recognition': '意图识别',
    'information_extraction': '信息提取',
    'document_generation': '文档生成',
    'reply_generation': '回复生成',
    'conversation_flow': '对话流程',
    'safety_check': '安全检查',
    'quality_assurance': '质量保证'
  };

  return (
    <div>
      <Card 
        title="场景映射管理" 
        extra={
          <Space>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={handleReload}
            >
              刷新
            </Button>
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={mappings}
          rowKey="scenario_name"
          loading={mappingsLoading}
          pagination={{ pageSize: 10 }}
          expandable={{
            expandedRowRender: (record: ScenarioMapping) => (
              <Descriptions column={2} size="small">
                <Descriptions.Item label="场景描述">
                  {scenarioDescriptions[record.scenario_name] || record.scenario_name}
                </Descriptions.Item>
                <Descriptions.Item label="使用模型">
                  <Tag color="blue">{record.model_name}</Tag>
                </Descriptions.Item>
                <Descriptions.Item label="温度">
                  {record.parameters.temperature || '使用模型默认值'}
                </Descriptions.Item>
                <Descriptions.Item label="最大Token">
                  {record.parameters.max_tokens || '使用模型默认值'}
                </Descriptions.Item>
                <Descriptions.Item label="超时时间">
                  {record.parameters.timeout ? `${record.parameters.timeout}s` : '使用模型默认值'}
                </Descriptions.Item>
                <Descriptions.Item label="重试次数">
                  {record.parameters.max_retries || '使用模型默认值'}
                </Descriptions.Item>
              </Descriptions>
            )
          }}
        />
      </Card>

      {/* 编辑模态框 */}
      <Modal
        title={`编辑场景映射: ${editingScenario?.scenario_name}`}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          layout="vertical"
          onFinish={handleModalOk}
          initialValues={{
            model_name: editingScenario?.model_name,
            ...editingScenario?.parameters
          }}
        >
          <Form.Item
            label="选择模型"
            name="model_name"
            rules={[{ required: true, message: '请选择模型' }]}
          >
            <Select placeholder="选择模型">
              {availableModels.map((model: string) => (
                <Option key={model} value={model}>{model}</Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            label="温度 (可选)"
            name="temperature"
            tooltip="留空使用模型默认值"
          >
            <InputNumber 
              min={0} 
              max={2} 
              step={0.1} 
              style={{ width: '100%' }}
              placeholder="使用模型默认值"
            />
          </Form.Item>

          <Form.Item
            label="最大Token数 (可选)"
            name="max_tokens"
            tooltip="留空使用模型默认值"
          >
            <InputNumber 
              min={1} 
              max={100000} 
              style={{ width: '100%' }}
              placeholder="使用模型默认值"
            />
          </Form.Item>

          <Form.Item
            label="超时时间 (秒，可选)"
            name="timeout"
            tooltip="留空使用模型默认值"
          >
            <InputNumber 
              min={1} 
              max={300} 
              style={{ width: '100%' }}
              placeholder="使用模型默认值"
            />
          </Form.Item>

          <Form.Item
            label="最大重试次数 (可选)"
            name="max_retries"
            tooltip="留空使用模型默认值"
          >
            <InputNumber 
              min={0} 
              max={10} 
              style={{ width: '100%' }}
              placeholder="使用模型默认值"
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={updateMutation.isPending}>
                更新
              </Button>
              <Button onClick={() => setIsModalVisible(false)}>取消</Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 测试模态框 */}
      <Modal
        title={`测试场景配置: ${testingScenario}`}
        open={isTestModalVisible}
        onCancel={() => setIsTestModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form layout="vertical">
          <Form.Item label="测试输入">
            <Input.TextArea
              rows={4}
              value={testInput}
              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setTestInput(e.target.value)}
            />
          </Form.Item>
          
          <Form.Item>
            <Space>
              <Button 
                type="primary" 
                onClick={handleTestModalOk}
                loading={testMutation.isPending}
              >
                开始测试
              </Button>
              <Button onClick={() => setIsTestModalVisible(false)}>关闭</Button>
            </Space>
          </Form.Item>
        </Form>

        {testResult && (
          <div style={{ marginTop: 16 }}>
            <h4>测试结果:</h4>
            <p>状态: <Tag color={testResult.success ? 'green' : 'red'}>
              {testResult.success ? '成功' : '失败'}
            </Tag></p>
            <p>使用模型: <Tag color="blue">{testResult.model_used}</Tag></p>
            <p>延迟: {testResult.latency}秒</p>
            
            {testResult.parameters_used && (
              <div>
                <p>使用参数:</p>
                <pre style={{ 
                  background: '#f5f5f5', 
                  padding: 8, 
                  borderRadius: 4,
                  fontSize: 12
                }}>
                  {JSON.stringify(testResult.parameters_used, null, 2)}
                </pre>
              </div>
            )}
            
            {testResult.success && (
              <div>
                <p>响应:</p>
                <pre style={{ 
                  background: '#f5f5f5', 
                  padding: 8, 
                  borderRadius: 4,
                  maxHeight: 200,
                  overflow: 'auto'
                }}>
                  {testResult.response}
                </pre>
              </div>
            )}
            {testResult.error && (
              <div>
                <p>错误:</p>
                <pre style={{ 
                  background: '#fff2f0', 
                  padding: 8, 
                  borderRadius: 4,
                  color: '#ff4d4f'
                }}>
                  {testResult.error}
                </pre>
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
};

export default ScenarioMapping;
