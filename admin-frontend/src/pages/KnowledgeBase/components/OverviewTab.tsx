import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Statistic, Spin, message, Empty } from 'antd';
import { 
  FileTextOutlined, 
  UserOutlined, 
  TagsOutlined, 
  DatabaseOutlined 
} from '@ant-design/icons';
import { knowledgeBaseService, OverviewStats } from '../../../services/knowledgeBase';

const OverviewTab: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState<OverviewStats | null>(null);

  const fetchOverviewStats = async () => {
    setLoading(true);
    try {
      const data = await knowledgeBaseService.getOverviewStats();
      setStats(data);
    } catch (error) {
      console.error('获取概览统计失败:', error);
      message.error('获取概览统计失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchOverviewStats();
  }, []);

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: '16px' }}>加载概览数据中...</div>
      </div>
    );
  }

  if (!stats) {
    return (
      <Empty 
        description="暂无数据" 
        style={{ padding: '50px' }}
      />
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="总文档数"
              value={stats?.total_documents || 0}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="唯一文档数"
              value={stats?.unique_documents || 0}
              prefix={<DatabaseOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="角色类型"
              value={stats?.role_types || 0}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="分类数量"
              value={stats?.categories || 0}
              prefix={<TagsOutlined />}
              valueStyle={{ color: '#eb2f96' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 详细统计 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card title="角色详细统计" size="small">
            {Object.entries(stats?.role_distribution || {}).length > 0 ? (
              <div>
                {Object.entries(stats?.role_distribution || {}).map(([role, count]) => (
                  <div
                    key={role}
                    style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      padding: '8px 0',
                      borderBottom: '1px solid #f0f0f0'
                    }}
                  >
                    <span>{role}</span>
                    <span style={{ fontWeight: 'bold' }}>{count}</span>
                  </div>
                ))}
              </div>
            ) : (
              <Empty description="暂无角色统计数据" />
            )}
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="分类详细统计" size="small">
            {Object.entries(stats?.category_distribution || {}).length > 0 ? (
              <div>
                {Object.entries(stats?.category_distribution || {}).map(([category, count]) => (
                  <div
                    key={category}
                    style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      padding: '8px 0',
                      borderBottom: '1px solid #f0f0f0'
                    }}
                  >
                    <span>{category}</span>
                    <span style={{ fontWeight: 'bold' }}>{count}</span>
                  </div>
                ))}
              </div>
            ) : (
              <Empty description="暂无分类统计数据" />
            )}
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default OverviewTab;
