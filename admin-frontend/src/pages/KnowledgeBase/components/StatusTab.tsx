import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Row, 
  Col, 
  Badge, 
  Descriptions, 
  Button, 
  Space, 
  Alert, 
  Typography,
  Statistic,
  Tag,
  message
} from 'antd';
import { 
  CheckCircleOutlined, 
  ExclamationCircleOutlined, 
  ReloadOutlined,
  DatabaseOutlined,
  SettingOutlined,
  ApiOutlined
} from '@ant-design/icons';
import { knowledgeBaseService, SystemStatus } from '../../../services/knowledgeBase';

const { Text } = Typography;

interface StatusTabProps {
  systemStatus: any;
  onRefresh: () => void;
}

const StatusTab: React.FC<StatusTabProps> = ({ systemStatus: propSystemStatus, onRefresh }) => {
  const [loading, setLoading] = useState(false);
  const [systemStatus, setSystemStatus] = useState<SystemStatus | null>(null);

  const fetchSystemStatus = async () => {
    setLoading(true);
    try {
      const status = await knowledgeBaseService.getSystemStatus();
      setSystemStatus(status);
    } catch (error) {
      console.error('获取系统状态失败:', error);
      message.error('获取系统状态失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSystemStatus();
  }, []);

  const handleRefresh = () => {
    fetchSystemStatus();
    onRefresh();
    message.success('状态已刷新');
  };

  // 获取状态颜色和图标
  const getStatusDisplay = (status: string) => {
    switch (status) {
      case 'connected':
      case 'healthy':
        return { color: 'success', icon: <CheckCircleOutlined />, text: '正常' };
      case 'disconnected':
      case 'unhealthy':
        return { color: 'error', icon: <ExclamationCircleOutlined />, text: '异常' };
      default:
        return { color: 'warning', icon: <ExclamationCircleOutlined />, text: status };
    }
  };

  const chromadbStatus = systemStatus ? getStatusDisplay(systemStatus.chromadb_status) : null;

  return (
    <div>
      {/* 整体状态概览 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col span={24}>
          <Alert
            message={
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <span>
                  知识库服务状态: {' '}
                  {propSystemStatus ? (
                    <Badge 
                      status={propSystemStatus.status === 'healthy' ? 'success' : 'error'} 
                      text={propSystemStatus.status === 'healthy' ? '服务正常' : '服务异常'} 
                    />
                  ) : (
                    <Badge status="processing" text="检查中..." />
                  )}
                </span>
                <Button 
                  icon={<ReloadOutlined />} 
                  onClick={handleRefresh}
                  loading={loading}
                  size="small"
                >
                  刷新状态
                </Button>
              </div>
            }
            type={propSystemStatus?.status === 'healthy' ? 'success' : 'warning'}
            showIcon
          />
        </Col>
      </Row>

      {/* 详细状态信息 */}
      <Row gutter={[16, 16]}>
        {/* ChromaDB状态 */}
        <Col xs={24} lg={12}>
          <Card 
            title={
              <Space>
                <DatabaseOutlined />
                ChromaDB状态
              </Space>
            }
          >
            {systemStatus ? (
              <div>
                <div style={{ marginBottom: '16px' }}>
                  <Badge 
                    status={chromadbStatus?.color as any} 
                    text={
                      <span>
                        连接状态: <strong>{chromadbStatus?.text}</strong>
                      </span>
                    } 
                  />
                </div>
                
                <Statistic
                  title="文档数量"
                  value={systemStatus.document_count}
                  prefix={<DatabaseOutlined />}
                  valueStyle={{ color: '#1890ff' }}
                />
                
                {systemStatus.collection_info && (
                  <div style={{ marginTop: '16px' }}>
                    <Descriptions size="small" column={1}>
                      <Descriptions.Item label="集合名称">
                        {systemStatus.collection_info.name || 'N/A'}
                      </Descriptions.Item>
                      <Descriptions.Item label="文档数量">
                        {systemStatus.collection_info.document_count || 0}
                      </Descriptions.Item>
                    </Descriptions>
                  </div>
                )}
              </div>
            ) : (
              <div style={{ textAlign: 'center', padding: '20px' }}>
                <Text type="secondary">加载中...</Text>
              </div>
            )}
          </Card>
        </Col>

        {/* 配置状态 */}
        <Col xs={24} lg={12}>
          <Card 
            title={
              <Space>
                <SettingOutlined />
                配置状态
              </Space>
            }
          >
            {systemStatus?.config_status ? (
              <Descriptions size="small" column={1}>
                <Descriptions.Item label="数据库路径">
                  <Text code>{systemStatus.config_status.database_path}</Text>
                </Descriptions.Item>
                <Descriptions.Item label="集合名称">
                  <Text code>{systemStatus.config_status.collection_name}</Text>
                </Descriptions.Item>
                <Descriptions.Item label="嵌入模型">
                  <Text code>{systemStatus.config_status.embedding_model}</Text>
                </Descriptions.Item>
                <Descriptions.Item label="知识库启用">
                  <Tag color={systemStatus.config_status.knowledge_base_enabled ? 'green' : 'red'}>
                    {systemStatus.config_status.knowledge_base_enabled ? '已启用' : '已禁用'}
                  </Tag>
                </Descriptions.Item>
              </Descriptions>
            ) : (
              <div style={{ textAlign: 'center', padding: '20px' }}>
                <Text type="secondary">加载中...</Text>
              </div>
            )}
          </Card>
        </Col>
      </Row>

      {/* 组件状态 */}
      <Row gutter={[16, 16]} style={{ marginTop: '24px' }}>
        <Col span={24}>
          <Card 
            title={
              <Space>
                <ApiOutlined />
                组件状态
              </Space>
            }
          >
            {systemStatus?.components ? (
              <Row gutter={[16, 16]}>
                {Object.entries(systemStatus.components).map(([component, status]) => (
                  <Col key={component} xs={24} sm={12} md={8} lg={6}>
                    <div style={{ 
                      padding: '12px', 
                      border: '1px solid #d9d9d9', 
                      borderRadius: '6px',
                      textAlign: 'center'
                    }}>
                      <div style={{ marginBottom: '8px' }}>
                        <Badge 
                          status={status ? 'success' : 'error'} 
                          text={status ? '正常' : '异常'} 
                        />
                      </div>
                      <Text strong>{component.replace(/_/g, ' ')}</Text>
                    </div>
                  </Col>
                ))}
              </Row>
            ) : (
              <div style={{ textAlign: 'center', padding: '20px' }}>
                <Text type="secondary">加载中...</Text>
              </div>
            )}
          </Card>
        </Col>
      </Row>

      {/* 健康检查详情 */}
      {propSystemStatus?.details && (
        <Row gutter={[16, 16]} style={{ marginTop: '24px' }}>
          <Col span={24}>
            <Card title="健康检查详情">
              <Descriptions bordered size="small">
                <Descriptions.Item label="服务状态" span={3}>
                  <Badge 
                    status={propSystemStatus.status === 'healthy' ? 'success' : 'error'} 
                    text={propSystemStatus.status} 
                  />
                </Descriptions.Item>
                <Descriptions.Item label="服务名称" span={3}>
                  {propSystemStatus.service}
                </Descriptions.Item>
                {propSystemStatus.error && (
                  <Descriptions.Item label="错误信息" span={3}>
                    <Text type="danger">{propSystemStatus.error}</Text>
                  </Descriptions.Item>
                )}
              </Descriptions>
            </Card>
          </Col>
        </Row>
      )}
    </div>
  );
};

export default StatusTab;
