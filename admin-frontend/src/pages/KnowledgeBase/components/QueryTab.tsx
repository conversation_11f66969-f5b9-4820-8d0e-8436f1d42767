import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Input, 
  Button, 
  Select, 
  Slider, 
  Space, 
  Collapse, 
  Tag, 
  Typography, 
  message,
  Row,
  Col,
  Empty,
  Spin
} from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { knowledgeBaseService, QueryResult, AvailableFilters } from '../../../services/knowledgeBase';

const { TextArea } = Input;
const { Option } = Select;
const { Panel } = Collapse;
const { Text, Paragraph } = Typography;

const QueryTab: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [queryText, setQueryText] = useState('');
  const [queryResults, setQueryResults] = useState<QueryResult[]>([]);
  const [queryInfo, setQueryInfo] = useState<any>(null);
  const [availableFilters, setAvailableFilters] = useState<AvailableFilters>({
    roles: [],
    categories: []
  });
  
  // 查询参数
  const [queryParams, setQueryParams] = useState({
    top_k: 5,
    similarity_threshold: 0.7,
    role_filter: undefined as string | undefined,
  });

  // 获取可用筛选选项
  const fetchAvailableFilters = async () => {
    try {
      const response = await knowledgeBaseService.getAvailableFilters();
      if (response.success) {
        setAvailableFilters(response.data);
      }
    } catch (error) {
      console.error('获取筛选选项失败:', error);
    }
  };

  useEffect(() => {
    fetchAvailableFilters();
  }, []);

  // 执行查询
  const handleQuery = async () => {
    if (!queryText.trim()) {
      message.warning('请输入查询内容');
      return;
    }

    setLoading(true);
    try {
      const response = await knowledgeBaseService.queryKnowledgeBase({
        query_text: queryText,
        ...queryParams,
      });
      
      setQueryResults(response.results);
      setQueryInfo({
        query: response.query,
        total_results: response.total_results,
        parameters: response.parameters,
      });
      
      if (response.results.length === 0) {
        message.info('未找到相关文档');
      } else {
        message.success(`找到 ${response.results.length} 个相关结果`);
      }
    } catch (error) {
      console.error('查询失败:', error);
      message.error('查询失败');
    } finally {
      setLoading(false);
    }
  };

  // 参数变更处理
  const handleParamChange = (key: string, value: any) => {
    setQueryParams(prev => ({ ...prev, [key]: value }));
  };

  // 清除结果
  const handleClearResults = () => {
    setQueryResults([]);
    setQueryInfo(null);
    setQueryText('');
  };

  // 示例查询
  const handleExampleQuery = (example: string) => {
    setQueryText(example);
  };

  const exampleQueries = [
    '如何注册开发者账号？',
    '系统有什么功能？',
    '如何配置API接口？',
    '数据库连接问题怎么解决？',
  ];

  return (
    <div>
      {/* 查询输入区域 */}
      <Card title="查询测试" style={{ marginBottom: '16px' }}>
        <Row gutter={[16, 16]}>
          <Col xs={24} lg={16}>
            <div style={{ marginBottom: '16px' }}>
              <TextArea
                placeholder="输入您的查询内容，例如：如何注册开发者账号？"
                value={queryText}
                onChange={(e) => setQueryText(e.target.value)}
                rows={4}
                maxLength={1000}
                showCount
              />
            </div>
            
            <Space wrap style={{ marginBottom: '16px' }}>
              <Text>示例查询：</Text>
              {exampleQueries.map((example, index) => (
                <Button
                  key={index}
                  size="small"
                  type="link"
                  onClick={() => handleExampleQuery(example)}
                >
                  {example}
                </Button>
              ))}
            </Space>
            
            <Space>
              <Button
                type="primary"
                icon={<SearchOutlined />}
                onClick={handleQuery}
                loading={loading}
                disabled={!queryText.trim()}
              >
                执行查询
              </Button>
              <Button onClick={handleClearResults}>
                清除结果
              </Button>
            </Space>
          </Col>
          
          <Col xs={24} lg={8}>
            <Card title="查询参数" size="small">
              <div style={{ marginBottom: '16px' }}>
                <Text>返回结果数量：</Text>
                <Slider
                  min={1}
                  max={20}
                  value={queryParams.top_k}
                  onChange={(value) => handleParamChange('top_k', value)}
                  marks={{ 1: '1', 5: '5', 10: '10', 20: '20' }}
                />
              </div>
              
              <div style={{ marginBottom: '16px' }}>
                <Text>相似度阈值：</Text>
                <Slider
                  min={0}
                  max={1}
                  step={0.1}
                  value={queryParams.similarity_threshold}
                  onChange={(value) => handleParamChange('similarity_threshold', value)}
                  marks={{ 0: '0', 0.5: '0.5', 1: '1' }}
                />
              </div>
              
              <div>
                <Text>角色筛选：</Text>
                <Select
                  placeholder="选择角色"
                  allowClear
                  style={{ width: '100%', marginTop: '8px' }}
                  value={queryParams.role_filter}
                  onChange={(value) => handleParamChange('role_filter', value)}
                >
                  {availableFilters.roles.map((role: string) => (
                    <Option key={role} value={role}>{role}</Option>
                  ))}
                </Select>
              </div>
            </Card>
          </Col>
        </Row>
      </Card>

      {/* 查询结果区域 */}
      {loading && (
        <Card>
          <div style={{ textAlign: 'center', padding: '50px' }}>
            <Spin size="large" />
            <div style={{ marginTop: '16px' }}>正在查询知识库...</div>
          </div>
        </Card>
      )}

      {queryInfo && !loading && (
        <Card 
          title={`查询结果 - "${queryInfo.query}"`}
          extra={
            <Space>
              <Text type="secondary">
                找到 {queryInfo.total_results} 个结果
              </Text>
              <Tag>
                Top-{queryInfo.parameters.top_k}
              </Tag>
              <Tag>
                阈值 {queryInfo.parameters.similarity_threshold}
              </Tag>
              {queryInfo.parameters.role_filter && (
                <Tag color="blue">
                  {queryInfo.parameters.role_filter}
                </Tag>
              )}
            </Space>
          }
        >
          {queryResults.length > 0 ? (
            <Collapse>
              {queryResults.map((result, index) => (
                <Panel
                  header={
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <span>
                        结果 {result.rank} - {result.metadata.title}
                      </span>
                      <Space>
                        <Tag color="green">
                          相似度: {result.similarity_score}
                        </Tag>
                        <Tag color="blue">
                          {result.metadata.role}
                        </Tag>
                        <Tag>
                          {result.metadata.category}
                        </Tag>
                      </Space>
                    </div>
                  }
                  key={index}
                >
                  <Row gutter={[16, 16]}>
                    <Col xs={24} lg={16}>
                      <div>
                        <Text strong>内容：</Text>
                        <div style={{ 
                          marginTop: '8px', 
                          padding: '12px', 
                          border: '1px solid #d9d9d9', 
                          borderRadius: '6px',
                          backgroundColor: '#fafafa'
                        }}>
                          <Paragraph style={{ margin: 0, whiteSpace: 'pre-wrap' }}>
                            {result.content}
                          </Paragraph>
                        </div>
                      </div>
                    </Col>
                    
                    <Col xs={24} lg={8}>
                      <div>
                        <Text strong>元数据：</Text>
                        <div style={{ marginTop: '8px' }}>
                          <div><Text type="secondary">文档ID:</Text> {result.metadata.doc_id}</div>
                          <div><Text type="secondary">角色:</Text> {result.metadata.role}</div>
                          <div><Text type="secondary">分类:</Text> {result.metadata.category}</div>
                          <div><Text type="secondary">源文件:</Text> {result.metadata.source_path}</div>
                          <div><Text type="secondary">相似度:</Text> {result.similarity_score}</div>
                        </div>
                      </div>
                    </Col>
                  </Row>
                </Panel>
              ))}
            </Collapse>
          ) : (
            <Empty description="未找到相关文档" />
          )}
        </Card>
      )}

      {!queryInfo && !loading && (
        <Card>
          <Empty 
            description="请输入查询内容并点击执行查询开始测试"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        </Card>
      )}
    </div>
  );
};

export default QueryTab;
