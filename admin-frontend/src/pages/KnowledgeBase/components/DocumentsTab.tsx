import React, { useState, useEffect, useCallback } from 'react';
import { 
  Card, 
  Table, 
  Input, 
  Select, 
  Button, 
  Space, 
  Tag, 
  Modal, 
  Typography, 
  message,
  Pagination,
  Row,
  Col
} from 'antd';
import {
  EyeOutlined,
  ReloadOutlined,
  FilterOutlined
} from '@ant-design/icons';
import { knowledgeBaseService, Document, AvailableFilters } from '../../../services/knowledgeBase';

const { Search } = Input;
const { Option } = Select;
const { Text, Paragraph } = Typography;

const DocumentsTab: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [filters, setFilters] = useState({
    role: undefined as string | undefined,
    category: undefined as string | undefined,
    search_term: undefined as string | undefined,
  });
  const [availableFilters, setAvailableFilters] = useState<AvailableFilters>({
    roles: [],
    categories: []
  });
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [modalVisible, setModalVisible] = useState(false);

  // 获取可用筛选选项
  const fetchAvailableFilters = useCallback(async () => {
    try {
      const response = await knowledgeBaseService.getAvailableFilters();
      if (response.success) {
        setAvailableFilters(response.data);
      }
    } catch (error) {
      console.error('获取筛选选项失败:', error);
    }
  }, []);

  // 获取文档列表
  const fetchDocuments = useCallback(async () => {
    setLoading(true);
    try {
      const response = await knowledgeBaseService.getDocuments({
        ...filters,
        page: currentPage,
        limit: pageSize,
      });
      setDocuments(response.documents);
      setTotal(response.total);
    } catch (error) {
      console.error('获取文档列表失败:', error);
      message.error('获取文档列表失败');
    } finally {
      setLoading(false);
    }
  }, [filters, currentPage, pageSize]);

  useEffect(() => {
    fetchAvailableFilters();
  }, [fetchAvailableFilters]);

  useEffect(() => {
    fetchDocuments();
  }, [fetchDocuments]);

  // 处理搜索
  const handleSearch = (value: string) => {
    setFilters(prev => ({ ...prev, search_term: value || undefined }));
    setCurrentPage(1);
  };

  // 处理筛选
  const handleFilterChange = (key: string, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setCurrentPage(1);
  };

  // 清除筛选
  const handleClearFilters = () => {
    setFilters({
      role: undefined,
      category: undefined,
      search_term: undefined,
    });
    setCurrentPage(1);
  };

  // 查看文档详情
  const handleViewDocument = (document: Document) => {
    setSelectedDocument(document);
    setModalVisible(true);
  };

  // 刷新数据
  const handleRefresh = () => {
    fetchDocuments();
    message.success('数据已刷新');
  };

  // 表格列定义
  const columns = [
    {
      title: '文档标题',
      dataIndex: 'title',
      key: 'title',
      width: 200,
      ellipsis: true,
      render: (text: string, record: Document) => (
        <Button 
          type="link" 
          onClick={() => handleViewDocument(record)}
          style={{ padding: 0, height: 'auto' }}
        >
          {text || '未命名文档'}
        </Button>
      ),
    },
    {
      title: '内容预览',
      dataIndex: 'content_preview',
      key: 'content_preview',
      ellipsis: true,
      render: (text: string) => (
        <Text ellipsis={{ tooltip: text }} style={{ maxWidth: 300 }}>
          {text}
        </Text>
      ),
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      width: 100,
      render: (role: string) => (
        <Tag color={role === 'company' ? 'blue' : role === 'developer' ? 'green' : 'orange'}>
          {role}
        </Tag>
      ),
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      width: 120,
      ellipsis: true,
    },
    {
      title: '块索引',
      dataIndex: 'chunk_index',
      key: 'chunk_index',
      width: 80,
      align: 'center' as const,
    },
    {
      title: '操作',
      key: 'actions',
      width: 100,
      render: (_: any, record: Document) => (
        <Button
          type="link"
          icon={<EyeOutlined />}
          onClick={() => handleViewDocument(record)}
        >
          查看
        </Button>
      ),
    },
  ];

  return (
    <div>
      {/* 筛选和搜索区域 */}
      <Card style={{ marginBottom: '16px' }}>
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={12} md={8}>
            <Search
              placeholder="搜索文档内容"
              allowClear
              onSearch={handleSearch}
              style={{ width: '100%' }}
            />
          </Col>
          <Col xs={24} sm={6} md={4}>
            <Select
              placeholder="选择角色"
              allowClear
              style={{ width: '100%' }}
              value={filters.role}
              onChange={(value) => handleFilterChange('role', value)}
            >
              {availableFilters.roles.map((role: string) => (
                <Option key={role} value={role}>{role}</Option>
              ))}
            </Select>
          </Col>
          <Col xs={24} sm={6} md={4}>
            <Select
              placeholder="选择分类"
              allowClear
              style={{ width: '100%' }}
              value={filters.category}
              onChange={(value) => handleFilterChange('category', value)}
            >
              {availableFilters.categories.map((category: string) => (
                <Option key={category} value={category}>{category}</Option>
              ))}
            </Select>
          </Col>
          <Col xs={24} sm={12} md={8}>
            <Space>
              <Button 
                icon={<FilterOutlined />} 
                onClick={handleClearFilters}
              >
                清除筛选
              </Button>
              <Button 
                icon={<ReloadOutlined />} 
                onClick={handleRefresh}
              >
                刷新
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 文档列表 */}
      <Card title={`文档列表 (共 ${total} 条)`}>
        <Table
          columns={columns}
          dataSource={documents}
          rowKey="id"
          loading={loading}
          pagination={false}
          scroll={{ x: 800 }}
        />
        
        {/* 分页 */}
        <div style={{ marginTop: '16px', textAlign: 'right' }}>
          <Pagination
            current={currentPage}
            pageSize={pageSize}
            total={total}
            showSizeChanger
            showQuickJumper
            showTotal={(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`}
            onChange={(page, size) => {
              setCurrentPage(page);
              setPageSize(size || 20);
            }}
          />
        </div>
      </Card>

      {/* 文档详情模态框 */}
      <Modal
        title={`文档详情 - ${selectedDocument?.title || '未命名文档'}`}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedDocument && (
          <div>
            <Row gutter={[16, 16]} style={{ marginBottom: '16px' }}>
              <Col span={12}>
                <Text strong>文档ID:</Text> {selectedDocument.doc_id}
              </Col>
              <Col span={12}>
                <Text strong>角色:</Text> <Tag color="blue">{selectedDocument.role}</Tag>
              </Col>
              <Col span={12}>
                <Text strong>分类:</Text> {selectedDocument.category}
              </Col>
              <Col span={12}>
                <Text strong>块索引:</Text> {selectedDocument.chunk_index}
              </Col>
              <Col span={24}>
                <Text strong>源文件:</Text> {selectedDocument.source_path}
              </Col>
            </Row>
            
            <div style={{ marginTop: '16px' }}>
              <Text strong>文档内容:</Text>
              <div style={{ 
                marginTop: '8px', 
                padding: '12px', 
                border: '1px solid #d9d9d9', 
                borderRadius: '6px',
                backgroundColor: '#fafafa',
                maxHeight: '400px',
                overflow: 'auto'
              }}>
                <Paragraph style={{ margin: 0, whiteSpace: 'pre-wrap' }}>
                  {selectedDocument.content}
                </Paragraph>
              </div>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default DocumentsTab;
