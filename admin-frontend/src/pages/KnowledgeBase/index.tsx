import React, { useState, useEffect } from 'react';
import { Card, Tabs, message, Spin } from 'antd';
import { 
  DatabaseOutlined, 
  SearchOutlined, 
  BarChartOutlined, 
  SettingOutlined,
  FileTextOutlined 
} from '@ant-design/icons';

import OverviewTab from './components/OverviewTab';
import DocumentsTab from './components/DocumentsTab';
import QueryTab from './components/QueryTab';
import StatisticsTab from './components/StatisticsTab';
import StatusTab from './components/StatusTab';
import { knowledgeBaseService } from '../../services/knowledgeBase';

const { TabPane } = Tabs;

const KnowledgeBase: React.FC = () => {
  const [loading] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [systemStatus, setSystemStatus] = useState<any>(null);

  // 检查系统状态
  const checkSystemStatus = async () => {
    try {
      const health = await knowledgeBaseService.healthCheck();
      setSystemStatus(health);
      
      if (health.status !== 'healthy') {
        message.warning('知识库服务状态异常，部分功能可能不可用');
      }
    } catch (error) {
      console.error('检查系统状态失败:', error);
      message.error('无法连接到知识库服务');
    }
  };

  useEffect(() => {
    checkSystemStatus();
  }, []);

  const handleTabChange = (key: string) => {
    setActiveTab(key);
  };

  const handleRefresh = () => {
    checkSystemStatus();
    message.success('状态已刷新');
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card 
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <DatabaseOutlined />
            <span>知识库管理</span>
            {systemStatus && (
              <span 
                style={{ 
                  marginLeft: '16px',
                  padding: '2px 8px',
                  borderRadius: '4px',
                  fontSize: '12px',
                  backgroundColor: systemStatus.status === 'healthy' ? '#f6ffed' : '#fff2e8',
                  color: systemStatus.status === 'healthy' ? '#52c41a' : '#fa8c16',
                  border: `1px solid ${systemStatus.status === 'healthy' ? '#b7eb8f' : '#ffd591'}`
                }}
              >
                {systemStatus.status === 'healthy' ? '服务正常' : '服务异常'}
              </span>
            )}
          </div>
        }
        extra={
          <span 
            onClick={handleRefresh}
            style={{ 
              cursor: 'pointer', 
              color: '#1890ff',
              fontSize: '14px'
            }}
          >
            刷新状态
          </span>
        }
      >
        <Spin spinning={loading}>
          <Tabs 
            activeKey={activeTab} 
            onChange={handleTabChange}
            type="card"
          >
            <TabPane 
              tab={
                <span>
                  <BarChartOutlined />
                  概览
                </span>
              } 
              key="overview"
            >
              <OverviewTab />
            </TabPane>

            <TabPane 
              tab={
                <span>
                  <FileTextOutlined />
                  文档管理
                </span>
              } 
              key="documents"
            >
              <DocumentsTab />
            </TabPane>

            <TabPane 
              tab={
                <span>
                  <SearchOutlined />
                  查询测试
                </span>
              } 
              key="query"
            >
              <QueryTab />
            </TabPane>

            <TabPane 
              tab={
                <span>
                  <BarChartOutlined />
                  统计分析
                </span>
              } 
              key="statistics"
            >
              <StatisticsTab />
            </TabPane>

            <TabPane 
              tab={
                <span>
                  <SettingOutlined />
                  系统状态
                </span>
              } 
              key="status"
            >
              <StatusTab systemStatus={systemStatus} onRefresh={handleRefresh} />
            </TabPane>
          </Tabs>
        </Spin>
      </Card>
    </div>
  );
};

export default KnowledgeBase;
