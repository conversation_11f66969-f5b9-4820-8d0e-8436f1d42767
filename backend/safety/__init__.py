#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全模块 - Safety Module

此模块提供系统安全相关的功能，包括：
- 内容审查：用户输入内容的安全检查和过滤
- 安全管理：系统运行时的安全监控和降级
- 审计日志：安全事件的记录和分析

主要组件：
- ContentModerator: 内容审查器
- SafetyManager: 安全管理器（已存在于 utils 中）
"""

from .content_moderation import (
    ContentModerator,
    ContentModerationResult,
    ModerationAction,
    get_content_moderator
)

__all__ = [
    'ContentModerator',
    'ContentModerationResult', 
    'ModerationAction',
    'get_content_moderator'
]
