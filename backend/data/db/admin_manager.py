"""
后台管理系统数据库管理器
"""
import sqlite3
import hashlib
import secrets
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any, Tuple
from backend.data.db.database_manager import DatabaseManager
from backend.models.admin import (
    AdminRole, AdminStatus, UserStatus,
    AdminUserInfo, UserInfo, ConversationInfo,
    ConfigItem, OperationLog
)
import logging

logger = logging.getLogger(__name__)


class AdminDatabaseManager:
    """后台管理系统数据库管理器"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)
    
    async def init_admin_tables(self):
        """初始化管理员相关数据表"""
        async with self.db_manager.get_connection() as conn:
            # 管理员用户表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS admin_users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username VARCHAR(50) UNIQUE NOT NULL,
                    password_hash VARCHAR(255) NOT NULL,
                    salt VARCHAR(32) NOT NULL,
                    role VARCHAR(20) NOT NULL DEFAULT 'customer_service',
                    email VARCHAR(100),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP,
                    status VARCHAR(10) DEFAULT 'active'
                )
            """)
            
            # 系统配置表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS system_configs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    config_key VARCHAR(100) UNIQUE NOT NULL,
                    config_value TEXT NOT NULL,
                    config_type VARCHAR(20) NOT NULL DEFAULT 'string',
                    category VARCHAR(50) NOT NULL DEFAULT 'general',
                    description TEXT,
                    updated_by INTEGER,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (updated_by) REFERENCES admin_users(id)
                )
            """)
            
            # 操作日志表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS admin_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    admin_id INTEGER NOT NULL,
                    action VARCHAR(100) NOT NULL,
                    resource VARCHAR(100),
                    details TEXT,
                    ip_address VARCHAR(45),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (admin_id) REFERENCES admin_users(id)
                )
            """)
            
            conn.commit()
            
            # 创建默认超级管理员账户
            await self._create_default_admin(conn)
    
    async def _create_default_admin(self, conn: sqlite3.Connection):
        """创建默认超级管理员账户"""
        try:
            # 检查是否已存在超级管理员
            cursor = conn.execute(
                "SELECT COUNT(*) FROM admin_users WHERE role = ?",
                (AdminRole.SUPER_ADMIN.value,)
            )
            count = cursor.fetchone()[0]
            
            if count == 0:
                # 创建默认超级管理员
                salt = secrets.token_hex(16)
                password_hash = self._hash_password("admin123", salt)
                
                conn.execute("""
                    INSERT INTO admin_users (username, password_hash, salt, role, email)
                    VALUES (?, ?, ?, ?, ?)
                """, ("admin", password_hash, salt, AdminRole.SUPER_ADMIN.value, "<EMAIL>"))
                
                conn.commit()
                self.logger.info("默认超级管理员账户已创建: admin/admin123")
        except Exception as e:
            self.logger.error(f"创建默认管理员账户失败: {e}")
    
    def _hash_password(self, password: str, salt: str) -> str:
        """密码哈希"""
        return hashlib.pbkdf2_hex(password.encode(), salt.encode(), 100000)
    
    def _verify_password(self, password: str, salt: str, password_hash: str) -> bool:
        """验证密码"""
        return self._hash_password(password, salt) == password_hash
    
    # ============================================================================
    # 认证相关方法
    # ============================================================================
    
    async def authenticate_admin(self, username: str, password: str) -> Optional[AdminUserInfo]:
        """管理员认证"""
        async with self.db_manager.get_connection() as conn:
            cursor = conn.execute("""
                SELECT id, username, password_hash, salt, role, email, created_at, last_login, status
                FROM admin_users 
                WHERE username = ? AND status = 'active'
            """, (username,))
            
            row = cursor.fetchone()
            if not row:
                return None
            
            # 验证密码
            if not self._verify_password(password, row[3], row[2]):
                return None
            
            # 更新最后登录时间
            conn.execute(
                "UPDATE admin_users SET last_login = CURRENT_TIMESTAMP WHERE id = ?",
                (row[0],)
            )
            conn.commit()
            
            return AdminUserInfo(
                id=row[0],
                username=row[1],
                role=AdminRole(row[4]),
                email=row[5],
                created_at=datetime.fromisoformat(row[6]),
                last_login=datetime.fromisoformat(row[7]) if row[7] else None,
                status=AdminStatus(row[8])
            )
    
    async def get_admin_by_id(self, admin_id: int) -> Optional[AdminUserInfo]:
        """根据ID获取管理员信息"""
        async with self.db_manager.get_connection() as conn:
            cursor = conn.execute("""
                SELECT id, username, role, email, created_at, last_login, status
                FROM admin_users 
                WHERE id = ?
            """, (admin_id,))
            
            row = cursor.fetchone()
            if not row:
                return None
            
            return AdminUserInfo(
                id=row[0],
                username=row[1],
                role=AdminRole(row[2]),
                email=row[3],
                created_at=datetime.fromisoformat(row[4]),
                last_login=datetime.fromisoformat(row[5]) if row[5] else None,
                status=AdminStatus(row[6])
            )
    
    # ============================================================================
    # 用户管理相关方法
    # ============================================================================
    
    async def get_users_list(self, page: int = 1, limit: int = 20, 
                           search: Optional[str] = None, 
                           status: Optional[UserStatus] = None) -> Tuple[List[UserInfo], int]:
        """获取用户列表"""
        async with self.db_manager.get_connection() as conn:
            # 构建查询条件
            where_conditions = []
            params = []
            
            if search:
                where_conditions.append("user_id LIKE ?")
                params.append(f"%{search}%")
            
            if status:
                # 注意：这里需要根据实际的用户状态字段调整
                where_conditions.append("status = ?")
                params.append(status.value)
            
            where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
            
            # 获取总数
            count_cursor = conn.execute(f"""
                SELECT COUNT(DISTINCT user_id) FROM conversations 
                WHERE {where_clause}
            """, params)
            total = count_cursor.fetchone()[0]
            
            # 获取用户列表（从conversations表中获取用户信息）
            offset = (page - 1) * limit
            cursor = conn.execute(f"""
                SELECT 
                    user_id,
                    MIN(created_at) as first_conversation,
                    MAX(updated_at) as last_active,
                    COUNT(*) as conversation_count
                FROM conversations 
                WHERE {where_clause}
                GROUP BY user_id
                ORDER BY last_active DESC
                LIMIT ? OFFSET ?
            """, params + [limit, offset])
            
            users = []
            for row in cursor.fetchall():
                # 获取文档数量
                doc_cursor = conn.execute(
                    "SELECT COUNT(*) FROM documents WHERE user_id = ?",
                    (row[0],)
                )
                document_count = doc_cursor.fetchone()[0]
                
                users.append(UserInfo(
                    id=row[0],
                    created_at=datetime.fromisoformat(row[1]),
                    last_active=datetime.fromisoformat(row[2]) if row[2] else None,
                    status=UserStatus.ACTIVE,  # 默认状态，可以后续扩展
                    conversation_count=row[3],
                    document_count=document_count
                ))
            
            return users, total
    
    async def get_user_conversations(self, user_id: str, page: int = 1, 
                                   limit: int = 10) -> Tuple[List[ConversationInfo], int]:
        """获取用户对话历史"""
        async with self.db_manager.get_connection() as conn:
            # 获取总数
            count_cursor = conn.execute(
                "SELECT COUNT(*) FROM conversations WHERE user_id = ?",
                (user_id,)
            )
            total = count_cursor.fetchone()[0]
            
            # 获取对话列表
            offset = (page - 1) * limit
            cursor = conn.execute("""
                SELECT id, user_id, created_at, updated_at, status, domain
                FROM conversations 
                WHERE user_id = ?
                ORDER BY created_at DESC
                LIMIT ? OFFSET ?
            """, (user_id, limit, offset))
            
            conversations = []
            for row in cursor.fetchall():
                # 获取消息数量
                msg_cursor = conn.execute(
                    "SELECT COUNT(*) FROM messages WHERE conversation_id = ?",
                    (row[0],)
                )
                message_count = msg_cursor.fetchone()[0]
                
                conversations.append(ConversationInfo(
                    id=row[0],
                    user_id=row[1],
                    created_at=datetime.fromisoformat(row[2]),
                    updated_at=datetime.fromisoformat(row[3]),
                    status=row[4],
                    message_count=message_count,
                    domain=row[5]
                ))
            
            return conversations, total
    
    # ============================================================================
    # 配置管理相关方法
    # ============================================================================
    
    async def get_configs(self, category: Optional[str] = None) -> List[ConfigItem]:
        """获取系统配置"""
        async with self.db_manager.get_connection() as conn:
            if category:
                cursor = conn.execute("""
                    SELECT config_key, config_value, config_type, category, 
                           description, updated_at, updated_by
                    FROM system_configs 
                    WHERE category = ?
                    ORDER BY config_key
                """, (category,))
            else:
                cursor = conn.execute("""
                    SELECT config_key, config_value, config_type, category, 
                           description, updated_at, updated_by
                    FROM system_configs 
                    ORDER BY category, config_key
                """)
            
            configs = []
            for row in cursor.fetchall():
                configs.append(ConfigItem(
                    key=row[0],
                    value=row[1],
                    type=row[2],
                    category=row[3],
                    description=row[4],
                    updated_at=datetime.fromisoformat(row[5]),
                    updated_by=row[6]
                ))
            
            return configs
    
    async def update_config(self, key: str, value: str, admin_id: int, 
                          description: Optional[str] = None) -> bool:
        """更新系统配置"""
        async with self.db_manager.get_connection() as conn:
            try:
                conn.execute("""
                    INSERT OR REPLACE INTO system_configs 
                    (config_key, config_value, description, updated_by, updated_at)
                    VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
                """, (key, value, description, admin_id))
                conn.commit()
                return True
            except Exception as e:
                self.logger.error(f"更新配置失败: {e}")
                return False
    
    # ============================================================================
    # 操作日志相关方法
    # ============================================================================
    
    async def log_admin_operation(self, admin_id: int, action: str, 
                                resource: Optional[str] = None,
                                details: Optional[str] = None,
                                ip_address: Optional[str] = None):
        """记录管理员操作日志"""
        async with self.db_manager.get_connection() as conn:
            try:
                conn.execute("""
                    INSERT INTO admin_logs (admin_id, action, resource, details, ip_address)
                    VALUES (?, ?, ?, ?, ?)
                """, (admin_id, action, resource, details, ip_address))
                conn.commit()
            except Exception as e:
                self.logger.error(f"记录操作日志失败: {e}")
    
    async def get_operation_logs(self, page: int = 1, limit: int = 50,
                               admin_id: Optional[int] = None,
                               action: Optional[str] = None) -> Tuple[List[OperationLog], int]:
        """获取操作日志"""
        async with self.db_manager.get_connection() as conn:
            # 构建查询条件
            where_conditions = []
            params = []
            
            if admin_id:
                where_conditions.append("al.admin_id = ?")
                params.append(admin_id)
            
            if action:
                where_conditions.append("al.action LIKE ?")
                params.append(f"%{action}%")
            
            where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
            
            # 获取总数
            count_cursor = conn.execute(f"""
                SELECT COUNT(*) FROM admin_logs al 
                WHERE {where_clause}
            """, params)
            total = count_cursor.fetchone()[0]
            
            # 获取日志列表
            offset = (page - 1) * limit
            cursor = conn.execute(f"""
                SELECT al.id, al.admin_id, au.username, al.action, 
                       al.resource, al.details, al.ip_address, al.created_at
                FROM admin_logs al
                JOIN admin_users au ON al.admin_id = au.id
                WHERE {where_clause}
                ORDER BY al.created_at DESC
                LIMIT ? OFFSET ?
            """, params + [limit, offset])
            
            logs = []
            for row in cursor.fetchall():
                logs.append(OperationLog(
                    id=row[0],
                    admin_id=row[1],
                    admin_username=row[2],
                    action=row[3],
                    resource=row[4],
                    details=row[5],
                    ip_address=row[6],
                    created_at=datetime.fromisoformat(row[7])
                ))
            
            return logs, total
