#!/usr/bin/env python3
"""
统一配置加载器
负责加载和管理unified_config.yaml中的所有配置
"""

import yaml
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
import threading


@dataclass
class LLMConfig:
    """LLM配置数据类"""
    model: str
    temperature: float
    max_tokens: int
    timeout: int = 10
    description: str = ""

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "model": self.model,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "timeout": self.timeout,
            "description": self.description
        }


@dataclass
class ConversationState:
    """对话状态配置"""
    name: str
    transitions: Dict[str, str]


class UnifiedConfigLoader:
    """统一配置加载器"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        self.logger = logging.getLogger(__name__)
        self._config = {}
        self._config_path = Path(__file__).parent / "unified_config.yaml"
        self._load_config()
        self._initialized = True
    
    def _load_config(self):
        """加载配置文件"""
        try:
            with open(self._config_path, 'r', encoding='utf-8') as f:
                raw_config = yaml.safe_load(f)

            # 解析环境变量
            self._config = self._resolve_env_variables(raw_config)
            self.logger.info(f"统一配置加载成功: {self._config_path}")
        except Exception as e:
            self.logger.error(f"加载统一配置失败: {e}")
            self._config = {}

    def _resolve_env_variables(self, config):
        """递归解析配置中的环境变量占位符"""
        import os
        import re

        if isinstance(config, dict):
            return {key: self._resolve_env_variables(value) for key, value in config.items()}
        elif isinstance(config, list):
            return [self._resolve_env_variables(item) for item in config]
        elif isinstance(config, str):
            # 匹配 ${VAR_NAME} 格式的环境变量
            def replace_env_var(match):
                var_name = match.group(1)
                env_value = os.getenv(var_name)
                if env_value is None:
                    self.logger.warning(f"环境变量 {var_name} 未设置，保持原值")
                    return match.group(0)  # 返回原始占位符
                return env_value

            return re.sub(r'\$\{([^}]+)\}', replace_env_var, config)
        else:
            return config

    def reload(self):
        """重新加载配置"""
        self._load_config()
        self.logger.info("统一配置已重新加载")
    
    def get_system_config(self) -> Dict[str, Any]:
        """获取系统配置"""
        return self._config.get('system', {})
    
    def get_llm_config(self, config_type: str = 'default') -> LLMConfig:
        """获取LLM配置"""
        llm_configs = self._config.get('llm', {})
        
        # 获取默认配置
        default_config = llm_configs.get('default', {})
        
        # 获取特定配置
        if config_type != 'default':
            specific_config = llm_configs.get('configurations', {}).get(config_type, {})
            # 合并配置，特定配置覆盖默认配置
            config = {**default_config, **specific_config}
        else:
            config = default_config
        
        return LLMConfig(
            model=config.get('model', 'gpt-3.5-turbo'),
            temperature=config.get('temperature', 0.7),
            max_tokens=config.get('max_tokens', 1000),
            timeout=config.get('timeout', 10),
            description=config.get('description', '')
        )
    
    def get_available_llm_types(self) -> List[str]:
        """获取可用的LLM配置类型"""
        llm_configs = self._config.get('llm', {})
        configurations = llm_configs.get('configurations', {})
        return ['default'] + list(configurations.keys())
    
    def get_conversation_config(self) -> Dict[str, Any]:
        """获取对话配置"""
        return self._config.get('conversation', {})
    
    def get_conversation_states(self) -> List[str]:
        """获取可用的对话状态"""
        conv_config = self.get_conversation_config()
        return conv_config.get('states', {}).get('available', ['IDLE'])
    
    def get_state_transitions(self, state: str) -> Dict[str, str]:
        """获取状态转换规则"""
        conv_config = self.get_conversation_config()
        transitions = conv_config.get('transitions', {})
        return transitions.get(state, {})
    
    def get_keyword_rules(self) -> Dict[str, Dict[str, Any]]:
        """获取关键词加速规则"""
        conv_config = self.get_conversation_config()
        keyword_config = conv_config.get('keyword_acceleration', {})
        return keyword_config.get('rules', {})
    
    def is_keyword_acceleration_enabled(self) -> bool:
        """检查关键词加速是否启用"""
        conv_config = self.get_conversation_config()
        keyword_config = conv_config.get('keyword_acceleration', {})
        return keyword_config.get('enabled', True)
    
    def get_business_rules(self) -> Dict[str, Any]:
        """获取业务规则配置"""
        return self._config.get('business_rules', {})

    def get_business_rule(self, key: str, default: Any = None) -> Any:
        """
        获取特定业务规则，支持点分割路径
        例如: get_business_rule('retry.max_pending_attempts')
        """
        business_rules = self.get_business_rules()

        # 支持点分割路径
        if '.' in key:
            keys = key.split('.')
            value = business_rules
            try:
                for k in keys:
                    value = value[k]
                return value
            except (KeyError, TypeError):
                return default
        else:
            return business_rules.get(key, default)
    
    def get_retry_config(self) -> Dict[str, Any]:
        """获取重试配置"""
        business_rules = self.get_business_rules()
        return business_rules.get('retry', {})
    
    def get_confirmation_keywords(self) -> List[str]:
        """获取确认关键词"""
        business_rules = self.get_business_rules()
        doc_config = business_rules.get('document_confirmation', {})
        return doc_config.get('confirmation_keywords', [])
    
    def get_message_templates(self) -> Dict[str, Any]:
        """获取消息模板"""
        return self._config.get('message_templates', {})
    
    def get_template(self, category: str, template_type: str = 'main') -> str:
        """获取特定模板"""
        templates = self.get_message_templates()
        category_templates = templates.get(category, {})
        
        if isinstance(category_templates, dict):
            return category_templates.get(template_type, '')
        else:
            return str(category_templates)
    
    def get_database_config(self) -> Dict[str, Any]:
        """获取数据库配置"""
        return self._config.get('database', {})
    
    def get_database_path(self) -> str:
        """获取数据库路径"""
        db_config = self.get_database_config()
        connection_config = db_config.get('connection', {})
        return connection_config.get('path', 'backend/data/aidatabase.db')

    def get_database_query(self, query_path: str) -> str:
        """获取数据库查询语句"""
        # 解析查询路径，如 "messages.get_conversation_history_limited"
        path_parts = query_path.split('.')
        db_config = self.get_database_config()
        queries = db_config.get('queries', {})

        current = queries
        for part in path_parts:
            if isinstance(current, dict) and part in current:
                current = current[part]
            else:
                return ""

        return current.strip() if isinstance(current, str) else ""
    
    def get_performance_config(self) -> Dict[str, Any]:
        """获取性能配置"""
        return self._config.get('performance', {})
    
    def get_cache_config(self) -> Dict[str, Any]:
        """获取缓存配置"""
        perf_config = self.get_performance_config()
        return perf_config.get('cache', {})
    
    def is_cache_enabled(self) -> bool:
        """检查缓存是否启用"""
        cache_config = self.get_cache_config()
        return cache_config.get('enabled', True)

    def get_scenario_params(self, agent_name: str) -> dict:
        """获取场景参数

        Args:
            agent_name: Agent名称

        Returns:
            dict: 场景参数字典
        """
        # 首先尝试从 message_config.generators 中获取
        generators = self._config.get('message_config', {}).get('generators', {})
        if agent_name in generators:
            generator_config = generators[agent_name]
            return {
                'temperature': generator_config.get('temperature', 0.7),
                'max_tokens': generator_config.get('max_tokens', 200),
                'timeout': 30  # 默认超时时间
            }

        # 如果没有找到，返回默认参数
        return {
            'temperature': 0.7,
            'max_tokens': 200,
            'timeout': 30
        }

    def add_change_listener(self, *args, **kwargs):
        """添加配置变更监听器（兼容性方法）"""
        # 暂时不实现，只是为了兼容性
        pass

    def get_threshold(self, path: str, default: Any = None) -> Any:
        """获取阈值配置（兼容性方法）"""
        return self.get_config_value(path, default)

    def get_message_template(self, template_path: str, default: str = None, **kwargs) -> str:
        """获取消息模板（兼容性方法）"""
        # 解析模板路径，如 "system.greeting.welcome"
        path_parts = template_path.split('.')
        templates = self.get_message_templates()

        current = templates
        for part in path_parts:
            if isinstance(current, dict) and part in current:
                current = current[part]
            else:
                return default or ""

        template = current if isinstance(current, str) else (default or "")

        # 如果有kwargs参数，尝试格式化模板
        if kwargs and template:
            try:
                return template.format(**kwargs)
            except (KeyError, ValueError):
                # 如果格式化失败，返回原模板
                return template

        return template

    def _get_config(self, key: str, default=None):
        """内部配置获取方法（兼容性方法）"""
        return self.get_config_value(key, default)

    def get_message_config(self):
        """获取消息配置（兼容性方法）"""
        return self.get_message_templates()

    def get_model_config(self, agent_name: str = None, model_name: str = None) -> Dict[str, Any]:
        """获取模型配置（支持场景映射）"""
        # 获取原始配置数据
        llm_configs = self._config.get('llm', {})
        models = llm_configs.get('models', {})
        scenario_mapping = llm_configs.get('scenario_mapping', {})
        scenario_params = llm_configs.get('scenario_params', {})
        default_config = scenario_params.get('default', {})

        # 确定要使用的模型名称
        target_model_name = model_name

        # 如果指定了agent_name，先从场景映射中查找对应的模型
        if agent_name and not target_model_name:
            target_model_name = scenario_mapping.get(agent_name)

        # 如果找到了目标模型名称，获取模型的基础配置
        if target_model_name and target_model_name in models:
            # 从models中获取基础配置
            base_model_config = models[target_model_name].copy()

            # 获取场景特定的参数配置
            scenario_specific_params = {}
            if agent_name and agent_name in scenario_params:
                scenario_specific_params = scenario_params[agent_name]

            # 合并配置：基础模型配置 + 场景特定参数
            final_config = {**base_model_config, **scenario_specific_params}
            return final_config

        # 如果没有找到特定模型，但有场景特定参数，使用默认配置+场景参数
        if agent_name and agent_name in scenario_params:
            scenario_specific_params = scenario_params[agent_name]
            return {**default_config, **scenario_specific_params}

        # 返回默认配置
        return default_config
    
    def get_security_config(self) -> Dict[str, Any]:
        """获取安全配置"""
        return self._config.get('security', {})
    
    def get_input_validation_config(self) -> Dict[str, Any]:
        """获取输入验证配置"""
        security_config = self.get_security_config()
        return security_config.get('input_validation', {})
    
    def is_input_validation_enabled(self) -> bool:
        """检查输入验证是否启用"""
        validation_config = self.get_input_validation_config()
        return validation_config.get('enabled', True)
    
    def get_max_input_length(self) -> int:
        """获取最大输入长度"""
        validation_config = self.get_input_validation_config()
        return validation_config.get('max_length', 10000)
    
    def get_development_config(self) -> Dict[str, Any]:
        """获取开发配置"""
        return self._config.get('development', {})
    
    def is_debug_mode(self) -> bool:
        """检查是否为调试模式"""
        system_config = self.get_system_config()
        return system_config.get('debug_mode', False)
    
    def get_config_section(self, section: str) -> Dict[str, Any]:
        """获取指定配置段"""
        return self._config.get(section, {})
    
    def get_config_value(self, path: str, default: Any = None) -> Any:
        """
        使用点分路径获取配置值
        例如: get_config_value('llm.default.temperature')
        """
        keys = path.split('.')
        value = self._config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def get_config_status(self) -> Dict[str, Any]:
        """获取配置状态信息"""
        return {
            "config_file": str(self._config_path),
            "file_exists": self._config_path.exists(),
            "config_loaded": bool(self._config),
            "sections": list(self._config.keys()) if self._config else [],
            "system_version": self.get_config_value('system.version', 'unknown'),
            "last_updated": self.get_config_value('system.last_updated', 'unknown')
        }


# 创建全局实例
unified_config_loader = UnifiedConfigLoader()


def get_unified_config() -> UnifiedConfigLoader:
    """获取统一配置加载器实例"""
    return unified_config_loader


# 便捷函数
def get_llm_config(config_type: str = 'default') -> LLMConfig:
    """获取LLM配置的便捷函数"""
    return unified_config_loader.get_llm_config(config_type)


def get_system_config() -> Dict[str, Any]:
    """获取系统配置的便捷函数"""
    return unified_config_loader.get_system_config()


def get_conversation_config() -> Dict[str, Any]:
    """获取对话配置的便捷函数"""
    return unified_config_loader.get_conversation_config()


def get_business_rules() -> Dict[str, Any]:
    """获取业务规则的便捷函数"""
    return unified_config_loader.get_business_rules()


def get_message_templates() -> Dict[str, Any]:
    """获取消息模板的便捷函数"""
    return unified_config_loader.get_message_templates()


if __name__ == "__main__":
    # 测试配置加载器
    loader = get_unified_config()
    
    print("🔧 统一配置加载器测试")
    print(f"配置状态: {loader.get_config_status()}")
    print(f"可用LLM类型: {loader.get_available_llm_types()}")
    print(f"对话状态: {loader.get_conversation_states()}")
    print(f"关键词加速启用: {loader.is_keyword_acceleration_enabled()}")
    print(f"缓存启用: {loader.is_cache_enabled()}")
    
    # 测试LLM配置
    default_llm = loader.get_llm_config('default')
    print(f"默认LLM配置: {default_llm}")
    
    domain_llm = loader.get_llm_config('domain_classification')
    print(f"领域分类LLM配置: {domain_llm}")
