#!/usr/bin/env python3
"""
统一配置访问接口

提供一个简洁、统一的配置访问层，隐藏底层配置管理的复杂性。
所有模块都应该通过这个接口访问配置，而不是直接导入多个配置管理器。

功能：
1. 统一的配置访问API
2. 自动配置来源选择（YAML文件 vs 动态配置）
3. 配置缓存和性能优化
4. 配置变更监听
5. 向后兼容的接口设计

使用方式：
```python
from backend.config import config_service

# 获取LLM配置
llm_config = config_service.get_llm_config("intent_recognition")

# 获取业务规则
retry_limit = config_service.get_business_rule("retry.max_pending_attempts", 3)

# 获取消息模板
template = config_service.get_message_template("greeting.basic")

# 获取阈值配置
threshold = config_service.get_threshold("business_rules.requirement_collection.completion_threshold", 0.7)

# 监听配置变更
config_service.add_change_listener("keyword_config", my_callback)
```
"""

import logging
from typing import Dict, Any, Optional, Callable, Union
import threading

# 导入统一配置管理器
from .unified_config_loader import get_unified_config
from .unified_dynamic_config import dynamic_keyword_config
from . import settings


class ConfigurationService:
    """统一配置服务
    
    提供简洁统一的配置访问接口，隐藏底层实现复杂性
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._cache = {}
        self._cache_lock = threading.RLock()
        self._change_listeners = {}
        
        # 配置来源优先级: 动态配置 > 静态配置 > 默认值
        self.logger.info("统一配置服务初始化完成")
    
    # ==================== LLM配置相关 ====================
    
    def get_llm_config(self, scenario: str) -> Dict[str, Any]:
        """
        获取LLM配置，返回AutoGen兼容格式

        Args:
            scenario: 场景名称（如 "intent_recognition", "document_generation"）

        Returns:
            AutoGen兼容的LLM配置字典
        """
        try:
            # 直接从统一配置获取配置
            unified_config = get_unified_config()
            base_config = unified_config.get_model_config(agent_name=scenario)

            # 转换为AutoGen期望的格式
            return self._convert_to_autogen_format(base_config)
        except Exception as e:
            self.logger.error(f"获取LLM配置失败: {scenario}, 错误: {e}")
            # 返回默认配置
            return self._convert_to_autogen_format({
                "provider": "deepseek",
                "model_name": "deepseek-chat",
                "temperature": 0.7,
                "max_tokens": 4000,
                "timeout": 45
            })
    
    def _convert_to_autogen_format(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        将我们的配置格式转换为AutoGen期望的格式
        
        Args:
            config: 我们的配置格式
            
        Returns:
            AutoGen兼容的配置格式
        """
        # AutoGen期望的格式：只包含AutoGen认识的字段
        autogen_config = {
            "config_list": [{
                "model": config.get("model_name", "gpt-3.5-turbo"),
                "api_key": config.get("api_key", ""),
                "base_url": config.get("api_base", ""),  # AutoGen使用base_url而不是api_base
                "api_type": "openai",  # 设置为openai兼容
            }],
            "temperature": config.get("temperature", 0.7),
            "max_tokens": config.get("max_tokens", 4000),
            "top_p": config.get("top_p", 1.0),
            "timeout": config.get("timeout", 30),
        }

        return autogen_config

    def get_llm_config_with_metadata(self, scenario: str) -> Dict[str, Any]:
        """
        获取包含元数据的LLM配置（包含model_name、provider等额外字段）

        Args:
            scenario: 场景名称

        Returns:
            包含元数据的LLM配置字典
        """
        try:
            # 直接从统一配置获取配置
            unified_config = get_unified_config()
            base_config = unified_config.get_model_config(agent_name=scenario)

            # 转换为包含元数据的格式
            autogen_config = self._convert_to_autogen_format(base_config)

            # 添加元数据字段和顶级字段（为了兼容性）
            result = autogen_config.copy()
            result["model_name"] = base_config.get("model_name", "gpt-3.5-turbo")
            result["provider"] = base_config.get("provider", "unknown")

            # 添加顶级字段，方便直接访问
            result["api_key"] = base_config.get("api_key", "")
            result["api_base"] = base_config.get("api_base", "")
            result["max_retries"] = base_config.get("max_retries", 3)

            return result
        except Exception as e:
            self.logger.error(f"获取LLM配置（含元数据）失败: {scenario}, 错误: {e}")
            return {
                "config_list": [{"model": "gpt-3.5-turbo", "api_key": "", "base_url": "", "api_type": "openai"}],
                "temperature": 0.7,
                "max_tokens": 4000,
                "model_name": "gpt-3.5-turbo",
                "provider": "openai",
                "api_key": "",
                "api_base": "",
                "max_retries": 3
            }

    def get_scenario_params(self, scenario: str) -> Dict[str, Any]:
        """
        获取场景参数

        Args:
            scenario: 场景名称

        Returns:
            场景参数字典
        """
        try:
            # 直接从统一配置获取场景参数
            unified_config = get_unified_config()
            llm_config = unified_config._config.get('llm', {})
            scenario_params = llm_config.get('scenario_params', {})

            # 获取场景特定参数
            params = scenario_params.get(scenario, {})

            # 如果没有场景特定参数，使用默认参数
            if not params:
                default_params = llm_config.get('default_params', {})
                params = default_params

            self.logger.debug(f"获取场景 '{scenario}' 参数: {params}")
            return params

        except Exception as e:
            self.logger.error(f"获取场景参数失败: {scenario}, 错误: {e}")
            return {
                "temperature": 0.7,
                "max_tokens": 4000,
                "timeout": 30
            }
    
    # ==================== 业务规则配置 ====================
    
    def get_business_rule(self, key: str, default: Any = None) -> Any:
        """
        获取业务规则配置
        
        Args:
            key: 配置键，支持点分割路径（如 "retry.max_pending_attempts"）
            default: 默认值
            
        Returns:
            配置值
        """
        try:
            # 先尝试从config_manager获取
            value = get_unified_config().get_business_rule(key, default)
            if value is not None:
                return value
        except Exception as e:
            self.logger.warning(f"从config_manager获取业务规则失败: {key}, 错误: {e}")
        
        # 如果是关键词相关配置，尝试从动态配置获取
        if key.startswith("keyword") or "intent" in key.lower():
            try:
                keyword_config = dynamic_keyword_config.get_config()
                if keyword_config:
                    return self._get_nested_value(keyword_config, key.split('.'), default)
            except Exception as e:
                self.logger.warning(f"从动态关键词配置获取失败: {key}, 错误: {e}")
        
        return default
    
    def get_threshold(self, key: str, default: float = 0.0) -> Union[float, bool]:
        """
        获取阈值配置
        
        Args:
            key: 阈值键，支持点分割路径
            default: 默认值
            
        Returns:
            阈值数值或布尔值
        """
        try:
            return get_unified_config().get_threshold(key, default)
        except Exception as e:
            self.logger.error(f"获取阈值配置失败: {key}, 错误: {e}")
            return default
    
    # ==================== 消息配置相关 ====================
    
    def get_message_template(self, key: str, **kwargs) -> str:
        """
        获取消息模板并进行变量替换
        
        Args:
            key: 模板键
            **kwargs: 模板变量
            
        Returns:
            格式化后的消息字符串
        """
        try:
            return get_unified_config().get_message_template(key, **kwargs)
        except Exception as e:
            self.logger.error(f"获取消息模板失败: {key}, 错误: {e}")
            return f"[模板错误: {key}]"
    
    def get_message_config(self, section: str = None) -> Dict[str, Any]:
        """
        获取消息配置
        
        Args:
            section: 配置节名称（可选）
            
        Returns:
            消息配置字典
        """
        try:
            return get_unified_config().get_message_config(section)
        except Exception as e:
            self.logger.error(f"获取消息配置失败: {section}, 错误: {e}")
            return {}
    
    def get_message_generator_config(self, generator_name: str) -> Dict[str, Any]:
        """
        获取动态消息生成器配置
        
        Args:
            generator_name: 生成器名称
            
        Returns:
            生成器配置字典
        """
        try:
            return get_unified_config().get_message_generator_config(generator_name)
        except Exception as e:
            self.logger.error(f"获取消息生成器配置失败: {generator_name}, 错误: {e}")
            return {}
    
    # ==================== 关键词配置相关 ====================
    
    def get_keyword_config(self) -> Optional[Dict[str, Any]]:
        """获取关键词配置"""
        try:
            return dynamic_keyword_config.get_config()
        except Exception as e:
            self.logger.error(f"获取关键词配置失败: {e}")
            return None
    
    def update_keyword_config(self, new_config: Dict[str, Any], 
                            author: str = "system", 
                            description: str = "配置更新") -> bool:
        """
        更新关键词配置
        
        Args:
            new_config: 新配置
            author: 操作作者
            description: 更新描述
            
        Returns:
            更新是否成功
        """
        try:
            return dynamic_keyword_config.update_config(new_config, author, description)
        except Exception as e:
            self.logger.error(f"更新关键词配置失败: {e}")
            return False
    
    def add_keywords(self, intent: str, keywords: list, author: str = "system") -> bool:
        """
        添加关键词
        
        Args:
            intent: 意图名称
            keywords: 关键词列表
            author: 操作作者
            
        Returns:
            操作是否成功
        """
        try:
            return dynamic_keyword_config.add_keywords(intent, keywords, author)
        except Exception as e:
            self.logger.error(f"添加关键词失败: {intent}, {e}")
            return False
    
    # ==================== 数据库配置相关 ====================
    
    def get_database_query(self, key: str) -> str:
        """
        获取数据库查询模板
        
        Args:
            key: 查询键
            
        Returns:
            SQL查询字符串
        """
        try:
            return get_unified_config().get_database_query(key)
        except Exception as e:
            self.logger.error(f"获取数据库查询失败: {key}, 错误: {e}")
            return ""
    
    # ==================== 配置监听和管理 ====================
    
    def add_change_listener(self, config_type: str, callback: Callable):
        """
        添加配置变更监听器
        
        Args:
            config_type: 配置类型 ("keyword", "message", "business")
            callback: 回调函数
        """
        if config_type not in self._change_listeners:
            self._change_listeners[config_type] = []
        
        self._change_listeners[config_type].append(callback)
        
        # 注册到相应的底层管理器
        if config_type == "keyword":
            dynamic_keyword_config.add_change_callback(callback)
        elif config_type in ["message", "business"]:
            # 对于静态配置，可以考虑文件监听
            pass
        
        self.logger.info(f"添加配置变更监听器: {config_type}")
    
    def remove_change_listener(self, config_type: str, callback: Callable):
        """移除配置变更监听器"""
        if config_type in self._change_listeners:
            try:
                self._change_listeners[config_type].remove(callback)
                
                # 从底层管理器移除
                if config_type == "keyword":
                    dynamic_keyword_config.remove_change_callback(callback)
                
                self.logger.info(f"移除配置变更监听器: {config_type}")
            except ValueError:
                pass
    
    def reload_all_configs(self):
        """重新加载所有配置"""
        try:
            # 重新加载静态配置
            get_unified_config().reload_all()
            
            # 清除本地缓存
            with self._cache_lock:
                self._cache.clear()
            
            self.logger.info("所有配置重新加载完成")
        except Exception as e:
            self.logger.error(f"重新加载配置失败: {e}")
    
    def get_config_status(self) -> Dict[str, Any]:
        """获取配置状态信息"""
        try:
            status = {
                "static_configs": get_unified_config().get_config_status(),
                "dynamic_configs": {
                    "keyword_config": dynamic_keyword_config.get_config_info()
                },
                "service_cache_size": len(self._cache),
                "change_listeners": {
                    config_type: len(listeners) 
                    for config_type, listeners in self._change_listeners.items()
                }
            }
            return status
        except Exception as e:
            self.logger.error(f"获取配置状态失败: {e}")
            return {}
    
    # ==================== 便捷访问方法 ====================
    
    def get_model_config(self, agent_name: str = None, model_name: str = None) -> Dict[str, Any]:
        """
        获取模型配置信息

        Args:
            agent_name: Agent名称（用于获取场景映射）
            model_name: 指定的模型名称

        Returns:
            模型配置信息
        """
        try:
            # 直接从统一配置获取配置
            unified_config = get_unified_config()

            if agent_name:
                # 根据agent_name获取完整的LLM配置
                llm_config = unified_config.get_model_config(agent_name=agent_name)
                self.logger.debug(f"获取到Agent {agent_name} 的LLM配置: {llm_config}")

                return {
                    "model_name": llm_config.get("model_name", "unknown"),
                    "provider": llm_config.get("provider", "unknown"),
                    "max_tokens": llm_config.get("max_tokens", 4000),
                    "temperature": llm_config.get("temperature", 0.7)
                }
            elif model_name:
                # 根据model_name直接获取配置
                model_config = unified_config.get_model_config(model_name=model_name)
                return {
                    "model_name": model_config.get("model_name", model_name),
                    "provider": model_config.get("provider", "unknown"),
                    "max_tokens": model_config.get("max_tokens", 4000),
                    "temperature": model_config.get("temperature", 0.7)
                }
            else:
                # 默认配置
                default_config = unified_config.get_model_config()
                return {
                    "model_name": default_config.get("model_name", "deepseek-chat"),
                    "provider": default_config.get("provider", "deepseek"),
                    "max_tokens": default_config.get("max_tokens", 4000),
                    "temperature": default_config.get("temperature", 0.7)
                }
        except Exception as e:
            self.logger.error(f"获取模型配置失败: {e}", exc_info=True)
            return {
                "model_name": "unknown",
                "provider": "unknown",
                "max_tokens": 4000,
                "temperature": 0.7
            }
    
    def get_agent_config(self, agent_name: str) -> Dict[str, Any]:
        """
        获取Agent配置（LLM + 业务规则的组合）
        
        Args:
            agent_name: Agent名称
            
        Returns:
            Agent完整配置
        """
        return {
            "llm_config": self.get_llm_config(agent_name),
            "scenario_params": self.get_scenario_params(agent_name),
            "business_rules": {},  # 可以根据需要扩展
        }
    
    def get_conversation_config(self) -> Dict[str, Any]:
        """获取对话相关的完整配置"""
        return {
            "keyword_config": self.get_keyword_config(),
            "message_templates": self.get_message_config("templates"),
            "business_rules": {
                "retry": self.get_business_rule("retry", {}),
                "document_confirmation": self.get_business_rule("document_confirmation", {}),
            },
            "thresholds": {
                "extraction_completeness": self.get_threshold("business_rules.requirement_collection.completion_threshold", 0.8),
                "conversation_max_turns": self.get_threshold("system.performance.max_conversation_turns", 15),
            }
        }
    
    def is_debug_enabled(self) -> bool:
        """检查是否启用调试模式"""
        return self.get_threshold("system.debug_mode", False)
    
    # ==================== 内部辅助方法 ====================
    
    def _get_nested_value(self, data: Dict[str, Any], path: list, default: Any = None) -> Any:
        """获取嵌套字典中的值"""
        current = data
        for key in path:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return default
        return current
    
    def _cache_key(self, prefix: str, *args) -> str:
        """生成缓存键"""
        return f"{prefix}:{':'.join(str(arg) for arg in args)}"


# ==================== 全局实例 ====================

# 创建全局配置服务实例
config_service = ConfigurationService()

# 导出主要接口，便于其他模块导入
__all__ = [
    'config_service',
    'ConfigurationService',
]