"""
优化配置模块
用于控制系统优化功能的开关和参数
"""

import os
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass


@dataclass
class OptimizationConfig:
    """优化配置类"""
    
    # 初始化跟踪
    enable_init_tracking: bool = True
    
    # 配置缓存
    enable_config_cache: bool = True
    
    # 重复检测阈值
    duplicate_warning_threshold: int = 2
    
    # 统计报告间隔
    stats_report_interval: int = 100
    
    # 日志级别
    optimization_log_level: str = "INFO"


class OptimizationManager:
    """优化管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.config = self._load_config()
        
    def _load_config(self) -> OptimizationConfig:
        """从环境变量加载优化配置"""
        return OptimizationConfig(
            enable_init_tracking=os.getenv("ENABLE_INIT_TRACKING", "true").lower() == "true",
            enable_config_cache=os.getenv("ENABLE_CONFIG_CACHE", "true").lower() == "true",
            duplicate_warning_threshold=int(os.getenv("DUPLICATE_WARNING_THRESHOLD", "2")),
            stats_report_interval=int(os.getenv("STATS_REPORT_INTERVAL", "100")),
            optimization_log_level=os.getenv("OPTIMIZATION_LOG_LEVEL", "INFO")
        )
    
    def is_init_tracking_enabled(self) -> bool:
        """检查是否启用初始化跟踪"""
        return self.config.enable_init_tracking
    
    def is_config_cache_enabled(self) -> bool:
        """检查是否启用配置缓存"""
        return self.config.enable_config_cache
    
    def get_duplicate_threshold(self) -> int:
        """获取重复检测阈值"""
        return self.config.duplicate_warning_threshold
    
    def should_report_stats(self, count: int) -> bool:
        """检查是否应该报告统计信息"""
        return count % self.config.stats_report_interval == 0
    
    def get_config_dict(self) -> Dict[str, Any]:
        """获取配置字典"""
        return {
            "enable_init_tracking": self.config.enable_init_tracking,
            "enable_config_cache": self.config.enable_config_cache,
            "duplicate_warning_threshold": self.config.duplicate_warning_threshold,
            "stats_report_interval": self.config.stats_report_interval,
            "optimization_log_level": self.config.optimization_log_level
        }


# 全局优化管理器实例
_optimization_manager = OptimizationManager()


def get_optimization_manager() -> OptimizationManager:
    """获取优化管理器实例"""
    return _optimization_manager


def is_optimization_enabled() -> bool:
    """检查是否启用任何优化功能"""
    manager = get_optimization_manager()
    return (manager.is_init_tracking_enabled() or 
            manager.is_config_cache_enabled())


def log_optimization_status():
    """记录优化状态"""
    manager = get_optimization_manager()
    logger = logging.getLogger(__name__)
    
    config = manager.get_config_dict()
    logger.info("🔧 系统优化配置状态:")
    for key, value in config.items():
        status = "✅ 启用" if value else "❌ 禁用" if isinstance(value, bool) else value
        logger.info(f"  {key}: {status}")
