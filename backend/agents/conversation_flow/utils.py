"""
对话流程工具函数和常量

包含：
- ConversationConstants: 配置常量类
- 通用工具函数
- 共享的导入和配置
"""

from backend.config.unified_config_loader import get_unified_config


class ConversationConstants:
    """集中管理对话流程中使用的配置常量"""

    DEFAULT_TEMPERATURE = 0.7
    COMPLETENESS_THRESHOLD = 0.8  # 与配置文件保持一致
    MAX_PENDING_ATTEMPTS = 3
    CACHE_TTL = 300

    @classmethod
    def get_temperature(cls):
        try:
            from backend.config import config_service
            config = config_service.get_llm_config_with_metadata("conversation_flow")
            return config.get("temperature", cls.DEFAULT_TEMPERATURE)
        except Exception:
            return cls.DEFAULT_TEMPERATURE

    @classmethod
    def get_completeness_threshold(cls):
        return get_unified_config().get_threshold("business_rules.requirement_collection.completion_threshold", cls.COMPLETENESS_THRESHOLD)

    @classmethod
    def get_max_pending_attempts(cls):
        return get_unified_config().get_business_rule("retry.max_pending_attempts", cls.MAX_PENDING_ATTEMPTS)