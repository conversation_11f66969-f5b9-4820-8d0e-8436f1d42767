#!/usr/bin/env python3
"""
统一状态管理器
整合所有状态管理操作，提供统一的状态管理接口
"""

import logging
from enum import Enum, auto
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass
from datetime import datetime
import asyncio
import threading

from backend.config.unified_config_loader import get_unified_config
from backend.data.db.database_manager import DatabaseManager


class ConversationState(Enum):
    """对话状态枚举"""
    IDLE = "IDLE"                    # 空闲状态
    PROCESSING_INTENT = "PROCESSING_INTENT"  # 处理意图
    COLLECTING_INFO = "COLLECTING_INFO"      # 收集需求信息
    DOCUMENTING = "DOCUMENTING"              # 文档生成与修改
    COMPLETED = "COMPLETED"                  # 完成状态


class FocusPointStatus(Enum):
    """关注点状态枚举"""
    PENDING = "pending"              # 待处理
    PROCESSING = "processing"        # 处理中
    COMPLETED = "completed"          # 已完成
    SKIPPED = "skipped"             # 已跳过


@dataclass
class StateTransition:
    """状态转换数据类"""
    from_state: ConversationState
    to_state: ConversationState
    trigger: str
    condition: Optional[str] = None
    timestamp: Optional[datetime] = None


@dataclass
class SessionState:
    """会话状态数据类"""
    session_id: str
    user_id: str
    conversation_state: ConversationState
    focus_points_status: Dict[str, FocusPointStatus]
    last_updated: datetime
    metadata: Dict[str, Any]


class UnifiedStateManager:
    """统一状态管理器"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls, db_manager: Optional[DatabaseManager] = None):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self, db_manager: Optional[DatabaseManager] = None):
        if hasattr(self, '_initialized'):
            return
        
        self.logger = logging.getLogger(__name__)
        self.config = get_unified_config()
        self.db_manager = db_manager
        
        # 状态转换规则
        self._transition_rules = self._load_transition_rules()
        
        # 状态缓存
        self._state_cache = {}
        self._cache_lock = asyncio.Lock()
        
        self._initialized = True
    
    def _load_transition_rules(self) -> Dict[str, Dict[str, str]]:
        """加载状态转换规则"""
        conv_config = self.config.get_conversation_config()
        return conv_config.get('transitions', {})
    
    async def get_conversation_state(self, session_id: str, user_id: str) -> ConversationState:
        """获取对话状态"""
        try:
            # 先检查缓存
            cache_key = f"{session_id}:{user_id}"
            async with self._cache_lock:
                if cache_key in self._state_cache:
                    cached_state = self._state_cache[cache_key]
                    # 检查缓存是否过期（5分钟）
                    if (datetime.now() - cached_state['timestamp']).seconds < 300:
                        return ConversationState(cached_state['state'])
            
            # 从数据库获取状态
            if self.db_manager:
                state = await self._determine_conversation_state_from_db(session_id, user_id)
            else:
                state = ConversationState.IDLE
            
            # 更新缓存
            async with self._cache_lock:
                self._state_cache[cache_key] = {
                    'state': state.value,
                    'timestamp': datetime.now()
                }
            
            return state
            
        except Exception as e:
            self.logger.error(f"获取对话状态失败: {e}")
            return ConversationState.IDLE
    
    async def _determine_conversation_state_from_db(self, session_id: str, user_id: str) -> ConversationState:
        """从数据库确定对话状态"""
        try:
            # 检查是否有未完成的文档
            doc_result = await self.db_manager.get_record(
                """
                SELECT document_id FROM documents
                WHERE conversation_id = ? AND user_id = ? AND status = 'draft'
                """,
                (session_id, user_id)
            )
            
            if doc_result:
                return ConversationState.DOCUMENTING
            
            # 检查是否有领域和分类信息
            domain_result = await self.db_manager.get_record(
                """
                SELECT domain_id, category_id 
                FROM conversations 
                WHERE conversation_id = ? AND user_id = ?
                """,
                (session_id, user_id)
            )
            
            if domain_result and domain_result.get('domain_id'):
                return ConversationState.COLLECTING_INFO
            
            return ConversationState.IDLE
            
        except Exception as e:
            self.logger.error(f"从数据库确定对话状态失败: {e}")
            return ConversationState.IDLE
    
    async def set_conversation_state(self, session_id: str, user_id: str, 
                                   new_state: ConversationState, 
                                   trigger: str = "manual") -> bool:
        """设置对话状态"""
        try:
            current_state = await self.get_conversation_state(session_id, user_id)
            
            # 验证状态转换是否合法
            if not self._is_valid_transition(current_state, new_state, trigger):
                self.logger.warning(f"无效的状态转换: {current_state} -> {new_state} (trigger: {trigger})")
                return False
            
            # 更新数据库中的状态
            if self.db_manager:
                await self._update_conversation_state_in_db(session_id, user_id, new_state)
            
            # 更新缓存
            cache_key = f"{session_id}:{user_id}"
            async with self._cache_lock:
                self._state_cache[cache_key] = {
                    'state': new_state.value,
                    'timestamp': datetime.now()
                }
            
            # 记录状态转换
            self.logger.info(f"状态转换成功: {current_state} -> {new_state} (session: {session_id}, trigger: {trigger})")
            
            return True
            
        except Exception as e:
            self.logger.error(f"设置对话状态失败: {e}")
            return False
    
    def _is_valid_transition(self, from_state: ConversationState, 
                           to_state: ConversationState, trigger: str) -> bool:
        """验证状态转换是否合法"""
        # 获取当前状态的转换规则
        state_transitions = self._transition_rules.get(from_state.value, {})
        
        # 检查触发器是否允许转换到目标状态
        expected_state = state_transitions.get(trigger)
        
        if expected_state == to_state.value:
            return True
        
        # 允许手动转换和一些特殊情况
        if trigger in ["manual", "system", "error_recovery", "state_machine", "test", "reset"]:
            return True
        
        return False
    
    async def _update_conversation_state_in_db(self, session_id: str, user_id: str, 
                                             state: ConversationState):
        """在数据库中更新对话状态"""
        try:
            # 这里可以根据实际数据库结构来实现
            # 目前只是记录日志
            self.logger.debug(f"更新数据库状态: {session_id} -> {state.value}")
        except Exception as e:
            self.logger.error(f"更新数据库状态失败: {e}")
    
    async def get_focus_point_status(self, session_id: str, user_id: str, 
                                   point_id: str) -> FocusPointStatus:
        """获取关注点状态"""
        try:
            if not self.db_manager:
                return FocusPointStatus.PENDING
            
            result = await self.db_manager.get_record(
                """
                SELECT status FROM focus_points_status 
                WHERE session_id = ? AND user_id = ? AND point_id = ?
                """,
                (session_id, user_id, point_id)
            )
            
            if result:
                return FocusPointStatus(result['status'])
            else:
                return FocusPointStatus.PENDING
                
        except Exception as e:
            self.logger.error(f"获取关注点状态失败: {e}")
            return FocusPointStatus.PENDING
    
    async def set_focus_point_status(self, session_id: str, user_id: str, 
                                   point_id: str, status: FocusPointStatus) -> bool:
        """设置关注点状态"""
        try:
            if not self.db_manager:
                return False
            
            # 更新或插入关注点状态
            await self.db_manager.execute_update(
                """
                INSERT OR REPLACE INTO focus_points_status 
                (session_id, user_id, point_id, status, updated_at)
                VALUES (?, ?, ?, ?, ?)
                """,
                (session_id, user_id, point_id, status.value, datetime.now())
            )
            
            self.logger.debug(f"关注点状态更新成功: {point_id} -> {status.value}")
            return True
            
        except Exception as e:
            self.logger.error(f"设置关注点状态失败: {e}")
            return False
    
    async def get_all_focus_points_status(self, session_id: str, user_id: str) -> Dict[str, FocusPointStatus]:
        """获取所有关注点状态"""
        try:
            if not self.db_manager:
                return {}
            
            results = await self.db_manager.get_records(
                """
                SELECT point_id, status FROM focus_points_status 
                WHERE session_id = ? AND user_id = ?
                """,
                (session_id, user_id)
            )
            
            return {
                result['point_id']: FocusPointStatus(result['status'])
                for result in results
            }
            
        except Exception as e:
            self.logger.error(f"获取所有关注点状态失败: {e}")
            return {}
    
    async def clear_processing_status(self, session_id: str, user_id: str) -> bool:
        """清理所有processing状态的关注点"""
        try:
            if not self.db_manager:
                return False
            
            await self.db_manager.execute_update(
                """
                UPDATE focus_points_status 
                SET status = 'pending', updated_at = ?
                WHERE session_id = ? AND user_id = ? AND status = 'processing'
                """,
                (datetime.now(), session_id, user_id)
            )
            
            self.logger.debug(f"清理processing状态完成: {session_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"清理processing状态失败: {e}")
            return False
    
    async def get_next_pending_focus_point(self, session_id: str, user_id: str, 
                                         focus_points: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """获取下一个待处理的关注点"""
        try:
            # 获取所有关注点状态
            statuses = await self.get_all_focus_points_status(session_id, user_id)
            
            # 按优先级排序，找到第一个pending状态的关注点
            for point in sorted(focus_points, key=lambda x: x.get("priority", "P2")):
                point_id = point.get("id")
                if not point_id:
                    continue
                
                status = statuses.get(point_id, FocusPointStatus.PENDING)
                if status == FocusPointStatus.PENDING:
                    return point
            
            return None
            
        except Exception as e:
            self.logger.error(f"获取下一个待处理关注点失败: {e}")
            return None
    
    async def get_session_state(self, session_id: str, user_id: str) -> SessionState:
        """获取完整的会话状态"""
        try:
            conversation_state = await self.get_conversation_state(session_id, user_id)
            focus_points_status = await self.get_all_focus_points_status(session_id, user_id)
            
            return SessionState(
                session_id=session_id,
                user_id=user_id,
                conversation_state=conversation_state,
                focus_points_status=focus_points_status,
                last_updated=datetime.now(),
                metadata={}
            )
            
        except Exception as e:
            self.logger.error(f"获取会话状态失败: {e}")
            return SessionState(
                session_id=session_id,
                user_id=user_id,
                conversation_state=ConversationState.IDLE,
                focus_points_status={},
                last_updated=datetime.now(),
                metadata={}
            )
    
    async def reset_session_state(self, session_id: str, user_id: str) -> bool:
        """重置会话状态"""
        try:
            # 重置对话状态
            await self.set_conversation_state(session_id, user_id, ConversationState.IDLE, "reset")
            
            # 清理所有关注点状态
            if self.db_manager:
                await self.db_manager.execute_update(
                    """
                    DELETE FROM focus_points_status 
                    WHERE session_id = ? AND user_id = ?
                    """,
                    (session_id, user_id)
                )
            
            # 清理缓存
            cache_key = f"{session_id}:{user_id}"
            async with self._cache_lock:
                if cache_key in self._state_cache:
                    del self._state_cache[cache_key]
            
            self.logger.info(f"会话状态重置成功: {session_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"重置会话状态失败: {e}")
            return False
    
    def get_available_states(self) -> List[ConversationState]:
        """获取可用的对话状态"""
        conv_config = self.config.get_conversation_config()
        available_states = conv_config.get('states', {}).get('available', [])
        return [ConversationState(state) for state in available_states]
    
    def get_transition_rules(self) -> Dict[str, Dict[str, str]]:
        """获取状态转换规则"""
        return self._transition_rules.copy()


# 创建全局实例
_state_manager_instance = None


def get_state_manager(db_manager: Optional[DatabaseManager] = None) -> UnifiedStateManager:
    """获取统一状态管理器实例"""
    global _state_manager_instance
    if _state_manager_instance is None:
        _state_manager_instance = UnifiedStateManager(db_manager)
    return _state_manager_instance


if __name__ == "__main__":
    # 测试状态管理器
    import asyncio
    
    async def test_state_manager():
        print("🧪 测试统一状态管理器...")
        
        manager = get_state_manager()
        
        # 测试基本功能
        print(f"可用状态: {[state.value for state in manager.get_available_states()]}")
        print(f"转换规则: {manager.get_transition_rules()}")
        
        # 测试状态获取
        state = await manager.get_conversation_state("test_session", "test_user")
        print(f"当前状态: {state.value}")
        
        # 测试状态设置
        success = await manager.set_conversation_state("test_session", "test_user", 
                                                     ConversationState.COLLECTING_INFO, "business_requirement")
        print(f"状态设置成功: {success}")
        
        print("🎉 状态管理器测试完成!")
    
    asyncio.run(test_state_manager())
