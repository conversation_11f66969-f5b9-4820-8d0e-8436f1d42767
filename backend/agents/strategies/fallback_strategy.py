"""
回退策略实现

当其他策略都无法处理用户请求时的兜底处理，包括：
- 未知意图处理
- 模糊请求澄清
- 通用帮助引导
- 错误情况处理

优先级: 1 (最低，兜底)
支持意图: general_chat, unknown
"""

import logging
from typing import List, Dict, Any
import random

from ..decision_types import DecisionStrategy, AnalyzedContext, DecisionResult, create_decision_result
from ..unified_state_manager import ConversationState


class FallbackStrategy(DecisionStrategy):
    """回退策略实现"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 回退回复模板分类
        self.fallback_templates = {
            "clarification": [
                "抱歉，我没有完全理解您的意思。能否请您再详细说明一下？",
                "我想更好地帮助您，请问您具体需要什么帮助？",
                "为了给您提供准确的帮助，请您详细描述一下您的需求。",
                "我需要更多信息才能为您提供合适的帮助，请您补充说明一下。"
            ],
            "general_help": [
                "我是AI助手，可以帮助您处理各种需求。请告诉我您需要什么帮助？",
                "我可以协助您解决问题、回答疑问或提供建议。有什么可以帮您的吗？",
                "作为您的AI助手，我随时准备为您提供帮助。请说明您的具体需求。",
                "我很乐意为您提供帮助！请告诉我您遇到了什么问题或需要什么服务。"
            ],
            "capability_hint": [
                "我可以帮助您：\n• 解答各类问题\n• 提供专业建议\n• 协助需求分析\n• 查找相关信息\n\n请告诉我您需要哪方面的帮助？",
                "我的主要能力包括：\n• 问题咨询与解答\n• 项目需求收集\n• 信息查询与整理\n• 方案建议与分析\n\n有什么具体需要协助的吗？",
                "我可以在以下方面为您提供帮助：\n• 技术咨询\n• 需求梳理\n• 信息搜索\n• 问题解决\n\n请说明您的具体需求。"
            ],
            "encouragement": [
                "虽然我暂时没有理解您的具体需求，但我会努力帮助您。请再试着描述一下？",
                "每个问题都有解决的方法，让我们一起来找到答案。请详细说明您的情况。",
                "我相信我们能够找到解决方案。请您耐心地再解释一下您的需求。",
                "不用担心，我会尽力理解并帮助您。请用不同的方式描述一下您的问题。"
            ],
            "redirect": [
                "如果您有具体的项目需求，我可以帮您详细分析。",
                "如果您需要查询某些信息，我可以为您搜索。",
                "如果您遇到了技术问题，我可以协助诊断。",
                "如果您需要咨询建议，我可以提供专业意见。"
            ]
        }
        
        # 常见场景引导
        self.scenario_guides = {
            "project_inquiry": "如果您想了解项目开发相关信息，请告诉我项目类型和具体需求。",
            "technical_support": "如果您遇到技术问题，请描述具体的错误现象或困难。",
            "information_search": "如果您需要查找信息，请告诉我您想了解什么内容。",
            "consultation": "如果您需要咨询建议，请详细说明您的情况和疑问。"
        }
        
        # 错误处理模板
        self.error_templates = [
            "系统处理时遇到了一些问题，请您稍后再试或换个方式描述。",
            "抱歉，刚才的处理出现了异常。请您重新描述一下需求。",
            "系统暂时无法处理您的请求，请您稍等片刻或重新表述。"
        ]
        
        # 上下文线索关键词
        self.context_clues = {
            "technical": ["系统", "软件", "程序", "代码", "bug", "错误", "技术"],
            "business": ["项目", "需求", "方案", "计划", "业务", "流程"],
            "design": ["设计", "界面", "UI", "图标", "logo", "海报", "视觉"],
            "content": ["文案", "内容", "文章", "资料", "文档", "写作"],
            "consultation": ["咨询", "建议", "意见", "分析", "评估", "指导"]
        }
    
    @property
    def name(self) -> str:
        return "fallback_strategy"
    
    @property
    def supported_intents(self) -> List[str]:
        return ["general_chat", "unknown"]
    
    @property
    def priority(self) -> int:
        return 1  # 最低优先级，兜底策略
    
    async def can_handle(self, context: AnalyzedContext) -> bool:
        """
        判断是否能处理当前上下文
        
        回退策略总是能处理任何请求（兜底）
        
        Args:
            context: 分析后的上下文
            
        Returns:
            bool: 总是返回True
        """
        return True  # 回退策略总是可以处理
    
    async def execute(self, context: AnalyzedContext) -> DecisionResult:
        """
        执行回退策略
        
        Args:
            context: 分析后的上下文
            
        Returns:
            DecisionResult: 决策结果
        """
        try:
            # 分析回退原因
            fallback_reason = self._analyze_fallback_reason(context)
            
            # 检测上下文线索
            context_clues = self._detect_context_clues(context)
            
            # 选择回退策略类型
            fallback_type = self._select_fallback_type(fallback_reason, context_clues)
            
            # 生成回退回复
            response_content = self._generate_fallback_response(fallback_type, context_clues)
            
            # 计算置信度（回退策略置信度较低）
            confidence = await self.calculate_confidence(context)
            
            # 确定处理动作
            action = self._determine_fallback_action(fallback_type)
            
            # 创建决策结果
            result = create_decision_result(
                action=action,
                confidence=confidence,
                intent=context.intent if context.intent != "unknown" else "general_chat",
                emotion=context.emotion,
                response_template=response_content,
                next_state=ConversationState.IDLE,
                strategy_name=self.name,
                metadata={
                    "fallback_reason": fallback_reason,
                    "fallback_type": fallback_type,
                    "context_clues": context_clues,
                    "is_fallback": True,
                    "original_intent": context.intent
                }
            )
            
            self.logger.info(f"回退策略执行: reason={fallback_reason}, type={fallback_type}")
            return result
            
        except Exception as e:
            self.logger.error(f"回退策略执行失败: {e}", exc_info=True)
            
            # 最基础的回退处理
            return create_decision_result(
                action="handle_unknown_situation",
                confidence=0.3,
                intent="general_chat",
                emotion="neutral",
                response_template="抱歉，我遇到了一些问题。请您重新描述一下需求，我会尽力帮助您。",
                next_state=ConversationState.IDLE,
                strategy_name=self.name,
                metadata={"error": str(e), "emergency_fallback": True}
            )
    
    async def calculate_confidence(self, context: AnalyzedContext) -> float:
        """
        计算处理置信度
        
        回退策略的置信度通常较低，因为它是兜底处理
        
        Args:
            context: 分析后的上下文
            
        Returns:
            float: 置信度 (0.0-1.0)
        """
        confidence_factors = []
        
        # 1. 基础置信度（回退策略固有较低）
        confidence_factors.append(0.3)
        
        # 2. 上下文线索质量
        context_clues = self._detect_context_clues(context)
        if context_clues:
            confidence_factors.append(0.5)  # 有线索时稍高
        else:
            confidence_factors.append(0.2)  # 无线索时很低
        
        # 3. 消息长度因子
        message_length = len(context.message)
        if message_length > 20:
            confidence_factors.append(0.4)  # 较长消息可能包含更多信息
        elif message_length > 5:
            confidence_factors.append(0.3)
        else:
            confidence_factors.append(0.2)  # 很短的消息难以理解
        
        # 4. 意图明确度
        if context.intent == "general_chat":
            confidence_factors.append(0.4)
        elif context.intent == "unknown":
            confidence_factors.append(0.2)
        else:
            confidence_factors.append(0.3)
        
        return sum(confidence_factors) / len(confidence_factors)
    
    def _analyze_fallback_reason(self, context: AnalyzedContext) -> str:
        """分析回退原因"""
        if context.intent == "unknown":
            return "unknown_intent"
        elif context.intent == "general_chat":
            return "general_conversation"
        elif context.confidence < 0.5:
            return "low_confidence"
        else:
            return "no_matching_strategy"
    
    def _detect_context_clues(self, context: AnalyzedContext) -> List[str]:
        """检测上下文线索"""
        message_lower = context.message.lower()
        detected_clues = []
        
        for clue_type, keywords in self.context_clues.items():
            if any(keyword in message_lower for keyword in keywords):
                detected_clues.append(clue_type)
        
        return detected_clues
    
    def _select_fallback_type(self, reason: str, clues: List[str]) -> str:
        """选择回退策略类型"""
        if reason == "unknown_intent":
            if clues:
                return "capability_hint"  # 有线索时提供能力提示
            else:
                return "clarification"  # 无线索时要求澄清
        elif reason == "general_conversation":
            return "general_help"
        elif reason == "low_confidence":
            return "encouragement"
        else:
            return "redirect"
    
    def _generate_fallback_response(self, fallback_type: str, clues: List[str]) -> str:
        """生成回退回复"""
        # 选择基础模板
        if fallback_type in self.fallback_templates:
            base_response = random.choice(self.fallback_templates[fallback_type])
        else:
            base_response = random.choice(self.fallback_templates["general_help"])
        
        # 根据上下文线索添加引导
        if clues and fallback_type in ["clarification", "capability_hint"]:
            guidance_parts = []
            for clue in clues[:2]:  # 最多显示2个线索
                if clue in ["technical", "business", "design", "content", "consultation"]:
                    scenario_key = f"{clue}_inquiry" if clue != "consultation" else "consultation"
                    if scenario_key in self.scenario_guides:
                        guidance_parts.append(self.scenario_guides[scenario_key])
            
            if guidance_parts:
                guidance = "\n\n" + "\n".join(guidance_parts)
                return base_response + guidance
        
        return base_response
    
    def _determine_fallback_action(self, fallback_type: str) -> str:
        """确定回退处理动作"""
        action_mapping = {
            "clarification": "request_clarification",
            "general_help": "provide_general_help",
            "capability_hint": "show_capabilities",
            "encouragement": "encourage_retry",
            "redirect": "redirect_to_service"
        }
        
        return action_mapping.get(fallback_type, "handle_unknown_situation")
    
    async def calculate_context_match(self, context: AnalyzedContext) -> float:
        """
        计算与上下文的匹配度
        
        回退策略对所有上下文都有基础匹配度
        
        Args:
            context: 分析后的上下文
            
        Returns:
            float: 匹配度 (0.0-1.0)
        """
        # 回退策略总是有基础匹配度
        base_match = 0.3
        
        # 如果是支持的意图，匹配度稍高
        if context.intent in self.supported_intents:
            return 0.6
        
        # 如果有上下文线索，匹配度稍高
        clues = self._detect_context_clues(context)
        if clues:
            return base_match + 0.2
        
        return base_match
    
    def get_fallback_statistics(self) -> Dict[str, Any]:
        """获取回退策略统计信息（用于监控）"""
        return {
            "strategy_name": self.name,
            "priority": self.priority,
            "supported_intents": self.supported_intents,
            "template_categories": list(self.fallback_templates.keys()),
            "context_clue_types": list(self.context_clues.keys()),
            "is_fallback_strategy": True
        }


# 导出策略类
__all__ = ['FallbackStrategy']
