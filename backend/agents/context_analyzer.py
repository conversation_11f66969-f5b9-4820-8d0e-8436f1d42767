"""
上下文分析器实现

负责分析用户输入的上下文信息，包括：
- 意图识别
- 情感分析
- 实体提取
- 上下文理解
"""

import logging
import re
from typing import Dict, Any, List, Optional

from .decision_engine_interface import ContextAnalyzerInterface
from .decision_types import DecisionContext, AnalyzedContext, DecisionConfidence


class ContextAnalyzer(ContextAnalyzerInterface):
    """上下文分析器实现"""
    
    def __init__(self, llm_service: Optional[Any] = None):
        self.logger = logging.getLogger(__name__)
        self.llm_service = llm_service
        
        # 初始化关键词规则（从SimplifiedDecisionEngine迁移）
        self._init_keyword_rules()
        self._init_emotion_keywords()
        
        self.logger.info("上下文分析器初始化完成")
    
    def _init_keyword_rules(self):
        """初始化关键词规则"""
        self.intent_keywords = {
            # 知识库查询关键词
            "search_knowledge_base": [
                "如何注册", "怎么注册", "注册流程", "账号注册",
                "如何使用", "怎么使用", "使用方法", "操作步骤",
                "什么是", "介绍一下", "功能说明", "如何操作",
                "支持哪些", "有哪些功能", "提供什么", "能用什么",
                "登录方法", "密码重置", "忘记密码",
                "功能介绍", "产品说明", "服务介绍",
                "收费标准", "价格说明", "套餐介绍",
                "技术支持", "客服联系", "帮助文档",
                "常见问题", "FAQ", "问题解答", "疑难解答"
            ],
            
            # 问候关键词
            "greeting": [
                "你好", "您好", "hi", "hello", "嗨", "早上好", "下午好", "晚上好"
            ],
            
            # 自我介绍关键词
            "ask_introduction": [
                "自我介绍", "介绍自己", "你是谁", "介绍一下你自己"
            ],
            
            # 能力询问关键词
            "ask_capabilities": [
                "你能做什么", "有什么功能", "能力", "功能介绍", "你会什么"
            ],
            
            # 业务需求关键词
            "business_requirement": [
                "我想", "我需要", "帮我", "制作", "设计", "开发", "创建",
                "海报", "网站", "app", "应用", "系统", "平台", "项目"
            ],
            
            # 情感支持关键词
            "emotional_support": [
                "心情不好", "安慰我", "难过", "沮丧", "不开心", "郁闷",
                "气死我了", "气死了", "烦死了", "烦死我了", "受不了", "要疯了",
                "崩溃", "抓狂", "痛苦", "悲伤", "愤怒", "生气", "不爽", "烦躁"
            ],
            
            # 一般聊天关键词
            "general_chat": [
                "聊天", "闲聊", "随便聊聊", "说说话", "陪我聊天"
            ]
        }
    
    def _init_emotion_keywords(self):
        """初始化情感关键词"""
        self.emotion_keywords = {
            "negative": [
                "气死", "烦死", "郁闷", "沮丧", "难过", "不开心", "讨厌", "恶心",
                "糟糕", "倒霉", "失望", "绝望", "痛苦", "悲伤", "愤怒", "生气",
                "不爽", "烦躁", "抓狂", "崩溃", "受不了", "要疯了"
            ],
            "anxious": [
                "担心", "紧张", "害怕", "恐惧", "不安", "焦虑", "忧虑", "慌张",
                "着急", "急死", "怎么办", "完了", "糟了", "坏了", "紧急"
            ],
            "confused": [
                "不懂", "不明白", "搞不清", "糊涂", "迷茫", "困惑", "不知道",
                "怎么回事", "什么意思", "看不懂", "理解不了", "搞不懂"
            ],
            "positive": [
                "开心", "高兴", "兴奋", "激动", "满意", "棒", "好的", "太好了",
                "完美", "优秀", "赞", "喜欢", "爱", "感谢", "谢谢"
            ]
        }
    
    async def analyze(self, context: DecisionContext) -> AnalyzedContext:
        """
        分析决策上下文
        
        Args:
            context: 原始决策上下文
            
        Returns:
            AnalyzedContext: 分析后的上下文
        """
        try:
            # 并发执行各种分析
            import asyncio
            
            analysis_tasks = [
                self.analyze_intent(context.message),
                self.analyze_emotion(context.message),
                self.extract_entities(context.message)
            ]
            
            intent, emotion, entities = await asyncio.gather(*analysis_tasks)
            
            # 计算整体置信度
            confidence = self._calculate_overall_confidence(intent, emotion, entities, context)
            
            analyzed_context = AnalyzedContext(
                original_context=context,
                intent=intent,
                confidence=confidence,
                emotion=emotion,
                entities=entities
            )
            
            return analyzed_context
            
        except Exception as e:
            self.logger.error(f"上下文分析失败: {e}", exc_info=True)
            
            # 返回默认分析结果
            return AnalyzedContext(
                original_context=context,
                intent="general_chat",
                confidence=DecisionConfidence.LOW.value,
                emotion="neutral",
                entities={}
            )
    
    async def analyze_intent(self, message: str) -> str:
        """
        分析意图
        
        Args:
            message: 用户消息
            
        Returns:
            str: 识别的意图
        """
        message_lower = message.lower()
        detected_intents = []
        
        # 基于关键词匹配
        for intent, keywords in self.intent_keywords.items():
            for keyword in keywords:
                if keyword in message_lower:
                    detected_intents.append(intent)
                    break
        
        # 如果检测到多个意图，按优先级选择
        if detected_intents:
            # 优先级顺序（与SimplifiedDecisionEngine保持一致）
            priority_order = [
                "search_knowledge_base",
                "ask_question", 
                "business_requirement",
                "ask_introduction",
                "ask_capabilities",
                "greeting",
                "emotional_support",
                "general_chat"
            ]
            
            for priority_intent in priority_order:
                if priority_intent in detected_intents:
                    return priority_intent
        
        # 如果没有匹配到关键词，尝试LLM分析
        if self.llm_service:
            try:
                llm_intent = await self._llm_intent_analysis(message)
                if llm_intent:
                    return llm_intent
            except Exception as e:
                self.logger.warning(f"LLM意图分析失败: {e}")
        
        # 默认返回一般聊天
        return "general_chat"
    
    async def analyze_emotion(self, message: str) -> str:
        """
        分析情感
        
        Args:
            message: 用户消息
            
        Returns:
            str: 识别的情感
        """
        message_lower = message.lower()
        
        # 基于关键词匹配情感
        for emotion, keywords in self.emotion_keywords.items():
            for keyword in keywords:
                if keyword in message_lower:
                    return emotion
        
        # 如果没有明显的情感关键词，返回中性
        return "neutral"
    
    async def extract_entities(self, message: str) -> Dict[str, Any]:
        """
        提取实体
        
        Args:
            message: 用户消息
            
        Returns:
            Dict[str, Any]: 提取的实体
        """
        entities = {}
        
        # 提取常见实体类型
        
        # 1. 项目类型
        project_types = ["网站", "app", "应用", "系统", "平台", "海报", "设计", "开发"]
        for project_type in project_types:
            if project_type in message:
                entities["project_type"] = project_type
                break
        
        # 2. 数字和金额
        numbers = re.findall(r'\d+', message)
        if numbers:
            entities["numbers"] = numbers
        
        # 3. 时间表达
        time_patterns = [
            r'(\d+)天', r'(\d+)周', r'(\d+)月', r'(\d+)年',
            r'今天', r'明天', r'后天', r'下周', r'下月'
        ]
        for pattern in time_patterns:
            matches = re.findall(pattern, message)
            if matches:
                entities["time_expressions"] = matches
                break
        
        # 4. 联系方式
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        phone_pattern = r'1[3-9]\d{9}'
        
        emails = re.findall(email_pattern, message)
        phones = re.findall(phone_pattern, message)
        
        if emails:
            entities["emails"] = emails
        if phones:
            entities["phones"] = phones
        
        return entities
    
    def get_analysis_confidence(self, context: AnalyzedContext) -> float:
        """
        获取分析置信度
        
        Args:
            context: 分析后的上下文
            
        Returns:
            float: 分析置信度
        """
        return context.confidence
    
    def _calculate_overall_confidence(self, 
                                    intent: str, 
                                    emotion: str, 
                                    entities: Dict[str, Any],
                                    context: DecisionContext) -> float:
        """计算整体分析置信度"""
        confidence_factors = []
        
        # 1. 意图识别置信度
        if intent != "general_chat":
            confidence_factors.append(0.8)  # 明确意图
        else:
            confidence_factors.append(0.4)  # 模糊意图
        
        # 2. 情感分析置信度
        if emotion != "neutral":
            confidence_factors.append(0.7)  # 明确情感
        else:
            confidence_factors.append(0.5)  # 中性情感
        
        # 3. 实体提取置信度
        if entities:
            confidence_factors.append(0.6)  # 有实体
        else:
            confidence_factors.append(0.3)  # 无实体
        
        # 4. 消息长度因子
        message_length = len(context.message)
        if message_length > 20:
            confidence_factors.append(0.7)  # 详细消息
        elif message_length > 5:
            confidence_factors.append(0.5)  # 中等消息
        else:
            confidence_factors.append(0.3)  # 简短消息
        
        # 计算加权平均
        return sum(confidence_factors) / len(confidence_factors)
    
    async def _llm_intent_analysis(self, message: str) -> Optional[str]:
        """使用LLM进行意图分析"""
        if not self.llm_service:
            return None
        
        try:
            # 构建提示词
            prompt = f"""
            请分析以下用户消息的意图，从以下选项中选择最合适的一个：
            - greeting: 问候
            - business_requirement: 业务需求
            - ask_capabilities: 询问能力
            - ask_introduction: 要求自我介绍
            - search_knowledge_base: 知识库查询
            - emotional_support: 情感支持
            - general_chat: 一般聊天
            
            用户消息: "{message}"
            
            请只返回意图名称，不要其他内容。
            """
            
            # 调用LLM
            response = await self.llm_service.generate_response(prompt)
            
            # 解析响应
            intent = response.strip().lower()
            valid_intents = [
                "greeting", "business_requirement", "ask_capabilities",
                "ask_introduction", "search_knowledge_base", 
                "emotional_support", "general_chat"
            ]
            
            if intent in valid_intents:
                return intent
            
        except Exception as e:
            self.logger.error(f"LLM意图分析出错: {e}")
        
        return None


# 全局上下文分析器实例
_global_context_analyzer: Optional[ContextAnalyzer] = None


def get_context_analyzer(llm_service: Optional[Any] = None) -> ContextAnalyzer:
    """获取全局上下文分析器实例"""
    global _global_context_analyzer
    if _global_context_analyzer is None:
        _global_context_analyzer = ContextAnalyzer(llm_service)
    return _global_context_analyzer


def reset_context_analyzer() -> None:
    """重置全局上下文分析器（主要用于测试）"""
    global _global_context_analyzer
    _global_context_analyzer = None
