"""
AutoGen兼容的LLM服务Agent
"""
import json
import time
import hashlib
import aiohttp
import asyncio
from typing import Dict, Any, List, Optional

from .base import AutoGenBaseAgent
from ..utils.logging_config import get_logger
from backend.agents.llm_utils import (
    LLMError, LLMErrorType, classify_error, CircuitBreaker, ResponseCache
)
from backend.utils.performance_monitor import performance_monitor
from backend.agents.llm_interface import LLMServiceInterface
from backend.config.unified_config_loader import get_unified_config


class BaseHTTPClient:
    """基础HTTP客户端类，提供通用的HTTP请求功能"""

    def __init__(self, api_key: str, api_base: str, timeout: int, max_retries: int, provider: str):
        self.api_key = api_key
        self.api_base = api_base
        self.timeout = timeout
        self.max_retries = max_retries
        self.provider = provider
        self.logger = get_logger(f"backend.agents.llm_service.{provider.title()}Client")

    def _get_headers(self) -> Dict[str, str]:
        """获取请求头，子类可以重写此方法"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "Accept": "application/json"
        }

        # OpenRouter需要额外的头部
        if self.provider == "openrouter":
            headers.update({
                "HTTP-Referer": "https://autogen.com",
                "X-Title": "AutoGen"
            })

        return headers

    def _get_endpoint(self) -> str:
        """获取API端点，子类可以重写此方法"""
        if self.provider == "qwen-intent":
            return f"{self.api_base}/services/aigc/text-intent/detection"
        return f"{self.api_base}/chat/completions"

    def _build_request_body(self, **kwargs) -> Dict[str, Any]:
        """构建请求体，子类可以重写此方法"""
        if self.provider == "qwen-intent":
            # 提取用户消息内容
            user_message = ""
            for msg in kwargs["messages"]:
                if msg.get("role") == "user":
                    user_message = msg["content"]
                    break

            if not user_message:
                raise ValueError("未找到用户消息")

            return {
                "model": kwargs.get("model", "tongyi-intent-detect-v3"),
                "input": {
                    "text": user_message
                },
                "parameters": {
                    "result_type": "json"
                }
            }

        # 标准聊天完成请求体
        return {
            "model": kwargs.get("model", self._get_default_model()),
            "messages": [{
                "role": msg.get("role", "user"),
                "content": msg["content"]
            } for msg in kwargs["messages"]],
            "temperature": min(max(kwargs.get("temperature", get_unified_config().get_threshold("confidence.default", 0.7)), 0.0), 2.0),
            "max_tokens": min(kwargs.get("max_tokens", 1024), self._get_max_tokens()),
            "top_p": min(max(kwargs.get("top_p", 1.0), 0.0), 1.0),
            "stream": False
        }

    def _get_default_model(self) -> str:
        """获取默认模型名称"""
        model_map = {
            "deepseek": "deepseek-chat",
            "openrouter": "google/gemini-2.0-flash-001",
            "qwen": "qwen-plus",
            "qwen-intent": "tongyi-intent-detect-v3",
            "doubao": "doubao-1-5-thinking-pro-250415"
        }
        return model_map.get(self.provider, "unknown")

    def _get_max_tokens(self) -> int:
        """获取最大token数"""
        max_tokens_map = {
            "deepseek": 4096,
            "openrouter": 4096,
            "qwen": 8000,
            "qwen-intent": 4000,
            "doubao": 4096
        }
        return max_tokens_map.get(self.provider, 4096)

    def _process_response(self, response_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理响应数据，子类可以重写此方法"""
        if self.provider == "qwen-intent":
            # 将意图识别结果转换为标准格式
            intent_result = response_data.get("output", {}).get("intents", [])

            return {
                "choices": [
                    {
                        "message": {
                            "role": "assistant",
                            "content": json.dumps(intent_result, ensure_ascii=False)
                        }
                    }
                ],
                "usage": {
                    "prompt_tokens": len(str(intent_result)) // 4,
                    "completion_tokens": len(json.dumps(intent_result)) // 4,
                    "total_tokens": len(str(intent_result)) // 4 + len(json.dumps(intent_result)) // 4
                }
            }

        return response_data

    async def chat_completion(self, **kwargs) -> Dict[str, Any]:
        """执行聊天完成请求"""
        headers = self._get_headers()
        url = self._get_endpoint()
        request_body = self._build_request_body(**kwargs)

        # 记录请求信息
        self.logger.debug(f"API调用 - URL: {url}, 模型: {request_body.get('model', 'unknown')}")
        self.logger.debug(f"请求体: {json.dumps(request_body, ensure_ascii=False)[:200]}...")

        async with aiohttp.ClientSession() as session:
            for attempt in range(self.max_retries):
                try:
                    self.logger.debug(f"发送请求 (尝试 {attempt+1}/{self.max_retries})")
                    async with session.post(
                        url,
                        headers=headers,
                        json=request_body,
                        timeout=aiohttp.ClientTimeout(total=self.timeout)
                    ) as response:
                        response_text = await response.text()
                        self.logger.debug(f"响应状态: {response.status}")

                        if response.status != 200:
                            self.logger.error(f"请求失败 (尝试 {attempt+1}): {response.status}, 消息='{response_text}', URL='{url}'")
                            if attempt == self.max_retries - 1:
                                raise Exception(f"API请求失败: {response.status} {response_text}")
                            await asyncio.sleep(1 * (attempt + 1))
                            continue

                        result = await response.json()
                        self.logger.debug(f"响应体: {json.dumps(result, ensure_ascii=False)[:500]}...")

                        # 处理响应
                        processed_result = self._process_response(result)
                        return processed_result

                except aiohttp.ClientError as e:
                    self.logger.error(f"请求错误 (尝试 {attempt+1}): {str(e)}")
                    if attempt == self.max_retries - 1:
                        raise
                    await asyncio.sleep(1 * (attempt + 1))

            raise Exception("所有重试都失败了")


class AutoGenLLMServiceAgent(AutoGenBaseAgent, LLMServiceInterface):
    """AutoGen LLM服务代理"""
    
    def __init__(self,
                 enable_cache: bool = None,
                 cache_size: int = None,
                 cache_ttl: int = None,
                 enable_circuit_breaker: bool = True,
                 **kwargs):
        """初始化LLM服务Agent"""
        super().__init__(name="AutoGenLLMServiceAgent",
                         human_input_mode="NEVER",
                         llm_config=None,  # 禁用AutoGen的默认LLM配置
                         **kwargs)
        from backend.config import config_service
        self.config_service = config_service

        # 缓存统一配置对象，避免重复调用
        self.unified_config = get_unified_config()

        # 初始化客户端字典，用于存储不同提供商的客户端
        self.clients = {}
        self.client = None

        # 初始化默认客户端
        default_client = self._init_client(agent_name="llm_service")
        self.client = default_client

        # 从业务配置获取缓存设置
        try:
            from ..utils.performance_init import get_business_performance_config
            perf_config = get_business_performance_config()
            cache_config = perf_config.get("caching", {})
        except Exception as e:
            self.logger.warning(f"获取缓存配置失败，使用默认值: {e}")
            cache_config = {}

        # 设置缓存配置（参数优先，然后是业务配置，最后是默认值）
        self.enable_cache = (
            enable_cache if enable_cache is not None
            else cache_config.get("enable_caching", True)
        )
        cache_size = (
            cache_size if cache_size is not None
            else cache_config.get("max_cache_size", 100)
        )
        cache_ttl = (
            cache_ttl if cache_ttl is not None
            else cache_config.get("cache_ttl", 3600)
        )

        # 初始化缓存和断路器
        self.response_cache = ResponseCache(max_size=cache_size, ttl=cache_ttl) if self.enable_cache else None

        # 启用断路器功能
        self.enable_circuit_breaker = enable_circuit_breaker
        self.circuit_breakers = {}  # 为每个提供商维护独立的断路器

        # 初始化统计信息
        self.call_stats = {
            "total_calls": 0,
            "successful_calls": 0,
            "failed_calls": 0,
            "cache_hits": 0,
            "circuit_breaks": 0,
            "total_latency": 0
        }

        # 使用统一配置服务（用于获取场景参数）
        from backend.config import config_service
        self.config_service = config_service

    def _generate_cache_key(self, messages: List[Dict[str, str]], model_config: Dict[str, Any]) -> str:
        """生成缓存键"""
        # 创建包含消息和关键配置的字符串
        key_data = {
            "messages": messages,
            "model": model_config["model_name"],
            "temperature": model_config.get("temperature", self.unified_config.get_threshold("confidence.default", 0.7)),
            "max_tokens": model_config.get("max_tokens", 1024)
        }
        key_str = json.dumps(key_data, sort_keys=True, ensure_ascii=False)
        return hashlib.md5(key_str.encode('utf-8')).hexdigest()

    def _get_circuit_breaker(self, provider: str) -> CircuitBreaker:
        """获取或创建指定提供商的断路器"""
        if provider not in self.circuit_breakers:
            self.circuit_breakers[provider] = CircuitBreaker(
                failure_threshold=self.unified_config.get_threshold("performance.retry.persistent_retry", 5),
                recovery_time=self.unified_config.get_threshold("performance.timeout.long", 60)
            )
        return self.circuit_breakers[provider]

    def _validate_input(self, messages: List[Dict[str, str]], **kwargs) -> None:
        """验证输入参数"""
        if not messages:
            raise ValueError("消息列表不能为空")

        if not isinstance(messages, list):
            raise TypeError("消息必须是列表类型")

        for i, message in enumerate(messages):
            if not isinstance(message, dict):
                raise TypeError(f"消息[{i}]必须是字典类型")

            if "content" not in message:
                raise ValueError(f"消息[{i}]缺少必需的'content'字段")

            if not isinstance(message["content"], str):
                raise TypeError(f"消息[{i}]的'content'字段必须是字符串类型")

            if not message["content"].strip():
                raise ValueError(f"消息[{i}]的内容不能为空")

            # 验证角色字段（如果存在）
            if "role" in message and message["role"] not in ["user", "assistant", "system"]:
                raise ValueError(f"消息[{i}]的'role'字段值无效: {message['role']}")

        # 验证可选参数
        if "temperature" in kwargs:
            temp = kwargs["temperature"]
            if not isinstance(temp, (int, float)) or temp < 0 or temp > 2:
                raise ValueError("temperature参数必须是0-2之间的数值")

        if "max_tokens" in kwargs:
            max_tokens = kwargs["max_tokens"]
            if not isinstance(max_tokens, int) or max_tokens <= 0:
                raise ValueError("max_tokens参数必须是正整数")

        if "top_p" in kwargs:
            top_p = kwargs["top_p"]
            if not isinstance(top_p, (int, float)) or top_p <= 0 or top_p > 1:
                raise ValueError("top_p参数必须是0-1之间的数值")

    def _init_client(self, agent_name: str = None, model_name: str = None):
        """初始化LLM客户端
        参数:
            agent_name: 可选，指定关联的Agent名称
            model_name: 可选，直接指定模型名称
        """
        # 获取模型配置(支持通过agent_name或model_name指定)
        model_config = self.config_service.get_llm_config_with_metadata(agent_name or model_name or "default")

        # 调试输出
        self.logger.debug(f"获取到的模型配置: {model_config}")
        self.logger.debug(f"配置类型: {type(model_config)}")
        self.logger.debug(f"配置键: {list(model_config.keys()) if isinstance(model_config, dict) else 'Not a dict'}")

        # 保存当前配置
        self.config = model_config

        # 根据提供商创建对应的客户端
        if "provider" not in model_config:
            self.logger.error(f"模型配置中缺少provider字段: {model_config}")
            raise KeyError("模型配置中缺少provider字段")
        provider = model_config["provider"]

        # 如果已经有该提供商的客户端，直接返回
        if provider in self.clients:
            return self.clients[provider]

        # 使用统一的BaseHTTPClient创建客户端
        client = BaseHTTPClient(
            api_key=model_config["api_key"],
            api_base=model_config["api_base"],
            timeout=model_config.get("timeout", self.unified_config.get_threshold("performance.timeout.llm_service", 30)),
            max_retries=model_config.get("max_retries", self.unified_config.get_threshold("performance.retry.default", 3)),
            provider=provider
        )

        self.clients[provider] = client

        # 对于OpenAI，我们仍然使用官方客户端
        if provider == "openai":
            from openai import OpenAI
            openai_client = OpenAI(
                api_key=model_config["api_key"],
                base_url=model_config["api_base"],
                timeout=model_config.get("timeout", self.unified_config.get_threshold("performance.timeout.llm_service", 30)),
                max_retries=model_config.get("max_retries", self.unified_config.get_threshold("performance.retry.default", 3))
            )
            self.clients[provider] = openai_client
            client = openai_client

        # 设置默认客户端
        if agent_name == "llm_service" or not self.client:
            self.client = client

        return client

    async def _call_llm_with_retry(self, client: Any, params: Dict[str, Any]) -> Dict[str, Any]:
        """带重试机制的LLM调用
        参数:
            client: LLM客户端实例
            params: 调用参数
        返回:
            LLM响应字典
        """
        max_retries = params.get("max_retries", 3)
        retry_delay = params.get("retry_delay", 1)

        for attempt in range(max_retries + 1):
            try:
                if hasattr(client, "chat_completion"):
                    response = await client.chat_completion(**params)
                else:
                    response = await client.chat.completions.create(**params)

                # 处理DeepSeek/OpenAI不同格式的响应
                if isinstance(response, dict):  # DeepSeek格式
                    return {
                        "content": response["choices"][0]["message"]["content"],
                        "usage": response.get("usage"),
                        "model": response.get("model", "unknown")
                    }
                elif hasattr(response, "choices"):  # OpenAI格式
                    return {
                        "content": response.choices[0].message.content,
                        "usage": response.usage.dict() if hasattr(response, "usage") else None,
                        "model": getattr(response, "model", "unknown")
                    }
                else:
                    return response

            except Exception:
                if attempt == max_retries:
                    raise
                await asyncio.sleep(retry_delay * (attempt + 1))

        raise Exception("LLM调用失败")

    def _generate_prompt_summary(self, messages: List[Dict[str, str]], scenario: str = None, agent_name: str = None) -> str:
        """生成提示词摘要，用于终端日志显示

        Args:
            messages: 消息列表
            scenario: 场景描述
            agent_name: Agent名称

        Returns:
            摘要字符串
        """
        if not messages or len(messages) == 0:
            return "空消息列表"

        # 获取最后一条用户消息作为用户输入摘要
        user_input = "无用户输入"
        for msg in reversed(messages):
            if msg.get("role") == "user":
                content = msg.get("content", "")
                # 截取用户输入（最多30个字符）
                user_input = content[:30] + ("..." if len(content) > 30 else "")
                break

        # 计算总长度
        total_length = sum(len(str(msg.get("content", ""))) for msg in messages)

        # 识别任务类型
        task_type = "未知任务"
        if scenario:
            # 根据scenario识别任务类型
            scenario_mapping = {
                "intent_recognition": "意图识别",
                "domain_classification": "领域分类",
                "category_classification": "类别分类",
                "information_extraction": "信息提取",
                "domain_guidance": "领域引导",
                "clarification": "澄清问题",
                "document_generation": "文档生成"
            }
            task_type = scenario_mapping.get(scenario, scenario)
        elif agent_name:
            # 根据agent_name识别任务类型
            if "intent" in agent_name.lower():
                task_type = "意图识别"
            elif "domain" in agent_name.lower():
                task_type = "领域分类"
            elif "category" in agent_name.lower():
                task_type = "类别分类"
            elif "extraction" in agent_name.lower():
                task_type = "信息提取"
            elif "guidance" in agent_name.lower():
                task_type = "引导问题"
            else:
                task_type = agent_name

        # 尝试识别使用的模板（从消息内容中推断）
        template_name = "未知模板"
        for msg in messages:
            content = str(msg.get("content", ""))
            # 意图识别模板 - 优先检查，使用更精确的特征
            if ("意图识别提示词模板" in content or
                "基础意图类型（intent，必选其一）" in content or
                "子意图类型（sub_intent，根据主意图选择）" in content or
                "情感类型（emotion，必选其一）" in content):
                template_name = "intent_recognition.md"
                break
            # 领域分类模板
            elif ("领域分类专家" in content or "判断需求所属领域" in content or
                  "请对以下文本进行领域分类" in content or "分析用户输入和对话历史" in content):
                template_name = "domain_classifier.md"
                break
            # 类别分类模板
            elif ("类别分类" in content or "请对以下文本进行类别分类" in content):
                template_name = "category_classifier.md"
                break
            # 信息提取模板
            elif ("请从以下对话中提取关键信息" in content or "信息提取" in content):
                template_name = "information_extraction.md"
                break
            # 引导问题生成模板
            elif ("请为用户生成引导问题" in content or "引导问题" in content or
                  "领域引导问题生成模板" in content or "需求分析助手" in content):
                template_name = "domain_guidance.md"
                break
            # 优化问题生成模板（合并了问题生成和优化功能）
            elif ("智能问题生成模板" in content or "专业的需求沟通专家" in content or
                  "优化问题生成" in content or "一步到位" in content or
                  "optimized_question_generation" in content.lower() or
                  "需求分析师和对话体验专家" in content or
                  "沟通三原则" in content or "共情先行" in content):
                template_name = "optimized_question_generation.md"
                break
            # 澄清问题模板 - 使用更精确的特征
            elif ("追问生成模板" in content or "经验丰富的需求分析师" in content or
                  "针对用户不完整或模糊的回答" in content or "避免重复开场白" in content or
                  "focus_point_name" in content or "focus_point_description" in content):
                template_name = "clarification_question.md"
                break
            # 文档生成模板
            elif ("文档生成" in content or "生成需求文档" in content):
                template_name = "document_template.md"
                break
            # 审查和完善模板
            elif ("review_and_refine" in content.lower() or "审查和完善" in content or
                  "根据用户的反馈修改需求文档" in content or "当前文档" in content):
                template_name = "review_and_refine.md"
                break

        return f"正在为{task_type}处理, 用户输入: '{user_input}', 模板: {template_name}, 总长度: {total_length} 字符"

    async def call_llm(self,
                       messages: List[Dict[str, str]],
                       agent_name: Optional[str] = None,
                       scenario: Optional[str] = None,
                       session_id: Optional[str] = None,
                       **kwargs) -> Dict[str, Any]:
        """调用LLM API
        参数:
            messages: 消息列表
            model_name: 直接指定模型名称(可选)
            agent_name: 通过Agent名称获取配置(可选)
            session_id: 会话ID(可选)，用于日志记录
            scenario: 场景描述(可选)，用于日志记录
        """
        # 验证输入参数
        try:
            self._validate_input(messages, **kwargs)
        except (ValueError, TypeError) as e:
            raise LLMError(f"输入参数验证失败: {str(e)}", LLMErrorType.INVALID_REQUEST)

        # 获取带会话ID的日志记录器
        stage = scenario or agent_name or "llm_call"
        logger = get_logger(
            f"{self.__class__.__module__}.{self.__class__.__name__}",
            session_id=session_id,
            stage=stage
        )

        # 先获取实际要使用的模型配置
        # 优先使用scenario参数，如果没有则使用agent_name
        effective_agent_name = scenario or agent_name or "default"
        model_config = self.config_service.get_llm_config_with_metadata(effective_agent_name)

        # 生成缓存键
        cache_key = self._generate_cache_key(messages, model_config) if self.enable_cache else None

        # 尝试从缓存获取响应
        if self.enable_cache and self.response_cache and cache_key:
            cached_response = self.response_cache.get(cache_key)
            if cached_response:
                self.call_stats["cache_hits"] += 1
                logger.info(f"缓存命中 - 模型: {model_config['model_name']}, 场景: {scenario or agent_name or '未指定'}")
                return cached_response

        # 检查断路器状态
        provider = model_config["provider"]
        if self.enable_circuit_breaker:
            circuit_breaker = self._get_circuit_breaker(provider)
            if circuit_breaker.is_open:
                self.call_stats["circuit_breaks"] += 1
                logger.warning(f"断路器开启，跳过调用 - 提供商: {provider}")
                raise LLMError(f"断路器开启，提供商 {provider} 暂时不可用", LLMErrorType.CIRCUIT_BREAKER)

        # 生成摘要信息用于终端显示
        prompt_summary = self._generate_prompt_summary(messages, scenario, agent_name)

        # 记录调用开始，使用摘要信息
        logger.info(f"[LLM调用] {prompt_summary}")

        # 详细的输入消息只记录到文件日志（通过额外的logger）
        file_logger = get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}.detail")
        file_logger.debug(f"LLM调用详细信息 - 模型: {model_config['model_name']}, 场景: {scenario or agent_name or '未指定'}")
        file_logger.debug(f"输入消息: {json.dumps(messages, ensure_ascii=False)}")

        # 添加模型验证日志 - 显示场景到模型的映射
        scenario_info = scenario or agent_name or '未指定'
        self.logger.info(f"[模型验证] 场景: {scenario_info} -> 实际模型: {model_config['model_name']} ({model_config['provider']})")

        # 同时记录到session日志，确保内部组件的模型使用情况也被记录
        try:
            from backend.config.logging_config import SessionLogger
            # 创建临时的session logger用于记录模型使用
            temp_session_logger = SessionLogger(
                logger_name=f"{self.__class__.__module__}.{self.__class__.__name__}",
                session_id="system",  # 系统级别的模型使用记录
                user_id="system"
            )
            temp_session_logger.log_model_usage(
                scenario=scenario_info,
                model_name=model_config['model_name'],
                provider=model_config['provider']
            )
        except Exception as e:
            self.logger.debug(f"记录session模型使用日志失败: {e}")

        self.call_stats["total_calls"] += 1
        start_time = time.time()

        # 使用性能监控器跟踪LLM调用
        with performance_monitor.track_llm_call(
            provider=model_config["provider"],
            model=model_config["model_name"],
            operation=scenario or agent_name or "chat_completion"
        ):
            try:
                # 从统一配置获取场景参数
                scenario_params = self.config_service.get_scenario_params(scenario) if scenario else {}

                # 设置合理的超时时间，防止长时间等待
                timeout = kwargs.get("timeout", scenario_params.get("timeout", model_config.get("timeout", 30)))
                if timeout > 60:  # 限制最大超时时间为60秒
                    timeout = 60
                    logger.warning(f"超时时间过长，已限制为60秒")

                call_params = {
                    "messages": messages,
                    "model": model_config["model_name"],
                    "timeout": timeout,
                    **model_config,
                    **scenario_params,
                    **kwargs
                }

                # 记录实际调用参数
                logger.info(
                    f"调用参数: 模型={call_params['model']}, 温度={call_params.get('temperature', 0.7)}, max_tokens={call_params.get('max_tokens')}")

                # 获取或初始化对应的客户端
                provider = model_config["provider"]
                if provider not in self.clients:
                    client = self._init_client(model_name=model_config["model_name"], agent_name=effective_agent_name)
                else:
                    client = self.clients[provider]

                response = await self._call_llm_with_retry(client, call_params)

                # 记录调用结果（终端显示简化信息）
                elapsed = time.time() - start_time
                token_info = ""
                if 'usage' in response and response['usage']:
                    usage = response['usage']
                    token_info = f", Tokens: {usage.get('total_tokens', 'N/A')}"

                logger.info(f"[LLM结果] 成功 - 耗时: {elapsed:.2f}s{token_info}")

                # 记录LLM响应内容摘要到主日志
                response_content = response.get('content', '')
                content_preview = response_content[:200] + "..." if len(response_content) > 200 else response_content
                logger.info(f"[LLM响应] {content_preview}")

                # 详细的输出内容记录到文件日志
                file_logger.debug(f"完整输出内容: {json.dumps(response_content, ensure_ascii=False)}")
                if 'usage' in response and response['usage']:
                    file_logger.debug(f"Token使用情况: {json.dumps(response['usage'], ensure_ascii=False)}")

                # 记录模型信息到文件日志
                file_logger.debug(f"实际使用模型: {response.get('model', call_params['model'])}, 提供商: {provider}")

                # 添加耗时和token使用情况到响应中
                response["duration"] = elapsed
                response["token_usage"] = response.get("usage", {})

                # 记录断路器成功
                if self.enable_circuit_breaker:
                    circuit_breaker = self._get_circuit_breaker(provider)
                    circuit_breaker.record_success()

                # 缓存响应
                if self.enable_cache and self.response_cache and cache_key:
                    self.response_cache.set(cache_key, response)
                    logger.debug(f"响应已缓存 - 缓存键: {cache_key[:16]}...")

                self.call_stats["successful_calls"] += 1
                self.call_stats["total_latency"] += elapsed
                return response

            except LLMError:
                # 重新抛出已分类的LLM错误
                raise
            except ValueError as e:
                # 配置或参数错误
                elapsed = time.time() - start_time
                error = LLMError(f"参数错误: {str(e)}", LLMErrorType.INVALID_REQUEST)
                logger.error(f"[LLM结果] 失败 - 耗时: {elapsed:.2f}s, 错误: {error.error_type}")
                file_logger.error(f"详细错误信息: {str(error)}")
                self.call_stats["failed_calls"] += 1
                self.call_stats["total_latency"] += elapsed
                raise error
            except asyncio.TimeoutError as e:
                # 超时错误
                elapsed = time.time() - start_time
                error = LLMError(f"请求超时: {str(e)}", LLMErrorType.TIMEOUT)
                if self.enable_circuit_breaker:
                    circuit_breaker = self._get_circuit_breaker(provider)
                    circuit_breaker.record_failure()
                logger.error(f"[LLM结果] 失败 - 耗时: {elapsed:.2f}s, 错误: {error.error_type}")
                file_logger.error(f"详细错误信息: {str(error)}")
                self.call_stats["failed_calls"] += 1
                self.call_stats["total_latency"] += elapsed
                raise error
            except Exception as e:
                # 其他未分类错误
                error = classify_error(e, model_config["provider"])
                elapsed = time.time() - start_time

                # 记录断路器失败
                if self.enable_circuit_breaker:
                    circuit_breaker = self._get_circuit_breaker(provider)
                    circuit_breaker.record_failure()

                # 记录错误信息（终端显示简化信息）
                logger.error(f"[LLM结果] 失败 - 耗时: {elapsed:.2f}s, 错误: {error.error_type}")
                file_logger.error(f"详细错误信息: {str(error)}")

                self.call_stats["failed_calls"] += 1
                self.call_stats["total_latency"] += elapsed
                raise error

    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "model_name": self.config["model_name"],
            "provider": self.config["provider"],
            "max_tokens": self._get_max_tokens()
        }

    def get_stats(self) -> Dict[str, Any]:
        """获取调用统计信息"""
        stats = self.call_stats.copy()
        if stats["total_calls"] > 0:
            stats["success_rate"] = stats["successful_calls"] / stats["total_calls"]
            stats["average_latency"] = stats["total_latency"] / stats["total_calls"]
        else:
            stats["success_rate"] = 0.0
            stats["average_latency"] = 0.0

        # 添加断路器状态
        if self.enable_circuit_breaker:
            stats["circuit_breaker_status"] = {}
            for provider, breaker in self.circuit_breakers.items():
                stats["circuit_breaker_status"][provider] = {
                    "is_open": breaker.is_open,
                    "failure_count": breaker.failure_count
                }

        # 添加缓存状态
        if self.enable_cache and self.response_cache:
            stats["cache_size"] = len(self.response_cache.cache)
            stats["cache_hit_rate"] = (
                stats["cache_hits"] / stats["total_calls"]
                if stats["total_calls"] > 0 else 0.0
            )

        return stats

    def reset_stats(self) -> None:
        """重置统计信息"""
        self.call_stats = {
            "total_calls": 0,
            "successful_calls": 0,
            "failed_calls": 0,
            "cache_hits": 0,
            "circuit_breaks": 0,
            "total_latency": 0
        }

    def clear_cache(self) -> None:
        """清空缓存"""
        if self.enable_cache and self.response_cache:
            self.response_cache.clear()
