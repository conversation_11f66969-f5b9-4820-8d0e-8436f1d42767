#!/usr/bin/env python3
"""
对话状态机
实现基于状态机模式的对话状态管理
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass
from datetime import datetime

from backend.agents.unified_state_manager import (
    ConversationState, 
    UnifiedStateManager, 
    get_state_manager
)
from backend.config.unified_config_loader import get_unified_config


@dataclass
class StateResult:
    """状态处理结果"""
    success: bool
    new_state: Optional[ConversationState] = None
    response: Optional[str] = None
    actions: List[str] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.actions is None:
            self.actions = []
        if self.metadata is None:
            self.metadata = {}


class BaseState(ABC):
    """状态基类"""
    
    def __init__(self, state_machine: 'ConversationStateMachine'):
        self.state_machine = state_machine
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.config = get_unified_config()
    
    @abstractmethod
    async def handle_message(self, message: str, session_id: str, user_id: str, 
                           context: Dict[str, Any]) -> StateResult:
        """处理消息"""
        pass
    
    @abstractmethod
    def get_state_name(self) -> ConversationState:
        """获取状态名称"""
        pass
    
    async def on_enter(self, session_id: str, user_id: str, context: Dict[str, Any]):
        """进入状态时的处理"""
        self.logger.debug(f"进入状态: {self.get_state_name().value}")
    
    async def on_exit(self, session_id: str, user_id: str, context: Dict[str, Any]):
        """退出状态时的处理"""
        self.logger.debug(f"退出状态: {self.get_state_name().value}")


class IdleState(BaseState):
    """空闲状态"""
    
    def get_state_name(self) -> ConversationState:
        return ConversationState.IDLE
    
    async def handle_message(self, message: str, session_id: str, user_id: str, 
                           context: Dict[str, Any]) -> StateResult:
        """处理空闲状态下的消息"""
        message_lower = message.lower().strip()
        
        # 问候语处理
        greeting_keywords = self.config.get_keyword_rules().get('greeting', {}).get('keywords', [])
        if any(keyword in message_lower for keyword in greeting_keywords):
            templates = self.config.get_message_templates().get('greeting', {})
            responses = templates.get('responses', ['您好！我是您的AI需求分析师。'])
            response = responses[0] if responses else "您好！"
            
            return StateResult(
                success=True,
                new_state=ConversationState.IDLE,
                response=response,
                actions=['send_greeting']
            )
        
        # 业务需求处理
        business_keywords = self.config.get_keyword_rules().get('business_requirement', {}).get('keywords', [])
        if any(keyword in message_lower for keyword in business_keywords):
            templates = self.config.get_message_templates().get('requirement_collection', {})
            response = templates.get('start', '好的，让我来帮您分析这个项目。')
            
            return StateResult(
                success=True,
                new_state=ConversationState.COLLECTING_INFO,
                response=response,
                actions=['start_requirement_collection'],
                metadata={'initial_requirement': message}
            )
        
        # 系统能力查询
        question_keywords = self.config.get_keyword_rules().get('ask_question', {}).get('keywords', [])
        if any(keyword in message_lower for keyword in question_keywords):
            templates = self.config.get_message_templates().get('capabilities', {})
            response = templates.get('main', '我可以帮助您分析和整理项目需求。')
            
            return StateResult(
                success=True,
                new_state=ConversationState.IDLE,
                response=response,
                actions=['show_capabilities']
            )
        
        # 默认处理：尝试理解为业务需求
        return StateResult(
            success=True,
            new_state=ConversationState.COLLECTING_INFO,
            response="我来帮您分析这个需求，请详细说明您的想法。",
            actions=['start_requirement_collection'],
            metadata={'initial_requirement': message}
        )


class CollectingInfoState(BaseState):
    """信息收集状态"""
    
    def get_state_name(self) -> ConversationState:
        return ConversationState.COLLECTING_INFO
    
    async def handle_message(self, message: str, session_id: str, user_id: str, 
                           context: Dict[str, Any]) -> StateResult:
        """处理信息收集状态下的消息"""
        message_lower = message.lower().strip()
        
        # 确认关键词检查
        confirm_keywords = self.config.get_confirmation_keywords()
        if any(keyword in message_lower for keyword in confirm_keywords):
            return StateResult(
                success=True,
                new_state=ConversationState.DOCUMENTING,
                response="好的，我开始为您生成需求文档。",
                actions=['start_document_generation'],
                metadata={'confirmed_info': message}
            )
        
        # 重新开始请求
        if any(keyword in message_lower for keyword in ['重新开始', '重来', '重新']):
            return StateResult(
                success=True,
                new_state=ConversationState.IDLE,
                response="好的，我们重新开始。请告诉我您想要做什么项目？",
                actions=['restart_conversation']
            )
        
        # 默认处理：继续收集信息
        templates = self.config.get_message_templates().get('requirement_collection', {})
        response = templates.get('continue', '很好！请继续告诉我：')
        
        return StateResult(
            success=True,
            new_state=ConversationState.COLLECTING_INFO,
            response=response,
            actions=['collect_information'],
            metadata={'collected_info': message}
        )


class DocumentingState(BaseState):
    """文档生成状态"""
    
    def get_state_name(self) -> ConversationState:
        return ConversationState.DOCUMENTING
    
    async def handle_message(self, message: str, session_id: str, user_id: str, 
                           context: Dict[str, Any]) -> StateResult:
        """处理文档生成状态下的消息"""
        message_lower = message.lower().strip()
        
        # 确认文档
        confirm_keywords = self.config.get_confirmation_keywords()
        if any(keyword in message_lower for keyword in confirm_keywords):
            return StateResult(
                success=True,
                new_state=ConversationState.COMPLETED,
                response="文档已确认完成！如果您有新的需求，请随时告诉我。",
                actions=['confirm_document', 'complete_conversation']
            )
        
        # 修改请求
        if any(keyword in message_lower for keyword in ['修改', '改', '调整', '更新']):
            return StateResult(
                success=True,
                new_state=ConversationState.DOCUMENTING,
                response="好的，我来为您修改文档。请告诉我需要修改的内容。",
                actions=['modify_document'],
                metadata={'modification_request': message}
            )
        
        # 重新开始
        if any(keyword in message_lower for keyword in ['重新开始', '重来', '重新']):
            return StateResult(
                success=True,
                new_state=ConversationState.IDLE,
                response="好的，我们重新开始。请告诉我您想要做什么项目？",
                actions=['restart_conversation']
            )
        
        # 默认处理：继续文档相关操作
        return StateResult(
            success=True,
            new_state=ConversationState.DOCUMENTING,
            response="请告诉我您希望如何处理这个文档？",
            actions=['handle_document_operation'],
            metadata={'user_input': message}
        )


class CompletedState(BaseState):
    """完成状态"""
    
    def get_state_name(self) -> ConversationState:
        return ConversationState.COMPLETED
    
    async def handle_message(self, message: str, session_id: str, user_id: str, 
                           context: Dict[str, Any]) -> StateResult:
        """处理完成状态下的消息"""
        message_lower = message.lower().strip()
        
        # 新需求
        business_keywords = self.config.get_keyword_rules().get('business_requirement', {}).get('keywords', [])
        if any(keyword in message_lower for keyword in business_keywords):
            return StateResult(
                success=True,
                new_state=ConversationState.COLLECTING_INFO,
                response="好的，让我来帮您分析这个新项目。",
                actions=['start_new_requirement'],
                metadata={'new_requirement': message}
            )
        
        # 问候语
        greeting_keywords = self.config.get_keyword_rules().get('greeting', {}).get('keywords', [])
        if any(keyword in message_lower for keyword in greeting_keywords):
            return StateResult(
                success=True,
                new_state=ConversationState.IDLE,
                response="您好！有什么新的项目需要我帮您分析吗？",
                actions=['reset_to_idle']
            )
        
        # 默认处理：回到空闲状态
        return StateResult(
            success=True,
            new_state=ConversationState.IDLE,
            response="有什么新的需求我可以帮您分析吗？",
            actions=['reset_to_idle']
        )


class ConversationStateMachine:
    """对话状态机"""
    
    def __init__(self, state_manager: Optional[UnifiedStateManager] = None):
        self.logger = logging.getLogger(__name__)
        self.state_manager = state_manager or get_state_manager()
        
        # 初始化状态
        self.states = {
            ConversationState.IDLE: IdleState(self),
            ConversationState.COLLECTING_INFO: CollectingInfoState(self),
            ConversationState.DOCUMENTING: DocumentingState(self),
            ConversationState.COMPLETED: CompletedState(self)
        }
        
        self.logger.info("对话状态机初始化完成")
    
    async def process_message(self, message: str, session_id: str, user_id: str, 
                            context: Optional[Dict[str, Any]] = None) -> StateResult:
        """处理消息"""
        if context is None:
            context = {}
        
        try:
            # 获取当前状态
            current_state = await self.state_manager.get_conversation_state(session_id, user_id)
            
            # 获取状态处理器
            state_handler = self.states.get(current_state)
            if not state_handler:
                self.logger.error(f"未找到状态处理器: {current_state}")
                return StateResult(
                    success=False,
                    response="系统错误，请稍后重试。"
                )
            
            # 处理消息
            result = await state_handler.handle_message(message, session_id, user_id, context)
            
            # 如果需要状态转换
            if result.new_state and result.new_state != current_state:
                # 执行状态转换
                await self._transition_state(current_state, result.new_state, 
                                           session_id, user_id, context)
            
            return result
            
        except Exception as e:
            self.logger.error(f"处理消息失败: {e}", exc_info=True)
            return StateResult(
                success=False,
                response="处理消息时出现错误，请稍后重试。"
            )
    
    async def _transition_state(self, from_state: ConversationState, 
                              to_state: ConversationState,
                              session_id: str, user_id: str, 
                              context: Dict[str, Any]):
        """执行状态转换"""
        try:
            # 退出当前状态
            current_handler = self.states.get(from_state)
            if current_handler:
                await current_handler.on_exit(session_id, user_id, context)
            
            # 更新状态管理器中的状态
            await self.state_manager.set_conversation_state(session_id, user_id, to_state, "state_machine")
            
            # 进入新状态
            new_handler = self.states.get(to_state)
            if new_handler:
                await new_handler.on_enter(session_id, user_id, context)
            
            self.logger.info(f"状态转换完成: {from_state.value} -> {to_state.value}")
            
        except Exception as e:
            self.logger.error(f"状态转换失败: {e}", exc_info=True)
    
    async def get_current_state(self, session_id: str, user_id: str) -> ConversationState:
        """获取当前状态"""
        return await self.state_manager.get_conversation_state(session_id, user_id)
    
    async def reset_conversation(self, session_id: str, user_id: str) -> bool:
        """重置对话"""
        return await self.state_manager.reset_session_state(session_id, user_id)


# 创建全局实例
_state_machine_instance = None


def get_conversation_state_machine(state_manager: Optional[UnifiedStateManager] = None) -> ConversationStateMachine:
    """获取对话状态机实例"""
    global _state_machine_instance
    if _state_machine_instance is None:
        _state_machine_instance = ConversationStateMachine(state_manager)
    return _state_machine_instance


if __name__ == "__main__":
    # 测试状态机
    import asyncio
    
    async def test_state_machine():
        print("🧪 测试对话状态机...")
        
        state_machine = get_conversation_state_machine()
        session_id = "test_session"
        user_id = "test_user"
        
        # 测试问候语
        result = await state_machine.process_message("你好", session_id, user_id)
        print(f"问候语处理: {result.success}, 响应: {result.response}")
        
        # 测试业务需求
        result = await state_machine.process_message("我想做一个网站", session_id, user_id)
        print(f"业务需求处理: {result.success}, 新状态: {result.new_state}, 响应: {result.response}")
        
        # 测试信息收集
        result = await state_machine.process_message("这是一个电商网站", session_id, user_id)
        print(f"信息收集: {result.success}, 响应: {result.response}")
        
        # 测试确认
        result = await state_machine.process_message("确认", session_id, user_id)
        print(f"确认处理: {result.success}, 新状态: {result.new_state}, 响应: {result.response}")
        
        print("🎉 状态机测试完成!")
    
    asyncio.run(test_state_machine())
