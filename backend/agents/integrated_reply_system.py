"""
整合决策引擎与回复系统 - 统一管理决策到回复的完整流程
版本: v1.0
作者: AI Assistant
创建时间: 2025-06-20

功能:
1. 整合决策引擎、消息回复管理器和动态回复生成器
2. 提供统一的决策到回复转换接口
3. 确保统一配置中的策略配置与实际回复处理的一致性
4. 提供决策结果验证和回复质量保证
5. 支持决策链路的完整监控和分析
"""

import logging
import time
from typing import Dict, Any, List, Optional
from enum import Enum
from dataclasses import dataclass
from .simplified_decision_engine import SimplifiedDecisionEngine
from .message_reply_manager import MessageReplyManager, MessageType
from .dynamic_reply_generator import DynamicReplyGenerator, DynamicReplyFactory


class DecisionType(Enum):
    """决策类型枚举"""
    STATIC_REPLY = "static_reply"           # 静态回复决策
    DYNAMIC_REPLY = "dynamic_reply"         # 动态回复决策
    HANDLER_EXECUTION = "handler_execution"  # 处理器执行决策
    HYBRID_REPLY = "hybrid_reply"           # 混合回复决策


@dataclass
class DecisionContext:
    """决策上下文"""
    intent: str
    emotion: str
    current_state: str
    session_id: str
    user_id: str = ""  # 添加用户ID字段
    user_message: str = ""
    conversation_history: List[Dict[str, Any]] = None
    additional_context: Dict[str, Any] = None
    sub_intent: Optional[str] = None  # 新增子意图字段
    
    def __post_init__(self):
        if self.conversation_history is None:
            self.conversation_history = []
        if self.additional_context is None:
            self.additional_context = {}


@dataclass
class ReplyResult:
    """回复结果"""
    content: str
    decision_type: DecisionType
    strategy_used: Dict[str, Any]
    execution_time: float
    success: bool
    fallback_used: bool = False
    error_message: str = None

# Action策略映射器
class ActionStrategyMapper:
    """Action策略映射器 - 分析action指令并确定最佳回复策略"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 定义action到回复策略的映射
        self.action_strategy_map = {
            # 静态回复类型
            "reset_conversation": {
                "type": DecisionType.STATIC_REPLY,
                "reply_key": "reset_confirmation",
                "description": "重置会话确认"
            },
            "restart_conversation": {
                "type": DecisionType.HANDLER_EXECUTION,
                "handler_method": "handle_restart_conversation",
                "description": "重新开始对话"
            },
            "finalize_and_reset": {
                "type": DecisionType.HANDLER_EXECUTION,
                "handler_method": "handle_finalize_and_reset",
                "description": "文档完成确认并重置状态"
            },
            
            # 动态回复类型
            "respond_with_greeting": {
                "type": DecisionType.DYNAMIC_REPLY,
                "factory_method": "generate_greeting_reply",
                "description": "动态问候回复"
            },
            "show_empathy_and_clarify": {
                "type": DecisionType.DYNAMIC_REPLY,
                "factory_method": "generate_empathy_reply",
                "description": "共情澄清回复"
            },
            "apologize_and_request_refinement": {
                "type": DecisionType.DYNAMIC_REPLY,
                "factory_method": "generate_apology_reply",
                "description": "道歉请求改进回复"
            },
            "request_clarification": {
                "type": DecisionType.DYNAMIC_REPLY,
                "factory_method": "generate_clarification_reply",
                "description": "请求澄清回复"
            },
            
            # 处理器执行类型（需要复杂业务逻辑）
            "start_requirement_gathering": {
                "type": DecisionType.HANDLER_EXECUTION,
                "handler_method": "_process_intent",
                "description": "开始需求收集"
            },
            "continue_requirement_gathering": {
                "type": DecisionType.HANDLER_EXECUTION,
                "handler_method": "_process_intent",
                "description": "继续需求收集"
            },
            "process_answer_and_ask_next": {
                "type": DecisionType.HANDLER_EXECUTION,
                "handler_method": "handle_process_answer_and_ask_next",
                "description": "处理回答并问下一个"
            },
            "skip_question_and_ask_next": {
                "type": DecisionType.HANDLER_EXECUTION,
                "handler_method": "handle_skip_question_and_ask_next",
                "description": "跳过问题并问下一个"
            },
            "execute_document_modification": {
                "type": DecisionType.HANDLER_EXECUTION,
                "handler_method": "handle_document_modification",
                "description": "执行文档修改"
            },
            "provide_suggestions": {
                "type": DecisionType.HANDLER_EXECUTION,
                "handler_method": "handle_provide_suggestions",
                "description": "提供建议"
            },
            
            # 混合回复类型（简单处理+动态回复）
            "acknowledge_and_redirect": {
                "type": DecisionType.HYBRID_REPLY,
                "factory_method": "generate_clarification_reply",
                "reply_key": "clarification_request",
                "description": "确认并引导回复"
            },
            "handle_unknown_situation": {
                "type": DecisionType.HYBRID_REPLY,
                "factory_method": "generate_clarification_reply",
                "reply_key": "unknown_action",
                "description": "处理未知情况"
            },
            # 增加通用请求处理决策
            "handle_general_request": {
                "type": DecisionType.HANDLER_EXECUTION,
                "handler_method": "_process_general_request",
                "description": "处理通用请求"
            }
        }
    
    def get_strategy(self, action: str) -> Dict[str, Any]:
        """获取action对应的回复策略"""
        strategy = self.action_strategy_map.get(action)
        if not strategy:
            self.logger.warning(f"未找到action '{action}' 的策略映射，使用默认策略")
            return {
                "type": DecisionType.STATIC_REPLY,
                "reply_key": "unknown_action",
                "description": "默认未知处理"
            }
        return strategy
    
    def is_dynamic_reply_action(self, action: str) -> bool:
        """判断action是否需要动态回复"""
        strategy = self.get_strategy(action)
        return strategy["type"] in [DecisionType.DYNAMIC_REPLY, DecisionType.HYBRID_REPLY]
    
    def is_handler_execution_action(self, action: str) -> bool:
        """判断action是否需要执行处理器"""
        strategy = self.get_strategy(action)
        return strategy["type"] == DecisionType.HANDLER_EXECUTION


class IntegratedReplySystem:
    """整合回复系统 - 统一管理决策到回复的完整流程"""
    
    def __init__(self, llm_client=None, strategies_path: str = None, reply_manager=None, dynamic_reply_generator=None):
        """
        初始化整合回复系统

        Args:
            llm_client: LLM客户端实例
            strategies_path: 策略配置文件路径（已废弃，使用统一配置）
            reply_manager: 可选的MessageReplyManager实例（依赖注入）
            dynamic_reply_generator: 可选的DynamicReplyGenerator实例（依赖注入）
        """
        self.logger = logging.getLogger(__name__)

        # 跟踪初始化
        from backend.config.unified_config_loader import _init_tracker
        _init_tracker.track_init("IntegratedReplySystem")

        # 初始化各个组件（不再使用strategies_path参数）
        self.decision_engine = SimplifiedDecisionEngine()

        # 使用依赖注入的reply_manager，如果没有则创建新实例
        if reply_manager is not None:
            self.reply_manager = reply_manager
            self.logger.info("使用注入的MessageReplyManager实例")
        else:
            self.reply_manager = MessageReplyManager(llm_client)
            self.logger.info("创建新的MessageReplyManager实例")

        # 使用依赖注入的dynamic_reply_generator，避免重复创建
        if dynamic_reply_generator is not None:
            self.dynamic_reply_generator = dynamic_reply_generator
            self.logger.info("使用注入的DynamicReplyGenerator实例")
        else:
            self.dynamic_reply_generator = DynamicReplyGenerator(llm_client)
            self.logger.info("创建新的DynamicReplyGenerator实例")

        self.reply_factory = DynamicReplyFactory(self.dynamic_reply_generator)
        self.strategy_mapper = ActionStrategyMapper()
        
        # 性能统计
        self.stats = {
            "total_decisions": 0,
            "successful_replies": 0,
            "fallback_replies": 0,
            "handler_executions": 0,
            "decision_type_distribution": {
                "static": 0,
                "dynamic": 0,
                "handler": 0,
                "hybrid": 0
            }
        }
        
        self.logger.info("整合回复系统初始化完成")
    
    async def process_decision_to_reply(
        self,
        context: DecisionContext,
        conversation_flow_instance=None
    ) -> ReplyResult:
        """
        处理从决策到回复的完整流程
        
        Args:
            context: 决策上下文
            conversation_flow_instance: ConversationFlow实例（用于执行handler方法）
            
        Returns:
            ReplyResult: 回复结果
        """
        import time
        start_time = time.time()
        
        self.stats["total_decisions"] += 1
        
        try:
            # 1. 统一使用决策引擎获取策略 - 所有决策都来源于strategies.yaml
            decision_context = {
                "current_state": context.current_state,
                **context.additional_context
            }

            strategy = self.decision_engine.get_strategy(
                context.intent,
                context.emotion,
                decision_context,
                context.sub_intent
            )

            action = strategy.get("action")
            sub_intent_info = f", sub_intent={context.sub_intent}" if context.sub_intent else ""
            self.logger.info(f"使用决策引擎获取策略: state={context.current_state}, intent={context.intent}, emotion={context.emotion}{sub_intent_info}, action={action}")

            if not action:
                self.logger.error("决策策略中缺少action字段")
                return await self._create_fallback_result(context, strategy, start_time)

            # 2. 分析action类型并选择处理方式
            action_strategy = self.strategy_mapper.get_strategy(action)
            decision_type = action_strategy["type"]

            self.stats["decision_type_distribution"][decision_type.value.split("_")[0]] += 1

            # 3. 根据决策类型执行相应的回复生成
            if decision_type == DecisionType.STATIC_REPLY:
                reply_content = await self._handle_static_reply(action_strategy, context, strategy)
            elif decision_type == DecisionType.DYNAMIC_REPLY:
                reply_content = await self._handle_dynamic_reply(action_strategy, context, strategy)
            elif decision_type == DecisionType.HANDLER_EXECUTION:
                reply_content = await self._handle_handler_execution(
                    action_strategy, context, strategy, conversation_flow_instance
                )
            elif decision_type == DecisionType.HYBRID_REPLY:
                reply_content = await self._handle_hybrid_reply(action_strategy, context, strategy)
            else:
                reply_content = await self._handle_fallback_reply(context, strategy)
            
            execution_time = time.time() - start_time
            
            if reply_content:
                self.stats["successful_replies"] += 1
                return ReplyResult(
                    content=reply_content,
                    decision_type=decision_type,
                    strategy_used=strategy,
                    execution_time=execution_time,
                    success=True
                )
            else:
                return await self._create_fallback_result(context, strategy, start_time)
                
        except Exception as e:
            self.logger.error(f"处理决策到回复时出错: {e}", exc_info=True)
            return await self._create_fallback_result(context, strategy, start_time, str(e))
    
    async def _handle_static_reply(
        self,
        action_strategy: Dict[str, Any],
        context: DecisionContext,
        strategy: Dict[str, Any]
    ) -> str:
        """处理静态回复"""
        reply_key = action_strategy.get("reply_key")
        if not reply_key:
            self.logger.error(f"静态回复策略缺少reply_key: {action_strategy}")
            return None
        
        return await self.reply_manager.get_reply(
            reply_key=reply_key,
            message_type=MessageType.STATIC,
            context={"session_id": context.session_id}
        )
    
    async def _handle_dynamic_reply(
        self,
        action_strategy: Dict[str, Any],
        context: DecisionContext,
        strategy: Dict[str, Any]
    ) -> str:
        """处理动态回复"""
        factory_method = action_strategy.get("factory_method")
        if not factory_method:
            self.logger.error(f"动态回复策略缺少factory_method: {action_strategy}")
            return None

        # 获取工厂方法
        method = getattr(self.reply_factory, factory_method, None)
        if not method:
            self.logger.error(f"工厂方法不存在: {factory_method}")
            return None

        # 调用工厂方法生成回复
        prompt_instruction = strategy.get("prompt_instruction", "")

        # 检查方法是否支持emotion参数
        import inspect
        method_signature = inspect.signature(method)

        if 'emotion' in method_signature.parameters:
            return await method(
                prompt_instruction=prompt_instruction,
                user_message=context.user_message,
                session_id=context.session_id,
                emotion=context.emotion
            )
        else:
            # 向后兼容，不传递emotion参数
            return await method(
                prompt_instruction=prompt_instruction,
                user_message=context.user_message,
                session_id=context.session_id
            )
    
    async def _handle_handler_execution(
        self,
        action_strategy: Dict[str, Any],
        context: DecisionContext,
        strategy: Dict[str, Any],
        conversation_flow_instance
    ) -> str:
        """处理需要执行handler的情况"""
        if not conversation_flow_instance:
            self.logger.error("需要执行handler但未提供conversation_flow_instance")
            return None
        
        handler_method = action_strategy.get("handler_method")
        if not handler_method:
            self.logger.error(f"处理器执行策略缺少handler_method: {action_strategy}")
            return None
        
        # 获取handler方法
        method = getattr(conversation_flow_instance, handler_method, None)
        if not method:
            self.logger.error(f"处理器方法不存在: {handler_method}")
            return None
        
        self.stats["handler_executions"] += 1
        
        # 构建决策结果
        decision_result = {
            "intent": context.intent,
            "emotion": context.emotion,
            "decision": strategy
        }
        
        # 安全地调用handler方法
        return await self._call_handler_safely(
            method,
            message=context.user_message,
            session_id=context.session_id,
            user_id=context.user_id,
            decision_result=decision_result,
            history=context.conversation_history
        )
    
    async def _call_handler_safely(self, handler, **kwargs):
        """
        安全地调用handler方法，只传入它需要的参数

        Args:
            handler: 要调用的handler方法
            **kwargs: 可能的参数

        Returns:
            handler的返回值
        """
        import inspect

        try:
            # 获取handler方法的签名
            sig = inspect.signature(handler)

            # 检查是否有**kwargs参数
            has_var_keyword = any(
                param.kind == inspect.Parameter.VAR_KEYWORD
                for param in sig.parameters.values()
            )

            # 过滤出handler实际需要的参数
            filtered_kwargs = {}
            for param_name, param in sig.parameters.items():
                if param_name in kwargs:
                    filtered_kwargs[param_name] = kwargs[param_name]

            # 如果handler有**kwargs参数，传入所有剩余参数
            if has_var_keyword:
                for key, value in kwargs.items():
                    if key not in filtered_kwargs:
                        filtered_kwargs[key] = value

            # 调用handler
            return await handler(**filtered_kwargs)

        except Exception as e:
            self.logger.error(f"调用handler {handler.__name__} 失败: {e}", exc_info=True)
            # 如果智能调用失败，尝试无参数调用
            try:
                return await handler()
            except Exception as e2:
                self.logger.error(f"无参数调用handler {handler.__name__} 也失败: {e2}")
                raise e  # 抛出原始异常

    async def _handle_hybrid_reply(
        self,
        action_strategy: Dict[str, Any],
        context: DecisionContext,
        strategy: Dict[str, Any]
    ) -> str:
        """处理混合回复"""
        # 先尝试动态回复
        factory_method = action_strategy.get("factory_method")
        if factory_method:
            method = getattr(self.reply_factory, factory_method, None)
            if method:
                prompt_instruction = strategy.get("prompt_instruction", "")

                # 检查方法是否支持emotion参数
                import inspect
                method_signature = inspect.signature(method)

                if 'emotion' in method_signature.parameters:
                    dynamic_reply = await method(
                        prompt_instruction=prompt_instruction,
                        user_message=context.user_message,
                        session_id=context.session_id,
                        emotion=context.emotion
                    )
                else:
                    # 向后兼容，不传递emotion参数
                    dynamic_reply = await method(
                        prompt_instruction=prompt_instruction,
                        user_message=context.user_message,
                        session_id=context.session_id
                    )

                if dynamic_reply:
                    return dynamic_reply
        
        # 动态回复失败，使用静态回复
        reply_key = action_strategy.get("reply_key")
        if reply_key:
            return await self.reply_manager.get_reply(
                reply_key=reply_key,
                message_type=MessageType.STATIC,
                context={"session_id": context.session_id}
            )
        
        return None
    
    async def _handle_fallback_reply(
        self,
        context: DecisionContext,
        strategy: Dict[str, Any]
    ) -> str:
        """处理回退回复"""
        return await self.reply_manager.get_reply(
            reply_key="unknown_action",
            message_type=MessageType.STATIC,
            context={"session_id": context.session_id}
        )
    
    async def _create_fallback_result(
        self,
        context: DecisionContext,
        strategy: Dict[str, Any],
        start_time: float,
        error_message: str = None
    ) -> ReplyResult:
        """创建回退结果"""
        self.stats["fallback_replies"] += 1
        
        fallback_content = await self._handle_fallback_reply(context, strategy)
        execution_time = time.time() - start_time
        
        return ReplyResult(
            content=fallback_content,
            decision_type=DecisionType.STATIC_REPLY,
            strategy_used=strategy,
            execution_time=execution_time,
            success=False,
            fallback_used=True,
            error_message=error_message
        )
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        total = max(self.stats["total_decisions"], 1)
        return {
            **self.stats,
            "success_rate": self.stats["successful_replies"] / total,
            "fallback_rate": self.stats["fallback_replies"] / total,
            "handler_execution_rate": self.stats["handler_executions"] / total
        }
    
    def validate_strategies_config(self) -> Dict[str, Any]:
        """验证strategies.yaml配置与action映射的一致性"""
        validation_result = {
            "valid": True,
            "missing_actions": [],
            "unmapped_actions": [],
            "recommendations": []
        }
        
        # 获取strategies.yaml中定义的所有action
        config_actions = set()
        for state_config in self.decision_engine.strategies_config.values():
            if isinstance(state_config, dict):
                for intent_config in state_config.values():
                    if isinstance(intent_config, dict):
                        for emotion_config in intent_config.values():
                            if isinstance(emotion_config, dict) and "action" in emotion_config:
                                config_actions.add(emotion_config["action"])
        
        # 获取映射器中定义的所有action
        mapped_actions = set(self.strategy_mapper.action_strategy_map.keys())
        
        # 检查缺失的映射
        missing_actions = config_actions - mapped_actions
        if missing_actions:
            validation_result["valid"] = False
            validation_result["missing_actions"] = list(missing_actions)
            validation_result["recommendations"].append(
                f"需要为以下action添加映射: {', '.join(missing_actions)}"
            )
        
        # 检查多余的映射
        unmapped_actions = mapped_actions - config_actions
        if unmapped_actions:
            validation_result["unmapped_actions"] = list(unmapped_actions)
            validation_result["recommendations"].append(
                f"以下action映射可能不再需要: {', '.join(unmapped_actions)}"
            )
        
        return validation_result
