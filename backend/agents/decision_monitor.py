"""
决策监控系统

提供决策引擎的全面监控功能：
- 性能指标收集
- 策略使用统计
- 错误率监控
- 响应时间分析
- 实时监控面板数据
"""

import logging
import time
import threading
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from collections import defaultdict, deque
from datetime import datetime, timedelta
import json

from .decision_types import DecisionResult, DecisionContext


@dataclass
class PerformanceMetrics:
    """性能指标"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    total_response_time: float = 0.0
    min_response_time: float = float('inf')
    max_response_time: float = 0.0
    
    def add_request(self, response_time: float, success: bool = True):
        """添加请求记录"""
        self.total_requests += 1
        if success:
            self.successful_requests += 1
        else:
            self.failed_requests += 1
        
        self.total_response_time += response_time
        self.min_response_time = min(self.min_response_time, response_time)
        self.max_response_time = max(self.max_response_time, response_time)
    
    @property
    def average_response_time(self) -> float:
        """平均响应时间"""
        return self.total_response_time / self.total_requests if self.total_requests > 0 else 0.0
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        return self.successful_requests / self.total_requests if self.total_requests > 0 else 0.0
    
    @property
    def error_rate(self) -> float:
        """错误率"""
        return self.failed_requests / self.total_requests if self.total_requests > 0 else 0.0


@dataclass
class StrategyMetrics:
    """策略使用指标"""
    name: str
    usage_count: int = 0
    total_response_time: float = 0.0
    success_count: int = 0
    error_count: int = 0
    confidence_sum: float = 0.0
    
    def add_usage(self, response_time: float, confidence: float, success: bool = True):
        """添加使用记录"""
        self.usage_count += 1
        self.total_response_time += response_time
        self.confidence_sum += confidence
        
        if success:
            self.success_count += 1
        else:
            self.error_count += 1
    
    @property
    def average_response_time(self) -> float:
        """平均响应时间"""
        return self.total_response_time / self.usage_count if self.usage_count > 0 else 0.0
    
    @property
    def average_confidence(self) -> float:
        """平均置信度"""
        return self.confidence_sum / self.usage_count if self.usage_count > 0 else 0.0
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        return self.success_count / self.usage_count if self.usage_count > 0 else 0.0


@dataclass
class RequestRecord:
    """请求记录"""
    timestamp: float
    message: str
    intent: str
    strategy_name: str
    action: str
    response_time: float
    confidence: float
    success: bool
    error_message: Optional[str] = None


class DecisionMonitor:
    """决策监控器"""
    
    def __init__(self, max_records: int = 1000):
        """
        初始化决策监控器
        
        Args:
            max_records: 最大记录数量
        """
        self.logger = logging.getLogger(__name__)
        self.max_records = max_records
        
        # 性能指标
        self.overall_metrics = PerformanceMetrics()
        self.strategy_metrics: Dict[str, StrategyMetrics] = {}
        self.intent_metrics: Dict[str, PerformanceMetrics] = defaultdict(PerformanceMetrics)
        
        # 请求记录（使用deque实现固定大小的环形缓冲区）
        self.request_records: deque[RequestRecord] = deque(maxlen=max_records)
        
        # 时间窗口统计（最近1小时）
        self.hourly_stats: deque[RequestRecord] = deque()
        
        # 线程锁
        self._lock = threading.RLock()
        
        self.logger.info(f"决策监控器初始化完成: max_records={max_records}")
    
    def record_decision(self, 
                       context: DecisionContext,
                       result: DecisionResult,
                       response_time: float,
                       success: bool = True,
                       error_message: Optional[str] = None):
        """
        记录决策结果
        
        Args:
            context: 决策上下文
            result: 决策结果
            response_time: 响应时间
            success: 是否成功
            error_message: 错误信息（如果有）
        """
        with self._lock:
            # 创建请求记录
            record = RequestRecord(
                timestamp=time.time(),
                message=context.message[:100],  # 限制长度
                intent=result.intent,
                strategy_name=result.strategy_name,
                action=result.action,
                response_time=response_time,
                confidence=result.confidence,
                success=success,
                error_message=error_message
            )
            
            # 添加到记录队列
            self.request_records.append(record)
            self.hourly_stats.append(record)
            
            # 更新整体指标
            self.overall_metrics.add_request(response_time, success)
            
            # 更新策略指标
            if result.strategy_name not in self.strategy_metrics:
                self.strategy_metrics[result.strategy_name] = StrategyMetrics(result.strategy_name)
            
            self.strategy_metrics[result.strategy_name].add_usage(
                response_time, result.confidence, success
            )
            
            # 更新意图指标
            self.intent_metrics[result.intent].add_request(response_time, success)
            
            # 清理过期的小时统计
            self._cleanup_hourly_stats()
            
            if not success and error_message:
                self.logger.warning(f"决策失败记录: {error_message}")
    
    def _cleanup_hourly_stats(self):
        """清理过期的小时统计数据"""
        cutoff_time = time.time() - 3600  # 1小时前
        while self.hourly_stats and self.hourly_stats[0].timestamp < cutoff_time:
            self.hourly_stats.popleft()
    
    def get_overall_stats(self) -> Dict[str, Any]:
        """获取整体统计信息"""
        with self._lock:
            return {
                "total_requests": self.overall_metrics.total_requests,
                "successful_requests": self.overall_metrics.successful_requests,
                "failed_requests": self.overall_metrics.failed_requests,
                "success_rate_percent": round(self.overall_metrics.success_rate * 100, 2),
                "error_rate_percent": round(self.overall_metrics.error_rate * 100, 2),
                "average_response_time_ms": round(self.overall_metrics.average_response_time * 1000, 2),
                "min_response_time_ms": round(self.overall_metrics.min_response_time * 1000, 2),
                "max_response_time_ms": round(self.overall_metrics.max_response_time * 1000, 2),
                "records_count": len(self.request_records)
            }
    
    def get_strategy_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取策略统计信息"""
        with self._lock:
            stats = {}
            for name, metrics in self.strategy_metrics.items():
                stats[name] = {
                    "usage_count": metrics.usage_count,
                    "success_count": metrics.success_count,
                    "error_count": metrics.error_count,
                    "success_rate_percent": round(metrics.success_rate * 100, 2),
                    "average_response_time_ms": round(metrics.average_response_time * 1000, 2),
                    "average_confidence": round(metrics.average_confidence, 3)
                }
            return stats
    
    def get_intent_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取意图统计信息"""
        with self._lock:
            stats = {}
            for intent, metrics in self.intent_metrics.items():
                stats[intent] = {
                    "total_requests": metrics.total_requests,
                    "successful_requests": metrics.successful_requests,
                    "failed_requests": metrics.failed_requests,
                    "success_rate_percent": round(metrics.success_rate * 100, 2),
                    "average_response_time_ms": round(metrics.average_response_time * 1000, 2)
                }
            return stats
    
    def get_hourly_stats(self) -> Dict[str, Any]:
        """获取最近1小时的统计信息"""
        with self._lock:
            self._cleanup_hourly_stats()
            
            if not self.hourly_stats:
                return {
                    "total_requests": 0,
                    "success_rate_percent": 0,
                    "average_response_time_ms": 0,
                    "requests_per_minute": 0
                }
            
            total_requests = len(self.hourly_stats)
            successful_requests = sum(1 for r in self.hourly_stats if r.success)
            total_response_time = sum(r.response_time for r in self.hourly_stats)
            
            # 计算每分钟请求数
            time_span = self.hourly_stats[-1].timestamp - self.hourly_stats[0].timestamp
            requests_per_minute = (total_requests / (time_span / 60)) if time_span > 0 else 0
            
            return {
                "total_requests": total_requests,
                "successful_requests": successful_requests,
                "success_rate_percent": round((successful_requests / total_requests) * 100, 2),
                "average_response_time_ms": round((total_response_time / total_requests) * 1000, 2),
                "requests_per_minute": round(requests_per_minute, 2)
            }
    
    def get_recent_errors(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取最近的错误记录"""
        with self._lock:
            errors = []
            for record in reversed(self.request_records):
                if not record.success and len(errors) < limit:
                    errors.append({
                        "timestamp": datetime.fromtimestamp(record.timestamp).isoformat(),
                        "message": record.message,
                        "intent": record.intent,
                        "strategy": record.strategy_name,
                        "error": record.error_message,
                        "response_time_ms": round(record.response_time * 1000, 2)
                    })
            return errors
    
    def get_performance_trends(self, minutes: int = 60) -> Dict[str, List[float]]:
        """获取性能趋势数据"""
        with self._lock:
            cutoff_time = time.time() - (minutes * 60)
            recent_records = [r for r in self.hourly_stats if r.timestamp >= cutoff_time]
            
            if not recent_records:
                return {"timestamps": [], "response_times": [], "success_rates": []}
            
            # 按分钟分组
            minute_buckets = defaultdict(list)
            for record in recent_records:
                minute_key = int(record.timestamp // 60) * 60
                minute_buckets[minute_key].append(record)
            
            timestamps = []
            response_times = []
            success_rates = []
            
            for minute_timestamp in sorted(minute_buckets.keys()):
                records = minute_buckets[minute_timestamp]
                timestamps.append(minute_timestamp)
                
                avg_response_time = sum(r.response_time for r in records) / len(records)
                response_times.append(round(avg_response_time * 1000, 2))
                
                success_count = sum(1 for r in records if r.success)
                success_rate = (success_count / len(records)) * 100
                success_rates.append(round(success_rate, 2))
            
            return {
                "timestamps": timestamps,
                "response_times": response_times,
                "success_rates": success_rates
            }
    
    def reset_stats(self):
        """重置所有统计信息"""
        with self._lock:
            self.overall_metrics = PerformanceMetrics()
            self.strategy_metrics.clear()
            self.intent_metrics.clear()
            self.request_records.clear()
            self.hourly_stats.clear()
            self.logger.info("监控统计信息已重置")


# 全局监控实例
_global_monitor: Optional[DecisionMonitor] = None


def get_decision_monitor(max_records: int = 1000) -> DecisionMonitor:
    """
    获取全局决策监控器实例
    
    Args:
        max_records: 最大记录数量
        
    Returns:
        DecisionMonitor: 监控器实例
    """
    global _global_monitor
    if _global_monitor is None:
        _global_monitor = DecisionMonitor(max_records)
    return _global_monitor


# 导出公共接口
__all__ = [
    'DecisionMonitor',
    'PerformanceMetrics',
    'StrategyMetrics',
    'RequestRecord',
    'get_decision_monitor'
]
