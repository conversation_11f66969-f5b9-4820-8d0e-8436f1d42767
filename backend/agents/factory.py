#!/usr/bin/env python3
"""
Agent工厂和依赖注入容器

提供Agent的创建、配置和依赖管理功能，实现依赖注入模式，
减少模块间的直接耦合，提高可测试性和可维护性。

核心概念：
1. Agent工厂：负责创建和配置Agent实例
2. 依赖容器：管理Agent之间的依赖关系
3. 服务注册：注册可重用的服务实例
4. 懒加载：按需创建Agent实例
5. 配置驱动：通过配置文件控制Agent创建

使用方式：
```python
from backend.agents.factory import agent_factory

# 获取配置好的Agent实例
conversation_flow = agent_factory.get_conversation_flow_agent(session_id)
llm_service = agent_factory.get_llm_service()
document_generator = agent_factory.get_document_generator()
```
"""

import threading
from typing import Dict, Any, Type, Callable, TypeVar
from dataclasses import dataclass
from weakref import WeakValueDictionary

# 导入通用模块
from backend.utils.common_imports import Dict, List, Any, Callable, get_logger
from backend.config import config_service

# Agent类型变量
T = TypeVar('T')


@dataclass
class ServiceDescriptor:
    """服务描述符"""
    service_type: Type
    factory_func: Callable
    singleton: bool = True
    dependencies: List[str] = None
    
    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = []


class DependencyInjectionContainer:
    """依赖注入容器
    
    管理服务的注册、创建和生命周期
    """
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self._services: Dict[str, ServiceDescriptor] = {}
        self._instances: Dict[str, Any] = {}
        self._lock = threading.RLock()
        
        # 弱引用缓存，避免循环引用
        self._weak_cache: WeakValueDictionary = WeakValueDictionary()
        
        self.logger.info("依赖注入容器初始化完成")
    
    def register_service(self, name: str, service_type: Type, 
                        factory_func: Callable, singleton: bool = True,
                        dependencies: List[str] = None) -> None:
        """
        注册服务
        
        Args:
            name: 服务名称
            service_type: 服务类型
            factory_func: 工厂函数
            singleton: 是否单例
            dependencies: 依赖列表
        """
        with self._lock:
            self._services[name] = ServiceDescriptor(
                service_type=service_type,
                factory_func=factory_func,
                singleton=singleton,
                dependencies=dependencies or []
            )
            self.logger.debug(f"注册服务: {name}")
    
    def register_singleton(self, name: str, instance: Any) -> None:
        """注册单例实例"""
        with self._lock:
            self._instances[name] = instance
            self.logger.debug(f"注册单例实例: {name}")
    
    def get_service(self, name: str) -> Any:
        """
        获取服务实例
        
        Args:
            name: 服务名称
            
        Returns:
            服务实例
        """
        with self._lock:
            # 检查是否有直接注册的实例
            if name in self._instances:
                return self._instances[name]
            
            # 检查弱引用缓存
            if name in self._weak_cache:
                return self._weak_cache[name]
            
            # 检查服务描述符
            if name not in self._services:
                raise ValueError(f"未注册的服务: {name}")
            
            descriptor = self._services[name]
            
            # 解析依赖
            dependencies = {}
            for dep_name in descriptor.dependencies:
                dependencies[dep_name] = self.get_service(dep_name)
            
            # 创建实例
            try:
                instance = descriptor.factory_func(**dependencies)
                
                # 如果是单例，缓存实例
                if descriptor.singleton:
                    self._instances[name] = instance
                else:
                    # 非单例使用弱引用缓存
                    self._weak_cache[name] = instance
                
                self.logger.debug(f"创建服务实例: {name}")
                return instance
                
            except Exception as e:
                self.logger.error(f"创建服务实例失败: {name}, 错误: {e}")
                raise
    
    def clear_cache(self) -> None:
        """清除缓存"""
        with self._lock:
            self._instances.clear()
            self._weak_cache.clear()
            self.logger.info("依赖注入容器缓存已清除")
    
    def get_service_info(self) -> Dict[str, Any]:
        """获取服务信息"""
        with self._lock:
            return {
                "registered_services": list(self._services.keys()),
                "cached_instances": list(self._instances.keys()),
                "total_services": len(self._services)
            }


class AgentFactory:
    """Agent工厂
    
    负责创建和配置各种Agent实例，实现依赖注入
    """
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.container = DependencyInjectionContainer()
        self._initialization_lock = threading.Lock()
        self._initialized = False

        # 延迟初始化，避免启动时的重复加载
        self._lazy_initialize()

    def _lazy_initialize(self):
        """延迟初始化，避免重复初始化"""
        with self._initialization_lock:
            if self._initialized:
                return

            self.logger.info("开始初始化Agent工厂...")

            # 注册核心服务
            self._register_core_services()

            # 注册Agent服务
            self._register_agent_services()

            self._initialized = True
            self.logger.info("Agent工厂初始化完成")
    
    def _register_core_services(self) -> None:
        """注册核心服务"""
        
        # 配置服务
        self.container.register_singleton("config_service", config_service)
        
        # 数据库管理器
        self.container.register_service(
            "database_manager",
            type(None),  # 实际类型在工厂函数中确定
            self._create_database_manager,
            singleton=True
        )
        
        # 各种数据管理器
        self.container.register_service(
            "message_manager",
            type(None),
            self._create_message_manager,
            singleton=True,
            dependencies=["database_manager", "config_service"]
        )
        
        self.container.register_service(
            "document_manager", 
            type(None),
            self._create_document_manager,
            singleton=True,
            dependencies=["config_service"]
        )
        
        self.container.register_service(
            "focus_point_manager",
            type(None), 
            self._create_focus_point_manager,
            singleton=True,
            dependencies=["database_manager", "config_service"]
        )
        
        # LLM服务
        self.container.register_service(
            "llm_service",
            type(None),
            self._create_llm_service,
            singleton=True,
            dependencies=["config_service"]
        )
        
        # 提示加载器
        self.container.register_service(
            "prompt_loader",
            type(None),
            self._create_prompt_loader,
            singleton=True
        )
    
    def _register_agent_services(self) -> None:
        """注册Agent服务"""

        # 知识库Agent
        self.container.register_service(
            "knowledge_base_agent",
            type(None),
            self._create_knowledge_base_agent,
            singleton=True,
            dependencies=["config_service"]
        )

        # 文档生成器
        self.container.register_service(
            "document_generator",
            type(None),
            self._create_document_generator,
            singleton=True,
            dependencies=["llm_service", "database_manager"]
        )

        # 信息提取器Agent
        self.container.register_service(
            "information_extractor_agent",
            type(None),
            self._create_information_extractor_agent,
            singleton=True,
            dependencies=["llm_service"]
        )

        # 意图决策引擎
        self.container.register_service(
            "intent_decision_engine",
            type(None),
            self._create_intent_decision_engine,
            singleton=True,
            dependencies=["llm_service"]
        )
        
        # 状态管理器
        self.container.register_service(
            "state_manager",
            type(None),
            self._create_state_manager,
            singleton=False,  # 每个会话一个实例
            dependencies=["database_manager", "focus_point_manager"]
        )
        
        # 会话管理器
        self.container.register_service(
            "session_manager",
            type(None),
            self._create_session_manager,
            singleton=False,
            dependencies=["database_manager"]
        )
        
        # 消息处理器
        self.container.register_service(
            "message_processor",
            type(None),
            self._create_message_processor,
            singleton=False,
            dependencies=["intent_decision_engine", "message_manager"]
        )
        
        # 整合回复系统（新增）
        self.container.register_service(
            "integrated_reply_system",
            type(None),
            self._create_integrated_reply_system,
            singleton=True,
            dependencies=["llm_service"]
        )
        
        # 领域分类器
        self.container.register_service(
            "domain_classifier_agent",
            type(None),
            self._create_domain_classifier_agent,
            singleton=True,
            dependencies=["llm_service"]
        )
        
        # 分类器
        self.container.register_service(
            "category_classifier_agent",
            type(None),
            self._create_category_classifier_agent,
            singleton=True,
            dependencies=["llm_service"]
        )

        # ==================== 混合AI代理服务 ====================

        # 知识库配置管理器
        self.container.register_service(
            "knowledge_base_config_manager",
            type(None),
            self._create_knowledge_base_config_manager,
            singleton=True
        )

        # RAG知识库代理
        self.container.register_service(
            "rag_knowledge_base_agent",
            type(None),
            self._create_rag_knowledge_base_agent,
            singleton=True,
            dependencies=["llm_service", "knowledge_base_config_manager"]
        )

        # 注意：已移除 hybrid_intent_recognition_engine 和 hybrid_conversation_router
        # 统一使用 SimplifiedDecisionEngine + ActionExecutor 架构

        # 知识库管理器
        self.container.register_service(
            "knowledge_base_manager",
            type(None),
            self._create_knowledge_base_manager,
            singleton=True
        )
    
    # ==================== 核心服务工厂方法 ====================
    
    def _create_database_manager(self) -> Any:
        """创建数据库管理器"""
        from backend.data.db.database_manager import DatabaseManager
        from backend.config.settings import DATABASE_PATH
        return DatabaseManager(str(DATABASE_PATH))
    
    def _create_message_manager(self, database_manager: Any, config_service: Any) -> Any:
        """创建消息管理器"""
        from backend.data.db.message_manager import MessageManager
        return MessageManager(database_manager)
    
    def _create_document_manager(self, config_service: Any) -> Any:
        """创建文档管理器"""
        from backend.data.db.document_manager import DocumentManager
        return DocumentManager(config_service)
    
    def _create_focus_point_manager(self, database_manager: Any, config_service: Any) -> Any:
        """创建焦点管理器"""
        from backend.data.db.focus_point_manager import FocusPointManager
        return FocusPointManager(database_manager)
    
    def _create_llm_service(self, config_service: Any) -> Any:
        """创建LLM服务"""
        from backend.agents.llm_service import AutoGenLLMServiceAgent
        from backend.agents.llm_interface import LLMServiceInterface
        
        # 创建具体的LLM服务实例
        llm_service = AutoGenLLMServiceAgent()
        
        # 确保它实现了LLMServiceInterface接口
        assert isinstance(llm_service, LLMServiceInterface), "LLM服务必须实现LLMServiceInterface接口"
        
        return llm_service
    
    def _create_prompt_loader(self) -> Any:
        """获取全局提示加载器实例"""
        from backend.utils.prompt_loader import _loader, PromptLoader
        from backend.agents.llm_interface import PromptLoaderInterface
        
        # 确保提示加载器实现了PromptLoaderInterface接口
        assert isinstance(_loader, PromptLoaderInterface), "提示加载器必须实现PromptLoaderInterface接口"
        
        return _loader
    
    # ==================== Agent工厂方法 ====================
    
    def _create_knowledge_base_agent(self, config_service: Any) -> Any:
        """创建知识库Agent"""
        from backend.agents.knowledge_base import KnowledgeBaseAgent
        # KnowledgeBaseAgent期望的是db_path字符串，不是config_service对象
        return KnowledgeBaseAgent()
    
    def _create_document_generator(self, llm_service: Any, database_manager: Any) -> Any:
        """创建文档生成器"""
        from backend.agents.document_generator import DocumentGenerator
        return DocumentGenerator(
            llm_client=llm_service,
            agent_name="document_generator",
            db_manager=database_manager
        )

    def _create_information_extractor_agent(self, llm_service: Any) -> Any:
        """创建信息提取器Agent"""
        from backend.agents.information_extractor import InformationExtractorAgent
        return InformationExtractorAgent(llm_service=llm_service)

    def _create_intent_decision_engine(self, llm_service: Any) -> Any:
        """创建意图决策引擎"""
        from backend.agents.simplified_decision_engine import get_simplified_decision_engine
        return get_simplified_decision_engine()

    # 新增方法
    def _create_integrated_reply_system(self, llm_service: Any) -> Any:
        """创建整合回复系统"""
        from backend.agents.integrated_reply_system import IntegratedReplySystem
        return IntegratedReplySystem(llm_client=llm_service)
    
    def _create_domain_classifier_agent(self, llm_service: Any) -> Any:
        """创建领域分类器"""
        from backend.agents.domain_classifier import DomainClassifierAgent
        return DomainClassifierAgent(llm_client=llm_service, agent_name="domain_classifier")
    
    def _create_category_classifier_agent(self, llm_service: Any) -> Any:
        """创建分类器"""
        from backend.agents.category_classifier import CategoryClassifierAgent
        return CategoryClassifierAgent(llm_client=llm_service, agent_name="category_classifier")

    # ==================== 混合AI代理工厂方法 ====================

    def _create_knowledge_base_config_manager(self) -> Any:
        """创建知识库配置管理器"""
        from backend.config.knowledge_base_config import get_knowledge_base_config_manager
        return get_knowledge_base_config_manager()

    def _create_rag_knowledge_base_agent(self, llm_service: Any,
                                        knowledge_base_config_manager: Any) -> Any:
        """创建RAG知识库代理"""
        from backend.agents.rag_knowledge_base_agent import RAGKnowledgeBaseAgent
        from backend.agents.llm_interface import LLMServiceInterface

        # 确保LLM服务实现了接口
        assert isinstance(llm_service, LLMServiceInterface), "LLM服务必须实现LLMServiceInterface接口"

        try:
            # 总是创建RAG代理，但在代理内部检查功能是否启用
            return RAGKnowledgeBaseAgent(
                llm_service=llm_service,
                agent_name="rag_knowledge_base"
            )
        except Exception as e:
            self.logger.error(f"创建RAG知识库代理失败: {e}")
            return None





    def _create_knowledge_base_manager(self) -> Any:
        """创建知识库管理器"""
        from backend.agents.knowledge_base_manager import KnowledgeBaseManager

        try:
            return KnowledgeBaseManager()
        except Exception as e:
            self.logger.error(f"创建知识库管理器失败: {e}")
            return None
    
    def _create_state_manager(self, database_manager: Any, focus_point_manager: Any) -> Any:
        """创建状态管理器"""
        from backend.agents.conversation_flow.state_manager import StateManager
        return StateManager(database_manager, focus_point_manager)
    
    def _create_session_manager(self, database_manager: Any) -> Any:
        """创建会话管理器（简化版）"""
        from backend.agents.conversation_flow.session_manager import SessionManager
        # 为了快速修复，暂时传入None作为占位符
        # TODO: 完善session_context_manager和history_service的创建
        return SessionManager(database_manager, None, None)
    
    def _create_message_processor(self, intent_decision_engine: Any, message_manager: Any) -> Any:
        """创建消息处理器"""
        from backend.agents.session_context import SessionContextManager

        # 创建必要的依赖
        SessionContextManager(self.container.get_service("database_manager"))
        # 注意：这个方法不应该被调用，因为会导致循环依赖
        # 使用_create_message_processor_for_agent代替
        raise NotImplementedError("使用_create_message_processor_for_agent方法代替")
    
    def _create_message_processor_for_agent(self, agent) -> Any:
        """为特定agent创建MessageProcessor实例（解决循环依赖）"""
        from backend.agents.conversation_flow.message_processor import MessageProcessor
        from backend.agents.session_context import SessionContextManager
        from backend.handlers.action_executor import ActionExecutor

        # 获取必要的依赖
        intent_decision_engine = self.container.get_service("intent_decision_engine")
        message_manager = self.container.get_service("message_manager")
        session_context_manager = SessionContextManager(self.container.get_service("database_manager"))

        # 优先复用agent已有的ActionExecutor，避免重复初始化
        if hasattr(agent, 'action_executor') and agent.action_executor is not None:
            action_executor = agent.action_executor
            self.logger.debug("复用agent现有的ActionExecutor实例")
        else:
            # 如果agent没有ActionExecutor，则创建新的并设置给agent
            action_executor = ActionExecutor(conversation_flow=agent)
            agent.action_executor = action_executor
            self.logger.debug("为agent创建新的ActionExecutor实例")

        # 注意：MessageProcessor 不再需要 hybrid_conversation_router 参数
        # 统一使用 SimplifiedDecisionEngine + ActionExecutor 架构

        return MessageProcessor(
            message_manager,
            session_context_manager,
            action_executor,
            agent.knowledge_base_agent,
            intent_decision_engine
        )
    
    # ==================== 公开的获取方法 ====================
    
    def get_conversation_flow_agent(self, session_id: str, **kwargs) -> Any:
        """
        创建对话流程Agent
        
        Args:
            session_id: 会话ID
            **kwargs: 额外参数
            
        Returns:
            配置好的ConversationFlowAgent实例
        """
        try:
            # 获取所有依赖（除了message_processor，它需要在agent创建后单独设置）
            dependencies = {
                "llm_service": self.container.get_service("llm_service"),
                "document_generator": self.container.get_service("document_generator"),
                "information_extractor_agent": self.container.get_service("information_extractor_agent"),
                "knowledge_base_agent": self.container.get_service("knowledge_base_agent"),
                "message_manager": self.container.get_service("message_manager"),
                "document_manager": self.container.get_service("document_manager"),
                "focus_point_manager": self.container.get_service("focus_point_manager"),
                "database_manager": self.container.get_service("database_manager"),  # 添加数据库管理器
                "intent_decision_engine": self.container.get_service("intent_decision_engine"),
                "state_manager": self.container.get_service("state_manager"),
                "session_manager": self.container.get_service("session_manager"),
                "config_service": self.container.get_service("config_service"),
                "prompt_loader": self.container.get_service("prompt_loader"),
                # 新增 integrated_reply_system 依赖注入
                "integrated_reply_system": self.container.get_service("integrated_reply_system"),
                # 新增分类器依赖注入
                "domain_classifier_agent": self.container.get_service("domain_classifier_agent"),
                "category_classifier_agent": self.container.get_service("category_classifier_agent"),
                # 混合AI代理依赖注入
                # 注意：hybrid_conversation_router 已移除，统一使用 SimplifiedDecisionEngine 架构
                "hybrid_conversation_router": None,  # 保持参数兼容性，但传递 None
                "knowledge_base_config_manager": self.container.get_service("knowledge_base_config_manager"),
            }
    
            # 添加会话特定参数
            dependencies.update(kwargs)
            dependencies["session_id"] = session_id
            
            # 创建Agent实例
            from backend.agents.conversation_flow.core_refactored import AutoGenConversationFlowAgent
            agent = AutoGenConversationFlowAgent(**dependencies)
            
            # 创建message_processor并设置到agent中（解决循环依赖）
            message_processor = self._create_message_processor_for_agent(agent)
            agent.message_processor = message_processor
            
            self.logger.info(f"创建ConversationFlowAgent成功: {session_id}")
            return agent
            
        except Exception as e:
            self.logger.error(f"创建ConversationFlowAgent失败: {session_id}, 错误: {e}")
            raise
    
    def get_llm_service(self) -> Any:
        """获取LLM服务"""
        return self.container.get_service("llm_service")
    
    def get_document_generator(self) -> Any:
        """获取文档生成器"""
        return self.container.get_service("document_generator")
    
    def get_knowledge_base_agent(self) -> Any:
        """获取知识库Agent"""
        return self.container.get_service("knowledge_base_agent")
    
    def get_message_manager(self) -> Any:
        """获取消息管理器"""
        return self.container.get_service("message_manager")
    
    def get_database_manager(self) -> Any:
        """获取数据库管理器"""
        return self.container.get_service("database_manager")

    # ==================== 混合AI代理获取方法 ====================

    def get_knowledge_base_config_manager(self) -> Any:
        """获取知识库配置管理器"""
        return self.container.get_service("knowledge_base_config_manager")

    def get_rag_knowledge_base_agent(self) -> Any:
        """获取RAG知识库代理"""
        return self.container.get_service("rag_knowledge_base_agent")



    def get_knowledge_base_manager(self) -> Any:
        """获取知识库管理器"""
        return self.container.get_service("knowledge_base_manager")
    
    # ==================== 管理方法 ====================
    
    def clear_cache(self) -> None:
        """清除所有缓存"""
        self.container.clear_cache()
        self.logger.info("Agent工厂缓存已清除")
    
    def get_factory_info(self) -> Dict[str, Any]:
        """获取工厂信息"""
        container_info = self.container.get_service_info()
        return {
            "factory_name": "AgentFactory",
            "container_info": container_info,
            "available_agents": [
                "conversation_flow_agent",
                "llm_service",
                "document_generator",
                "knowledge_base_agent",
                "message_manager",
                "database_manager",
                # 知识库相关代理（保留RAG功能）
                "knowledge_base_config_manager",
                "rag_knowledge_base_agent",
                "knowledge_base_manager"
            ]
        }
    
    def register_custom_service(self, name: str, factory_func: Callable, 
                              dependencies: List[str] = None, singleton: bool = True) -> None:
        """
        注册自定义服务
        
        Args:
            name: 服务名称
            factory_func: 工厂函数
            dependencies: 依赖列表
            singleton: 是否单例
        """
        self.container.register_service(
            name=name,
            service_type=type(None),
            factory_func=factory_func,
            singleton=singleton,
            dependencies=dependencies
        )
        self.logger.info(f"注册自定义服务: {name}")


# ==================== 全局实例 ====================

# 创建全局Agent工厂实例
agent_factory = AgentFactory()

# 导出主要接口
__all__ = [
    'AgentFactory',
    'DependencyInjectionContainer', 
    'ServiceDescriptor',
    'agent_factory',
]