"""
ConversationFlow回复系统混入类
将回复相关功能从主类中分离出来，保持代码整洁和可维护性

本模块提供了ConversationFlow的回复系统功能，包括：
- 新回复系统组件的初始化和管理
- 整合决策引擎的处理逻辑
- 统一的回复获取和管理
- 回复质量监控和分析
- 回退机制和错误处理

通过混入类设计模式，将回复相关的复杂逻辑从主类中分离，
提高代码的可维护性和可扩展性。
"""

from typing import Dict, Any, List
from backend.config.unified_config_loader import get_unified_config

#  ConversationFlow回复系统混入类
class ConversationFlowReplyMixin:
    """
    这个混入类为ConversationFlow提供了完整的回复系统功能，包括：

    核心功能：
    - 新回复系统组件的初始化（MessageReplyManager、DynamicReplyGenerator等）
    - 整合决策引擎的消息处理流程
    - 统一的回复获取接口，替换原有的18个静态方法
    - 回复质量监控和性能分析
    - 智能回退机制，确保系统稳定性

    设计特点：
    - 模块化设计：将回复功能从主类中分离
    - 容错性强：提供完整的回退机制
    - 监控完善：记录所有回复指标和质量分数
    - 异步支持：支持异步组件初始化和处理

    使用方式：
    通过多重继承的方式混入到ConversationFlow主类中：
    ```python
    class AutoGenConversationFlowAgent(AutoGenBaseAgent, ConversationFlowReplyMixin):
        pass
    ```

    依赖组件：
    - MessageReplyManager: 统一回复管理器
    - DynamicReplyGenerator: 动态回复生成器
    - IntegratedReplySystem: 整合决策引擎
    - TemplateVersionManager: 模板版本管理器
    - ReplyMonitoringSystem: 监控分析系统

    注意事项：
    - 所有组件都有回退机制，初始化失败不会影响系统运行
    - 监控系统会记录所有回复的性能指标
    - 支持热插拔，组件可以独立启用/禁用
    """
    
    # ==================== 回复系统组件初始化 ====================
    def _initialize_reply_systems(self):
        """
        初始化回复系统组件

        按顺序初始化5个核心回复系统组件：
        1. MessageReplyManager - 统一回复管理器
        2. DynamicReplyGenerator - 动态回复生成器
        3. IntegratedReplySystem - 整合决策引擎
        4. TemplateVersionManager - 模板版本管理器
        5. ReplyMonitoringSystem - 监控分析系统

        特点：
        - 容错设计：单个组件初始化失败不影响其他组件
        - 日志记录：详细记录每个组件的初始化状态
        - 回退机制：失败时设置为None，使用回退逻辑

        注意：
        - 此方法在ConversationFlow的__init__中调用
        - 所有组件都是可选的，系统可以在部分组件缺失时正常运行
        """
        self.logger.info("🔧 [调试] _initialize_reply_systems 方法开始执行")
        try:
            # 优先复用已通过依赖注入传入的组件，避免重复初始化
            pre_injected_integrated = getattr(self, 'integrated_reply_system', None)
            pre_injected_reply_mgr = getattr(self, 'reply_manager', None)

            if pre_injected_integrated:
                self.logger.info("检测到已注入的 IntegratedReplySystem，优先复用，跳过新建")
                # 如果未显式注入 reply_manager，尝试从 integrated_reply_system 复用
                if pre_injected_reply_mgr is None and hasattr(pre_injected_integrated, 'reply_manager'):
                    self.reply_manager = pre_injected_integrated.reply_manager
                    self.logger.info("已从 IntegratedReplySystem 复用 MessageReplyManager")

            # 1. 消息回复管理器（仅在没有可复用实例时才创建）
            if getattr(self, 'reply_manager', None) is None:
                try:
                    from backend.agents.message_reply_manager import MessageReplyManager
                    self.reply_manager = MessageReplyManager(
                        llm_client=self.llm_client
                        # 不再需要指定config_path，使用统一配置
                    )
                    self.logger.info("MessageReplyManager 初始化成功（使用统一配置）")
                except Exception as e:
                    self.logger.warning(f"MessageReplyManager 初始化失败: {e}")
                    self.reply_manager = None
            else:
                self.logger.info("复用已有的 MessageReplyManager 实例，跳过新建")

            # 2. 动态回复生成器和工厂
            self.logger.info("🔧 [调试] 开始初始化 DynamicReplyFactory...")
            self.logger.info(f"🔧 [调试] self.llm_client 状态: {hasattr(self, 'llm_client') and self.llm_client is not None} (类型: {type(getattr(self, 'llm_client', None)).__name__ if hasattr(self, 'llm_client') and self.llm_client else 'None'})")
            self.logger.info(f"🔧 [调试] self.message_manager 状态: {hasattr(self, 'message_manager') and self.message_manager is not None} (类型: {type(getattr(self, 'message_manager', None)).__name__ if hasattr(self, 'message_manager') and self.message_manager else 'None'})")

            try:
                from backend.agents.dynamic_reply_generator import DynamicReplyGenerator, DynamicReplyFactory
                self.logger.info("🔧 [调试] 成功导入 DynamicReplyGenerator 和 DynamicReplyFactory")

                generator = DynamicReplyGenerator(
                    llm_client=self.llm_client,
                    message_manager=getattr(self, 'message_manager', None)
                )
                self.logger.info("🔧 [调试] DynamicReplyGenerator 创建成功")

                self.reply_factory = DynamicReplyFactory(generator)
                self.logger.info("✅ DynamicReplyFactory 初始化成功")
                self.logger.info(f"🔧 [组件调试] reply_factory.generate_optimized_question 方法存在: {hasattr(self.reply_factory, 'generate_optimized_question')}")
                self.logger.info(f"🔧 [调试] reply_factory 最终状态: {self.reply_factory is not None} (类型: {type(self.reply_factory).__name__})")
            except Exception as e:
                self.logger.warning(f"❌ DynamicReplyFactory 初始化失败: {e}")
                self.logger.exception("🔧 [调试] DynamicReplyFactory 初始化异常详情:")
                self.reply_factory = None

            # 3. 整合决策引擎（若已有注入则复用，否则创建并注入 reply_manager）
            if pre_injected_integrated:
                self.integrated_reply_system = pre_injected_integrated
                self.logger.info("复用已有的 IntegratedReplySystem 实例，跳过新建")
            else:
                try:
                    from backend.agents.integrated_reply_system import IntegratedReplySystem
                    self.integrated_reply_system = IntegratedReplySystem(
                        llm_client=self.llm_client,
                        reply_manager=self.reply_manager  # 注入已创建/复用的MessageReplyManager实例
                    )
                    self.logger.info("IntegratedReplySystem 初始化成功")
                    self.logger.info(f"🔧 [组件调试] integrated_reply_system.reply_factory 存在: {hasattr(self.integrated_reply_system, 'reply_factory') and self.integrated_reply_system.reply_factory is not None}")
                except Exception as e:
                    self.logger.warning(f"IntegratedReplySystem 初始化失败: {e}")
                    self.integrated_reply_system = None

            # 4. 模板版本管理器
            try:
                from backend.agents.template_version_manager import TemplateVersionManager
                self.version_manager = TemplateVersionManager()
                self.logger.info("TemplateVersionManager 初始化成功")
            except Exception as e:
                self.logger.warning(f"TemplateVersionManager 初始化失败: {e}")
                self.version_manager = None

            self.logger.info("新回复系统组件初始化完成")

            # 🔧 最终状态检查
            self.logger.info("🔧 [调试] 回复系统组件最终状态:")
            self.logger.info(f"  - reply_manager: {getattr(self, 'reply_manager', None) is not None}")
            self.logger.info(f"  - reply_factory: {getattr(self, 'reply_factory', None) is not None}")
            self.logger.info(f"  - integrated_reply_system: {getattr(self, 'integrated_reply_system', None) is not None}")
            self.logger.info(f"  - version_manager: {getattr(self, 'version_manager', None) is not None}")

        except Exception as e:
            self.logger.error(f"❌ 初始化回复系统组件失败: {e}", exc_info=True)
            # 设置为None，使用回退机制
            self.reply_manager = None
            self.reply_factory = None
            self.integrated_reply_system = None
            self.version_manager = None
            self.monitoring_system = None

            self.logger.info("🔧 [调试] 由于异常，所有回复系统组件已设置为None")

    # ==================== 异步组件初始化 ====================
    async def initialize_async_components(self):
        """
        异步初始化组件

        初始化需要数据库连接的异步组件：
        - TemplateVersionManager: 初始化模板版本管理数据库表
        - ReplyMonitoringSystem: 初始化监控系统数据库表

        调用时机：
        - 在ConversationFlow完全初始化后调用
        - 通常在应用启动时的异步初始化阶段

        错误处理：
        - 捕获所有异常，不会因为数据库问题导致系统崩溃
        - 详细记录初始化过程和错误信息

        返回：
        - 无返回值，通过日志记录初始化状态
        """
        try:
            if self.version_manager:
                await self.version_manager.initialize_database()
                self.logger.info("TemplateVersionManager 数据库初始化完成")
            
            
                
            self.logger.info("异步组件初始化完成")
            
        except Exception as e:
            self.logger.error(f"异步组件初始化失败: {e}", exc_info=True)

    # ==================== 消息处理 ====================
    async def _process_with_integrated_system(
        self,
        message: str,
        session_id: str,
        user_id: str,
        decision_result: Dict[str, Any],
        history: List,
        action_command: str
    ) -> str:
        """使用整合回复系统处理消息"""
        try:
            if not self.integrated_reply_system:
                return None
            
            from backend.agents.integrated_reply_system import DecisionContext
            
            context = DecisionContext(
                intent=decision_result.get("intent"),
                emotion=decision_result.get("emotion"),
                current_state=self.current_state.name,
                session_id=session_id,
                user_id=user_id,
                user_message=message,
                conversation_history=history,
                additional_context={
                    "current_domain": self.current_domain,
                    "current_category": self.current_category,
                    "problem_statement": self.problem_statement,
                    "action_command": action_command
                }
            )
            
            reply_result = await self.integrated_reply_system.process_decision_to_reply(
                context=context,
                conversation_flow_instance=self
            )
            
            if reply_result.success:
                self.logger.info(f"整合回复系统处理成功: {action_command}")
                return reply_result.content
            else:
                self.logger.warning(f"整合回复系统处理失败: {reply_result.content}")
                return None
                
        except Exception as e:
            self.logger.error(f"整合回复系统处理异常: {e}", exc_info=True)
            return None

    async def _process_with_fallback_system(
        self,
        message: str,
        session_id: str,
        decision_result: Dict[str, Any],
        history: List,
        action_command: str
    ) -> str:
        """回退到原有的消息处理逻辑"""
        try:
            self.logger.info(f"使用回退逻辑处理action: {action_command}")

            # 尝试通过方法名直接调用处理器
            handler = await self._get_handler_by_action(action_command)

            if handler:
                # 智能调用handler，只传入它需要的参数
                response_text = await self._call_handler_safely(
                    handler,
                    message=message,
                    session_id=session_id,
                    decision_result=decision_result,
                    history=history
                )
                return response_text
            else:
                # 如果找不到处理器，使用默认处理
                self.logger.warning(f"未找到action {action_command} 的处理器，使用默认处理")
                return await self._get_reply("unknown_action")

        except Exception as e:
            self.logger.error(f"回退系统处理失败: {e}", exc_info=True)
            return await self._get_reply("system_error")

    async def _get_handler_by_action(self, action_command: str):
        """根据action获取对应的处理器方法"""
        # action到方法名的映射（保持向后兼容）
        # 简化的映射表 - 只保留没有专门Handler处理的actions
        # 大部分actions现在由ActionExecutor和专门的Handler处理
        # 这个映射表只作为回退机制，处理一些特殊情况
        action_to_method = {
            # 注意：以下actions已经有专门的Handler处理，不需要在这里映射：
            # - ConversationHandler: reset_conversation, restart_conversation, finalize_and_reset, respond_with_greeting
            # - RequirementHandler: start_requirement_gathering, process_answer_and_ask_next, 等
            # - DocumentHandler: execute_document_modification
            # - GeneralRequestHandler: explain_capabilities, provide_self_introduction, 等

            # 如果有特殊的action需要直接调用ConversationFlow方法，可以在这里添加
            # 目前为空，因为所有已知的actions都有对应的Handler
        }

        method_name = action_to_method.get(action_command)
        if method_name and hasattr(self, method_name):
            return getattr(self, method_name)
        else:
            return None

    async def _call_handler_safely(self, handler, **kwargs):
        """
        安全地调用handler方法，只传入它需要的参数

        Args:
            handler: 要调用的handler方法
            **kwargs: 可能的参数

        Returns:
            handler的返回值
        """
        import inspect

        try:
            # 获取handler方法的签名
            sig = inspect.signature(handler)

            # 检查是否有**kwargs参数
            has_var_keyword = any(
                param.kind == inspect.Parameter.VAR_KEYWORD
                for param in sig.parameters.values()
            )

            # 过滤出handler实际需要的参数
            filtered_kwargs = {}
            for param_name in sig.parameters.keys():
                if param_name in kwargs:
                    filtered_kwargs[param_name] = kwargs[param_name]

            # 如果handler有**kwargs参数，传入所有剩余参数
            if has_var_keyword:
                for key, value in kwargs.items():
                    if key not in filtered_kwargs:
                        filtered_kwargs[key] = value

            # 调用handler
            return await handler(**filtered_kwargs)

        except Exception as e:
            self.logger.error(f"调用handler {handler.__name__} 失败: {e}", exc_info=True)
            # 如果智能调用失败，尝试无参数调用
            try:
                return await handler()
            except Exception as e2:
                self.logger.error(f"无参数调用handler {handler.__name__} 也失败: {e2}")
                raise e  # 抛出原始异常

    async def _get_reply(self, reply_key: str, context: Dict[str, Any] = None, **kwargs) -> str:
        """
        统一的回复获取方法 - 替换所有_get_*_message方法

        这是新回复系统的核心接口，统一管理所有类型的回复获取。
        替换了原有的18个独立的_get_*_message方法。

        参数：
        - reply_key: 回复键，标识要获取的回复类型
        - context: 上下文信息，用于动态回复生成
        - **kwargs: 额外参数，如session_id等

        处理流程：
        1. 优先使用MessageReplyManager获取回复
        2. 如果组件不可用，回退到硬编码回复
        3. 记录错误并提供默认回复

        支持的回复类型：
        - 静态回复：预定义的固定文本
        - 动态回复：基于上下文生成的个性化回复
        - 模板回复：支持变量替换的模板回复

        返回：
        - str: 生成的回复文本

        异常处理：
        - 捕获所有异常，确保总是返回有效回复
        - 失败时使用回退机制提供默认回复
        """
        try:
            if self.reply_manager:
                # 使用新的回复管理器
                return await self.reply_manager.get_reply(
                    reply_key=reply_key,
                    context=context or {},
                    session_id=kwargs.get("session_id"),
                    **kwargs
                )
            else:
                # 回退到硬编码回复
                return self._get_fallback_reply(reply_key, context)
                
        except Exception as e:
            self.logger.error(f"获取回复失败: {e}", exc_info=True)
            return self._get_fallback_reply(reply_key, context)

    def _get_config(self):
        """获取配置对象的统一方法"""
        if hasattr(self, 'unified_config_loader'):
            return self.unified_config_loader
        elif hasattr(self, 'get_unified_config'):
            return self.get_unified_config()
        else:
            from backend.config.unified_config_loader import get_unified_config
            return get_unified_config()

    def _get_fallback_reply(self, reply_key: str, context: Dict[str, Any] = None) -> str:
        """回退回复映射 - 从统一配置文件获取"""
        try:
            # 使用统一的配置获取方法
            config = self._get_config()

            # 直接获取模板
            template = config.get_message_template(reply_key, **(context or {}))

            # 如果成功获取到模板且不是错误消息，直接返回
            if template and not template.startswith("[模板缺失"):
                return template

            # 如果没有找到指定模板，尝试获取默认回退消息
            fallback_template = config.get_message_template("guidance.default_requirement_prompt")
            if fallback_template and not fallback_template.startswith("[模板缺失"):
                return fallback_template

            # 最终回退消息
            return config.get_message_template("fallback.general")

        except Exception as e:
            self.logger.warning(f"获取回退回复失败: {e}")
            # 最终回退到配置消息
            try:
                config = self._get_config()
                return config.get_message_template("fallback.general")
            except:
                return get_unified_config().get_config_value("message_templates.conversation_flow_reply_mixin.general_problem")
