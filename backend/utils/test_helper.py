#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试辅助工具

提供快速设置测试环境的方法，避免每次都要从头开始测试
"""

import asyncio
import logging
from typing import Dict, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)


class TestHelper:
    """测试辅助类"""
    
    def __init__(self, db_manager=None):
        self.db_manager = db_manager
        
    async def setup_test_session(self, 
                                session_id: str, 
                                user_id: str = "test_user",
                                domain_id: str = "software_development",
                                domain_name: str = "软件开发",
                                category_id: str = "ecommerce_platform", 
                                category_name: str = "电商平台") -> Dict[str, Any]:
        """
        快速设置测试会话，直接进入需求收集状态
        
        Args:
            session_id: 会话ID
            user_id: 用户ID
            domain_id: 领域ID
            domain_name: 领域名称
            category_id: 分类ID
            category_name: 分类名称
            
        Returns:
            Dict: 设置结果
        """
        try:
            logger.info(f"开始设置测试会话: {session_id}")
            
            # 1. 创建或更新会话记录
            await self._create_conversation_record(session_id, user_id, domain_id, category_id)
            
            # 2. 设置领域分类结果
            await self._set_domain_classification(session_id, user_id, domain_id, domain_name)
            await self._set_category_classification(session_id, user_id, category_id, category_name)
            
            # 3. 初始化关注点
            await self._initialize_focus_points(session_id, user_id, category_id)
            
            logger.info(f"测试会话设置完成: {session_id}")
            
            return {
                "success": True,
                "session_id": session_id,
                "user_id": user_id,
                "domain": {"id": domain_id, "name": domain_name},
                "category": {"id": category_id, "name": category_name},
                "state": "COLLECTING_INFO",
                "message": "测试会话已设置完成，可以直接进行需求收集测试"
            }
            
        except Exception as e:
            logger.error(f"设置测试会话失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "测试会话设置失败"
            }
    
    async def _create_conversation_record(self, session_id: str, user_id: str, domain_id: str, category_id: str):
        """创建会话记录"""
        try:
            # 检查会话是否已存在
            existing = await self.db_manager.get_record(
                "SELECT conversation_id FROM conversations WHERE conversation_id = ? AND user_id = ?",
                (session_id, user_id)
            )
            
            if existing:
                # 更新现有会话
                await self.db_manager.execute_update(
                    """UPDATE conversations 
                       SET domain_id = ?, category_id = ?, updated_at = ?
                       WHERE conversation_id = ? AND user_id = ?""",
                    (domain_id, category_id, datetime.now().isoformat(), session_id, user_id)
                )
                logger.info(f"更新现有会话: {session_id}")
            else:
                # 创建新会话
                await self.db_manager.execute_update(
                    """INSERT INTO conversations 
                       (conversation_id, user_id, domain_id, category_id, status, created_at, updated_at)
                       VALUES (?, ?, ?, ?, 'active', ?, ?)""",
                    (session_id, user_id, domain_id, category_id, 
                     datetime.now().isoformat(), datetime.now().isoformat())
                )
                logger.info(f"创建新会话: {session_id}")
                
        except Exception as e:
            logger.error(f"创建会话记录失败: {e}")
            raise
    
    async def _set_domain_classification(self, session_id: str, user_id: str, domain_id: str, domain_name: str):
        """设置领域分类结果"""
        try:
            # 删除现有的领域分类结果
            await self.db_manager.execute_update(
                "DELETE FROM domain_recognition_results WHERE conversation_id = ?",
                (session_id,)
            )
            
            # 插入新的领域分类结果
            await self.db_manager.execute_update(
                """INSERT INTO domain_recognition_results 
                   (conversation_id, domain_id, confidence, created_at)
                   VALUES (?, ?, 0.95, ?)""",
                (session_id, domain_id, datetime.now().isoformat())
            )
            
            logger.info(f"设置领域分类: {domain_id} ({domain_name})")
            
        except Exception as e:
            logger.error(f"设置领域分类失败: {e}")
            raise
    
    async def _set_category_classification(self, session_id: str, user_id: str, category_id: str, category_name: str):
        """设置分类结果"""
        try:
            # 删除现有的分类结果
            await self.db_manager.execute_update(
                "DELETE FROM category_recognition_results WHERE conversation_id = ?",
                (session_id,)
            )
            
            # 插入新的分类结果
            await self.db_manager.execute_update(
                """INSERT INTO category_recognition_results 
                   (conversation_id, category_id, confidence, created_at)
                   VALUES (?, ?, 0.95, ?)""",
                (session_id, category_id, datetime.now().isoformat())
            )
            
            logger.info(f"设置分类: {category_id} ({category_name})")
            
        except Exception as e:
            logger.error(f"设置分类失败: {e}")
            raise
    
    async def _initialize_focus_points(self, session_id: str, user_id: str, category_id: str):
        """初始化关注点"""
        try:
            # 这里可以根据category_id设置相应的关注点
            # 暂时跳过，因为关注点会在需求收集过程中自动初始化
            logger.info(f"关注点初始化完成: {category_id}")
            
        except Exception as e:
            logger.error(f"初始化关注点失败: {e}")
            raise
    
    async def reset_session(self, session_id: str, user_id: str = "test_user") -> Dict[str, Any]:
        """重置会话到初始状态"""
        try:
            logger.info(f"重置会话: {session_id}")
            
            # 删除相关数据
            tables_to_clean = [
                "domain_recognition_results",
                "category_recognition_results", 
                "focus_points",
                "documents",
                "messages"
            ]
            
            for table in tables_to_clean:
                await self.db_manager.execute_update(
                    f"DELETE FROM {table} WHERE conversation_id = ?",
                    (session_id,)
                )
            
            # 重置会话状态
            await self.db_manager.execute_update(
                """UPDATE conversations 
                   SET domain_id = NULL, category_id = NULL, status = 'active', updated_at = ?
                   WHERE conversation_id = ? AND user_id = ?""",
                (datetime.now().isoformat(), session_id, user_id)
            )
            
            logger.info(f"会话重置完成: {session_id}")
            
            return {
                "success": True,
                "message": "会话已重置到初始状态"
            }
            
        except Exception as e:
            logger.error(f"重置会话失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "会话重置失败"
            }


# 便捷函数
async def quick_setup_test_session(session_id: str, 
                                  domain: str = "software_development",
                                  category: str = "ecommerce_platform"):
    """
    快速设置测试会话的便捷函数
    
    Args:
        session_id: 会话ID
        domain: 领域类型 (software_development, marketing, design)
        category: 分类类型 (ecommerce_platform, mobile_app, website)
    """
    from backend.agents.factory import agent_factory
    
    db_manager = agent_factory.get_database_manager()
    helper = TestHelper(db_manager)
    
    domain_mapping = {
        "software_development": {"id": "software_development", "name": "软件开发"},
        "marketing": {"id": "marketing", "name": "营销推广"},
        "design": {"id": "design", "name": "设计服务"}
    }
    
    category_mapping = {
        "ecommerce_platform": {"id": "ecommerce_platform", "name": "电商平台"},
        "mobile_app": {"id": "mobile_app", "name": "移动应用"},
        "website": {"id": "website", "name": "网站开发"}
    }
    
    domain_info = domain_mapping.get(domain, domain_mapping["software_development"])
    category_info = category_mapping.get(category, category_mapping["ecommerce_platform"])
    
    result = await helper.setup_test_session(
        session_id=session_id,
        domain_id=domain_info["id"],
        domain_name=domain_info["name"],
        category_id=category_info["id"],
        category_name=category_info["name"]
    )
    
    return result


if __name__ == "__main__":
    # 测试用例
    async def test():
        result = await quick_setup_test_session("test_session_123")
        print(f"设置结果: {result}")
    
    asyncio.run(test())
