#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的统一日志配置模块

此模块提供精简的日志配置功能，包括：
1. 三种核心日志：app.log、error.log、session.log
2. 异步文件日志处理（QueueHandler）
3. 同步控制台日志
4. 敏感数据脱敏
5. 默认配置优化

使用方法：
    from backend.utils.logging_config import get_logger, configure_logging

    # 默认配置（仅控制台日志）
    configure_logging()

    # 启用文件日志
    configure_logging(enable_file=True)

    # 自定义配置
    configure_logging(
        log_level=logging.DEBUG,
        max_bytes=10*1024*1024,
        backup_count=5
    )
"""

import logging
import logging.handlers
import json
import atexit
import asyncio
from pathlib import Path
from typing import List, Tuple
import threading
import functools
import time
from queue import Queue
import re
import sys

# 内联敏感数据脱敏类（原来在sensitive_data_masker.py中）
class SensitiveDataMasker:
    """敏感数据检测和脱敏工具类"""

    def __init__(self, patterns: List[Tuple[str, str]] = None):
        """初始化敏感数据检测和脱敏工具

        Args:
            patterns: 敏感数据模式列表，每项为 (pattern, replacement) 元组
        """
        self.patterns = patterns or []
        self._compiled_patterns = []
        self._compile_patterns()

    def _compile_patterns(self):
        """编译正则表达式模式"""
        self._compiled_patterns = [
            (re.compile(pattern), replacement)
            for pattern, replacement in self.patterns
        ]

    def mask_text(self, text: str) -> str:
        """对文本进行脱敏

        Args:
            text: 需要脱敏的文本

        Returns:
            脱敏后的文本
        """
        if not text:
            return text

        masked_text = text
        for pattern, replacement in self._compiled_patterns:
            masked_text = pattern.sub(replacement, masked_text)
        return masked_text

# 精简的敏感数据模式
_SENSITIVE_PATTERNS = [
    # API密钥
    (r'(?i)(api[_-]?key|access[_-]?key|secret[_-]?key)["\']?\s*[:=]\s*["\']?([^"\'}\s]{6})([^"\'}\s]+?)([^"\'}\s]{4})\b', r'\1=\2****\4'),
    # Bearer Token
    (r'(?i)(Authorization["\']?\s*:\s*["\']?Bearer\s+)[a-zA-Z0-9\-._~+/=]+', r'\1****'),
    # 通用密钥格式
    (r'(?i)(sk-)[a-zA-Z0-9\-._~+/=]{20,}', r'\1****'),
    # 密码字段
    (r'(?i)(password|passwd)["\']?\s*[:=]\s*["\']?([^"\'}\s]+)', r'\1=***'),
]

# 全局日志配置
_LOG_CONFIG = {
    "initialized": False,
    "log_dir": Path(__file__).parent.parent.parent / "logs",  # 项目根目录/logs
    "performance_log_dir": Path(__file__).parent.parent.parent / "logs" / "performance",  # 项目根目录/logs/performance
    "log_level": logging.DEBUG,  # 开发环境使用DEBUG级别
    "log_format": "%(asctime)s %(name)s %(levelname)s %(message)s",
    "session_format": "%(asctime)s [%(session_id)s] [%(stage)s] %(name)s %(levelname)s %(message)s",
    "max_bytes": 10 * 1024 * 1024,  # 10MB（按需求调整）
    "backup_count": 7,  # 保留7份备份文件
    "handlers": {},
    "enable_masking": True,
    "sensitive_patterns": _SENSITIVE_PATTERNS,
    "sensitive_data_masker": None,
    "queue_handler": None,
    "queue_listener": None,
    "deduplication_interval": 2.0,  # 去重时间窗口（秒）
    "json_format_enabled": True,  # 启用JSON格式
    "template_logging_enabled": False  # 启用模版内容日志输出
}

# 线程锁
_lock = threading.Lock()

class SessionLogFilter(logging.Filter):
    """会话日志过滤器，只记录与会话相关的日志，避免与app.log重复"""

    def __init__(self, session_id: str = None, stage: str = None):
        super().__init__()
        self.session_id = session_id or "system"
        self.stage = stage or "default"

    def filter(self, record):
        # 添加会话ID和阶段信息
        record.session_id = getattr(record, "session_id", self.session_id)
        record.stage = getattr(record, "stage", self.stage)

        # 标记这条日志已被session.log处理，避免app.log重复记录
        if self._is_session_related(record):
            record.logged_to_session = True
            return True

        return False

    def _is_session_related(self, record):
        """判断是否为会话相关日志"""
        # 1. 有明确session_id的日志（非系统日志）
        if hasattr(record, 'session_id') and record.session_id != "system":
            return True

        # 2. 有会话相关type的日志
        session_types = {
            "session_start", "session_end", "user_input", "ai_response",
            "state_change", "business_node", "llm_call", "data_operation",
            "intent_recognition", "domain_classification", "information_extraction",
            "session_error"
        }
        if hasattr(record, 'type') and record.type in session_types:
            return True

        # 3. 包含会话关键词的消息
        message = record.getMessage()
        session_keywords = [
            "👤 [用户输入]", "🤖 [AI回复]", "[业务节点]", "[Action执行]",
            "会话", "用户输入", "AI回复", "状态变更", "意图识别", "领域分类", "信息提取"
        ]
        if any(keyword in message for keyword in session_keywords):
            return True

        # 4. 来自会话相关模块的日志
        session_modules = ["conversation_flow", "chat", "session", "ActionExecutor"]
        if any(module in record.name for module in session_modules):
            return True

        return False


class AppLogFilter(logging.Filter):
    """应用日志过滤器，避免记录已被session.log记录的日志"""

    def filter(self, record):
        # 如果这条日志已经被session.log记录，则不在app.log中重复记录
        if hasattr(record, 'logged_to_session') and record.logged_to_session:
            return False

        return True

class TokenUsageFilter(logging.Filter):
    """过滤Token使用情况的日志"""
    def filter(self, record):
        return "Token使用情况" not in record.getMessage()

class IntelligentAppLogFilter(logging.Filter):
    """智能应用日志过滤器 - 减少冗余信息，突出重要内容"""

    def __init__(self):
        super().__init__()
        # 定义需要过滤的低价值日志模式
        self.noise_patterns = [
            "Ignoring ImportError",  # autogen导入错误
            "Trying paths:",         # docker配置查找
            "Found file at path:",   # docker配置发现
            "Found 'credsStore'",    # docker认证
            "查询缓存设置",            # 数据库缓存日志
            "参数: (",               # SQL参数（过于详细）
            "性能监控",               # 性能监控常规信息
            "配置加载",               # 配置加载常规信息
            "初始化",                 # 一般初始化信息
        ]

        # 定义重要日志模式（即使是DEBUG级别也要保留）
        self.important_patterns = [
            "用户输入",
            "AI回复",
            "LLM调用",
            "业务节点",
            "状态转换",
            "错误",
            "失败",
            "异常",
            "初始化完成",
            "服务启动"
        ]
        
        # 高频模块列表 - 在高并发时可能需要降低日志级别
        self.high_frequency_modules = [
            'backend.api.main',
            'backend.agents.conversation_flow',
            'backend.agents.llm_interface',
            'uvicorn.access'
        ]

    def filter(self, record):
        message = record.getMessage()

        # 保留所有ERROR和WARNING级别
        if record.levelno >= logging.WARNING:
            return True

        # 保留重要的业务日志
        if any(pattern in message for pattern in self.important_patterns):
            return True

        # 过滤掉噪音日志
        if any(pattern in message for pattern in self.noise_patterns):
            return False

        # 在高负载情况下，降低高频模块的日志级别
        if hasattr(record, 'high_concurrency') and record.high_concurrency:
            if any(module in record.name for module in self.high_frequency_modules):
                # 在高并发时只记录INFO级别以上的日志
                if record.levelno < logging.INFO:
                    return False

        # 过滤掉过于频繁的DEBUG日志
        if record.levelno == logging.DEBUG:
            # 只保留业务相关的DEBUG日志
            business_modules = ['backend.api', 'backend.agents', 'autogen_agent']
            if not any(module in record.name for module in business_modules):
                return False

        return True

class EnhancedDeduplicationLogFilter(logging.Filter):
    """增强的去重过滤器：在指定时间窗口内相同消息只记录一次，并统计重复次数"""
    def __init__(self, interval: float = 2.0, max_cache_size: int = 1000):
        super().__init__()
        self.interval = interval
        self.max_cache_size = max_cache_size
        self.last_occurrence = {}
        self.repeat_counts = {}
        self.last_cleanup = time.time()
        self.cleanup_interval = 300  # 5分钟清理一次缓存

    def filter(self, record: logging.LogRecord) -> bool:
        now = time.time()

        # 定期清理缓存
        if now - self.last_cleanup > self.cleanup_interval:
            self._cleanup_cache(now)
            self.last_cleanup = now

        # 生成消息键（包含日志级别和模块名以提高精确度）
        msg_key = f"{record.levelname}:{record.name}:{record.getMessage()}"

        # 检查是否需要去重
        last_time = self.last_occurrence.get(msg_key, 0)
        if now - last_time < self.interval:
            # 增加重复计数
            self.repeat_counts[msg_key] = self.repeat_counts.get(msg_key, 0) + 1
            return False

        # 如果有重复记录，添加重复次数信息
        if msg_key in self.repeat_counts and self.repeat_counts[msg_key] > 0:
            original_msg = record.getMessage()
            repeat_count = self.repeat_counts[msg_key]
            record.msg = f"{original_msg} [重复 {repeat_count} 次]"
            record.args = ()
            self.repeat_counts[msg_key] = 0

        # 更新最后出现时间
        self.last_occurrence[msg_key] = now
        return True

    def _cleanup_cache(self, current_time: float):
        """清理过期的缓存条目"""
        expired_keys = [
            key for key, last_time in self.last_occurrence.items()
            if current_time - last_time > self.interval * 10  # 保留10倍时间窗口的记录
        ]

        for key in expired_keys:
            self.last_occurrence.pop(key, None)
            self.repeat_counts.pop(key, None)

        # 如果缓存仍然太大，删除最旧的条目
        if len(self.last_occurrence) > self.max_cache_size:
            sorted_items = sorted(self.last_occurrence.items(), key=lambda x: x[1])
            items_to_remove = len(sorted_items) - self.max_cache_size + 100  # 多删除一些以减少频繁清理

            for key, _ in sorted_items[:items_to_remove]:
                self.last_occurrence.pop(key, None)
                self.repeat_counts.pop(key, None)

# 保持向后兼容
DeduplicationLogFilter = EnhancedDeduplicationLogFilter

class MaskingFormatter(logging.Formatter):
    """带敏感数据脱敏的格式化器"""

    @staticmethod
    def escape_markdown_chars(text: str) -> str:
        """转义Markdown特殊字符，防止在前端显示异常"""
        # 检查是否需要转义（只对AI回复进行转义）
        if '[AI回复]' not in text:
            return text

        # 提取AI回复的实际内容部分
        ai_reply_start = text.find('[AI回复]') + len('[AI回复]')
        prefix = text[:ai_reply_start]
        content = text[ai_reply_start:]

        # 只转义最容易引起问题的字符
        critical_chars = {
            '~': '\\~',  # 删除线 - 最常见的问题
            '*': '\\*',  # 粗体/斜体
            '_': '\\_',  # 粗体/斜体
            '`': '\\`',  # 代码块
        }

        # 转义关键字符
        for char, escaped in critical_chars.items():
            content = content.replace(char, escaped)

        return prefix + content

    def format(self, record):
        formatted = super().format(record)
        if _LOG_CONFIG["enable_masking"] and _LOG_CONFIG["sensitive_data_masker"]:
            formatted = _LOG_CONFIG["sensitive_data_masker"].mask_text(formatted)

        # 转义Markdown特殊字符
        formatted = self.escape_markdown_chars(formatted)
        return formatted

class ColoredFormatter(MaskingFormatter):
    """带颜色输出的格式化器"""

    COLORS = {
        'DEBUG': '\033[90m',  # 灰色
        'INFO': '\033[36m',   # 青色
        'WARNING': '\033[33m',# 黄色
        'ERROR': '\033[31m',  # 红色
        'CRITICAL': '\033[41m\033[97m', # 红底白字
        'RESET': '\033[0m',   # 重置颜色
        'NAME': '\033[94m',   # 蓝色
        'FILENAME': '\033[95m', # 紫色
        'LINENO': '\033[90m', # 灰色

        # 自定义颜色
        'USER_INPUT': '\033[33m',    # 黄色
        'INPUT_MESSAGE': '\033[38;2;16;185;129m',   # 蓝色
        'OUTPUT_CONTENT': '\033[35m', # 紫色

        # 流程节点颜色
        'INTENT_RECOGNITION': '\033[38;2;255;165;0m',    # 橙色 - 意图识别
        'DOMAIN_CLASSIFICATION': '\033[38;2;50;205;50m', # 绿色 - 领域分类
        'CATEGORY_CLASSIFICATION': '\033[38;2;138;43;226m', # 紫色 - 类别分类
        'INFORMATION_EXTRACTION': '\033[38;2;255;20;147m', # 深粉色 - 信息提取
        'BUSINESS_NODE': '\033[38;2;30;144;255m',        # 蓝色 - 业务节点
        'STATE_TRANSITION': '\033[38;2;255;215;0m',      # 金色 - 状态切换
        'LLM_CALL': '\033[38;2;128;128;128m',           # 灰色 - LLM调用
    }

    def format(self, record):
        original_message = super().format(record) # 获取原始未着色的消息
        
        parts = []
        remaining_message = original_message

        # 匹配时间戳
        match_time = re.match(r"^(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3})", remaining_message)
        if match_time:
            parts.append(self.COLORS['DEBUG'] + match_time.group(1) + self.COLORS['RESET'])
            remaining_message = remaining_message[len(match_time.group(1)):].lstrip()

        # 匹配日志名称 (backend.agents.conversation_flow 或 ActionExecutor)
        # 修复：匹配到日志级别前的所有内容，而不是第一个大写字母前
        match_name = re.match(r"^(\S+)(?=\s+(?:DEBUG|INFO|WARNING|ERROR|CRITICAL))", remaining_message)
        if match_name:
            name_part = match_name.group(1)
            if '.' in name_part:
                prefix, suffix = name_part.rsplit('.', 1)
                parts.append(prefix + '.' + self.COLORS['NAME'] + suffix + self.COLORS['RESET'])
            else:
                parts.append(self.COLORS['NAME'] + name_part + self.COLORS['RESET'])
            remaining_message = remaining_message[len(match_name.group(1)):].lstrip()

        # 匹配日志级别
        match_level = re.match(r"^([A-Z]+)", remaining_message)
        if match_level:
            level_part = match_level.group(1)
            parts.append(self.COLORS.get(level_part, '') + level_part + self.COLORS['RESET'])
            remaining_message = remaining_message[len(match_level.group(1)):].lstrip()

        # 剩余的消息部分，现在应用用户自定义的内容着色
        final_message_content = remaining_message

        # 流程节点颜色匹配（优先级最高）
        if any(pattern in final_message_content for pattern in ["开始识别意图", "意图识别结果", "[意图识别]"]):
            final_message_content = self.COLORS['INTENT_RECOGNITION'] + final_message_content + self.COLORS['RESET']
        elif any(pattern in final_message_content for pattern in ["开始分类文本", "领域分类结果", "[领域分类]", "Domain classification"]):
            final_message_content = self.COLORS['DOMAIN_CLASSIFICATION'] + final_message_content + self.COLORS['RESET']
        elif any(pattern in final_message_content for pattern in ["开始类别分类", "类别分类结果", "[类别分类]", "Category classification"]):
            final_message_content = self.COLORS['CATEGORY_CLASSIFICATION'] + final_message_content + self.COLORS['RESET']
        elif any(pattern in final_message_content for pattern in ["开始提取信息", "信息提取", "[信息提取]", "Information extraction"]):
            final_message_content = self.COLORS['INFORMATION_EXTRACTION'] + final_message_content + self.COLORS['RESET']
        elif any(pattern in final_message_content for pattern in ["[业务节点]", "Business node"]):
            final_message_content = self.COLORS['BUSINESS_NODE'] + final_message_content + self.COLORS['RESET']
        elif any(pattern in final_message_content for pattern in ["[状态切换]", "State transition"]):
            final_message_content = self.COLORS['STATE_TRANSITION'] + final_message_content + self.COLORS['RESET']
        elif any(pattern in final_message_content for pattern in ["LLM响应", "LLM调用", "call_llm"]):
            final_message_content = self.COLORS['LLM_CALL'] + final_message_content + self.COLORS['RESET']
        # 用户交互颜色匹配
        elif any(pattern in final_message_content for pattern in ["用户输入:", "[用户输入]"]):
            final_message_content = self.COLORS['USER_INPUT'] + final_message_content + self.COLORS['RESET']
        elif any(pattern in final_message_content for pattern in ["输入消息:", "[输入消息]"]):
            final_message_content = self.COLORS['INPUT_MESSAGE'] + final_message_content + self.COLORS['RESET']
        elif any(pattern in final_message_content for pattern in ["输出内容:", "[输出内容]"]):
            final_message_content = self.COLORS['OUTPUT_CONTENT'] + final_message_content + self.COLORS['RESET']
        else:
            # 如果没有匹配到特定内容，则保持原始颜色（因为级别颜色已经在前面处理了）
            pass

        parts.append(final_message_content)

        return " ".join(parts)

class EnhancedJsonFormatter(logging.Formatter):
    """增强的JSON格式化器，支持结构化日志和业务上下文"""
    def __init__(self, fmt_dict: dict = None):
        super().__init__()
        self.fmt_dict = fmt_dict or {}

        # 定义具体功能模块映射
        self.specific_modules = {
            # API层
            'backend.api.main': 'api_main',
            'backend.api.middleware': 'api_middleware',

            # 智能代理具体功能
            'backend.agents.intent_recognition': 'intent_recognition',
            'backend.agents.conversation_flow': 'conversation_flow',
            'backend.agents.decision_engine': 'decision_engine',
            'backend.agents.message_handler': 'message_handler',

            # LLM客户端
            'backend.agents.llm_service.DoubaoClient': 'doubao_client',
            'backend.agents.llm_service.OpenAIClient': 'openai_client',
            'backend.agents.llm_service.GeminiClient': 'gemini_client',

            # 数据库
            'backend.data.db.database_manager': 'database_manager',
            'backend.data.db.conversation_manager': 'conversation_manager',

            # 系统工具
            'backend.utils.prompt_loader': 'prompt_loader',
            'backend.utils.logging_config': 'logging_system',
            'backend.utils.performance': 'performance_monitor',

            # AutoGen
            'autogen_agent.conversation_flow': 'autogen_flow',
            'autogen_agent.group_chat': 'autogen_group_chat'
        }

        # 通用分类（作为后备）
        self.fallback_categories = {
            'api': ['backend.api'],
            'agent': ['backend.agents'],
            'database': ['backend.data', 'backend.utils.db'],
            'external': ['autogen', 'docker', 'requests', 'httpx'],
            'system': ['backend.utils', 'backend.config']
        }

        # 定义重要性级别
        self.importance_keywords = {
            'critical': ['error', 'failed', 'exception', 'timeout', 'crash'],
            'high': ['warning', '警告', '失败', '错误', 'llm调用', '用户输入'],
            'medium': ['info', '初始化', '完成', '成功', '开始'],
            'low': ['debug', 'ignoring', 'trying', '尝试']
        }

    def format(self, record: logging.LogRecord) -> str:
        timestamp = self.formatTime(record, self.datefmt)
        level = record.levelname
        module = record.name
        message = record.getMessage()

        # 敏感数据脱敏
        if _LOG_CONFIG["enable_masking"] and _LOG_CONFIG["sensitive_data_masker"]:
            message = _LOG_CONFIG["sensitive_data_masker"].mask_text(message)

        # 基础日志数据
        log_data = {
            "timestamp": timestamp,
            "level": level,
            "logger": module,
            "message": message,
            "thread": record.thread,
            "process": record.process
        }

        # 添加模块分类
        log_data["module_category"] = self._get_module_category(module)

        # 添加重要性级别
        log_data["importance"] = self._get_importance_level(message, level)

        # 添加业务标识
        if self._is_business_log(record):
            log_data["is_business"] = True

        # 添加会话信息
        if hasattr(record, 'session_id'):
            log_data["session_id"] = record.session_id
        if hasattr(record, 'stage'):
            log_data["stage"] = record.stage

        # 添加业务上下文信息
        if hasattr(record, 'type'):
            log_data["type"] = record.type
        if hasattr(record, 'user_id'):
            log_data["user_id"] = record.user_id
        if hasattr(record, 'request_id'):
            log_data["request_id"] = record.request_id
        if hasattr(record, 'model'):
            log_data["model"] = record.model
        if hasattr(record, 'duration'):
            log_data["duration"] = record.duration
        if hasattr(record, 'status'):
            log_data["status"] = record.status

        # 添加异常信息
        if record.exc_info:
            log_data["exception"] = {
                "type": record.exc_info[0].__name__ if record.exc_info[0] else None,
                "message": str(record.exc_info[1]) if record.exc_info[1] else None,
                "traceback": self.formatException(record.exc_info) if record.exc_info else None
            }

        # 添加文件位置信息（DEBUG级别时）
        if level == "DEBUG":
            log_data["location"] = {
                "filename": record.filename,
                "lineno": record.lineno,
                "funcName": record.funcName
            }

        return json.dumps(log_data, ensure_ascii=False, separators=(',', ':'))

    def _get_module_category(self, module_name: str) -> str:
        """根据模块名称获取具体功能分类"""
        # 首先尝试精确匹配具体模块
        if module_name in self.specific_modules:
            return self.specific_modules[module_name]

        # 尝试部分匹配具体模块
        for specific_module, category in self.specific_modules.items():
            if specific_module in module_name:
                return category

        # 后备：使用通用分类
        for category, patterns in self.fallback_categories.items():
            for pattern in patterns:
                if pattern in module_name:
                    return category

        # 最后返回模块名的最后一部分
        return module_name.split('.')[-1] if '.' in module_name else module_name

    def _get_importance_level(self, message: str, level: str) -> str:
        """根据消息内容和级别判断重要性"""
        message_lower = message.lower()

        # 级别优先
        if level in ['ERROR', 'CRITICAL']:
            return 'critical'
        elif level == 'WARNING':
            return 'high'

        # 关键词判断
        for importance, keywords in self.importance_keywords.items():
            if any(keyword in message_lower for keyword in keywords):
                return importance

        return 'medium'

    def _is_business_log(self, record) -> bool:
        """判断是否为业务日志"""
        business_indicators = [
            hasattr(record, 'session_id'),
            hasattr(record, 'type'),
            '用户' in record.getMessage(),
            'LLM' in record.getMessage(),
            '业务' in record.getMessage()
        ]
        return any(business_indicators)

class ErrorJsonFormatter(EnhancedJsonFormatter):
    """专门用于错误日志的JSON格式化器，提供更详细的错误信息"""

    def format(self, record: logging.LogRecord) -> str:
        timestamp = self.formatTime(record, self.datefmt)
        level = record.levelname
        module = record.name
        message = record.getMessage()

        # 敏感数据脱敏
        if _LOG_CONFIG["enable_masking"] and _LOG_CONFIG["sensitive_data_masker"]:
            message = _LOG_CONFIG["sensitive_data_masker"].mask_text(message)

        # 错误日志基础数据
        log_data = {
            "timestamp": timestamp,
            "level": level,
            "logger": module,
            "message": message,
            "thread": record.thread,
            "process": record.process,
            "severity": self._get_severity(level)
        }

        # 添加会话和用户信息
        if hasattr(record, 'session_id'):
            log_data["session_id"] = record.session_id
        if hasattr(record, 'user_id'):
            log_data["user_id"] = record.user_id
        if hasattr(record, 'stage'):
            log_data["stage"] = record.stage
        if hasattr(record, 'request_id'):
            log_data["request_id"] = record.request_id

        # 添加错误分类信息
        if hasattr(record, 'error_category'):
            log_data["error_category"] = record.error_category
        if hasattr(record, 'error_code'):
            log_data["error_code"] = record.error_code
        if hasattr(record, 'component'):
            log_data["component"] = record.component

        # 添加操作上下文
        if hasattr(record, 'operation'):
            log_data["operation"] = record.operation
        if hasattr(record, 'external_service'):
            log_data["external_service"] = record.external_service
        if hasattr(record, 'database_table'):
            log_data["database_table"] = record.database_table

        # 详细的异常信息
        if record.exc_info:
            exception_data = {
                "type": record.exc_info[0].__name__ if record.exc_info[0] else None,
                "message": str(record.exc_info[1]) if record.exc_info[1] else None,
                "traceback": self.formatException(record.exc_info) if record.exc_info else None,
                "traceback_lines": self._extract_traceback_lines(record.exc_info) if record.exc_info else []
            }
            log_data["exception"] = exception_data

        # 添加文件位置信息（错误日志总是包含）
        log_data["location"] = {
            "filename": record.filename,
            "lineno": record.lineno,
            "funcName": record.funcName,
            "pathname": record.pathname
        }

        # 添加环境信息
        log_data["environment"] = {
            "python_version": f"{record.created}",  # 使用创建时间作为标识
            "module_path": record.module if hasattr(record, 'module') else module
        }

        return json.dumps(log_data, ensure_ascii=False, separators=(',', ':'))

    def _get_severity(self, level: str) -> str:
        """获取错误严重程度"""
        severity_map = {
            "ERROR": "high",
            "CRITICAL": "critical",
            "FATAL": "critical"
        }
        return severity_map.get(level, "medium")

    def _extract_traceback_lines(self, exc_info) -> list:
        """提取traceback的关键行"""
        if not exc_info:
            return []

        import traceback
        tb_lines = traceback.format_exception(*exc_info)

        # 提取关键信息
        key_lines = []
        for line in tb_lines:
            line = line.strip()
            if line and not line.startswith('Traceback'):
                key_lines.append(line)

        return key_lines[-5:]  # 只保留最后5行关键信息

class SessionJsonFormatter(EnhancedJsonFormatter):
    """专门用于会话日志的JSON格式化器，优化会话相关信息的记录"""

    def format(self, record: logging.LogRecord) -> str:
        timestamp = self.formatTime(record, self.datefmt)
        level = record.levelname
        module = record.name
        message = record.getMessage()

        # 敏感数据脱敏
        if _LOG_CONFIG["enable_masking"] and _LOG_CONFIG["sensitive_data_masker"]:
            message = _LOG_CONFIG["sensitive_data_masker"].mask_text(message)

        # 会话日志基础数据
        log_data = {
            "timestamp": timestamp,
            "level": level,
            "logger": module,
            "message": message,
            "thread": record.thread,
            "process": record.process
        }

        # 会话信息（必须包含）
        log_data["session_id"] = getattr(record, 'session_id', 'unknown')
        log_data["stage"] = getattr(record, 'stage', 'default')

        # 用户信息
        if hasattr(record, 'user_id'):
            log_data["user_id"] = record.user_id

        # 业务流程追踪信息
        if hasattr(record, 'flow_id'):
            log_data["flow_id"] = record.flow_id
        if hasattr(record, 'step_sequence'):
            log_data["step_sequence"] = record.step_sequence
        if hasattr(record, 'total_steps'):
            log_data["total_steps"] = record.total_steps

        # 会话相关的业务上下文
        if hasattr(record, 'type'):
            log_data["type"] = record.type
        if hasattr(record, 'input_type'):
            log_data["input_type"] = record.input_type
        if hasattr(record, 'input_length'):
            log_data["input_length"] = record.input_length
        if hasattr(record, 'response_length'):
            log_data["response_length"] = record.response_length
        if hasattr(record, 'model'):
            log_data["model"] = record.model
        if hasattr(record, 'scenario'):
            log_data["scenario"] = record.scenario
        if hasattr(record, 'duration'):
            log_data["duration"] = record.duration
        if hasattr(record, 'generation_duration'):
            log_data["generation_duration"] = record.generation_duration

        # 业务质量指标
        if hasattr(record, 'completeness_score'):
            log_data["completeness_score"] = record.completeness_score
        if hasattr(record, 'confidence_score'):
            log_data["confidence_score"] = record.confidence_score
        if hasattr(record, 'user_satisfaction_prediction'):
            log_data["user_satisfaction_prediction"] = record.user_satisfaction_prediction
        if hasattr(record, 'document_quality_score'):
            log_data["document_quality_score"] = record.document_quality_score

        # 性能和用户体验指标
        if hasattr(record, 'user_wait_time'):
            log_data["user_wait_time"] = record.user_wait_time
        if hasattr(record, 'processing_efficiency'):
            log_data["processing_efficiency"] = record.processing_efficiency
        if hasattr(record, 'response_relevance'):
            log_data["response_relevance"] = record.response_relevance

        # 错误恢复和重试信息
        if hasattr(record, 'error_recovery_action'):
            log_data["error_recovery_action"] = record.error_recovery_action
        if hasattr(record, 'retry_count'):
            log_data["retry_count"] = record.retry_count
        if hasattr(record, 'fallback_used'):
            log_data["fallback_used"] = record.fallback_used

        # 状态变更信息
        if hasattr(record, 'from_state'):
            log_data["from_state"] = record.from_state
        if hasattr(record, 'to_state'):
            log_data["to_state"] = record.to_state
        if hasattr(record, 'trigger'):
            log_data["trigger"] = record.trigger

        # 业务节点信息
        if hasattr(record, 'node_name'):
            log_data["node_name"] = record.node_name
        if hasattr(record, 'node_type'):
            log_data["node_type"] = record.node_type
        if hasattr(record, 'result'):
            log_data["result"] = record.result

        # LLM调用信息
        if hasattr(record, 'prompt_length'):
            log_data["prompt_length"] = record.prompt_length
        if hasattr(record, 'token_usage'):
            log_data["token_usage"] = record.token_usage

        # 数据操作信息
        if hasattr(record, 'operation'):
            log_data["operation"] = record.operation
        if hasattr(record, 'table'):
            log_data["table"] = record.table
        if hasattr(record, 'affected_rows'):
            log_data["affected_rows"] = record.affected_rows

        # 意图识别信息
        if hasattr(record, 'intent'):
            log_data["intent"] = record.intent
        if hasattr(record, 'confidence'):
            log_data["confidence"] = record.confidence
        if hasattr(record, 'entities'):
            log_data["entities"] = record.entities

        # 领域分类信息
        if hasattr(record, 'domain_id'):
            log_data["domain_id"] = record.domain_id
        if hasattr(record, 'domain_name'):
            log_data["domain_name"] = record.domain_name

        # 信息提取信息
        if hasattr(record, 'extracted_count'):
            log_data["extracted_count"] = record.extracted_count
        if hasattr(record, 'total_points'):
            log_data["total_points"] = record.total_points
        if hasattr(record, 'extraction_result'):
            log_data["extraction_result"] = record.extraction_result

        # 会话生命周期信息
        if hasattr(record, 'start_time'):
            log_data["start_time"] = record.start_time
        if hasattr(record, 'end_reason'):
            log_data["end_reason"] = record.end_reason
        if hasattr(record, 'total_duration'):
            log_data["total_duration"] = record.total_duration
        if hasattr(record, 'user_agent'):
            log_data["user_agent"] = record.user_agent
        if hasattr(record, 'ip_address'):
            log_data["ip_address"] = record.ip_address

        # 错误信息（如果有）
        if hasattr(record, 'error_type'):
            log_data["error_type"] = record.error_type

        return json.dumps(log_data, ensure_ascii=False, separators=(',', ':'))

# 保持向后兼容
JsonFormatter = EnhancedJsonFormatter

def _cleanup_logging():
    """程序退出时清理日志资源"""
    global _LOG_CONFIG
    if _LOG_CONFIG.get("queue_listener"):
        _LOG_CONFIG["queue_listener"].stop()
        _LOG_CONFIG["queue_listener"] = None

def configure_logging(
    log_dir: str = None,
    log_level: int = logging.DEBUG,  # 开发环境默认DEBUG
    log_format: str = None,
    max_bytes: int = None,
    backup_count: int = None,
    enable_console: bool = True,
    enable_file: bool = False,  # 默认关闭文件日志
    enable_masking: bool = True,
    sensitive_patterns: List[Tuple[str, str]] = None,
    deduplication_interval: float = None,
    json_format_enabled: bool = True,  # 默认启用JSON格式
    template_logging_enabled: bool = False,  # 默认启用模版内容日志输出
    high_concurrency_mode: bool = False  # 新增高并发模式支持
) -> None:
    """配置优化的日志系统

    Args:
        log_dir: 日志目录路径，默认为 logs
        log_level: 日志级别，默认为 DEBUG（开发环境）
        log_format: 日志格式
        max_bytes: 单个日志文件最大字节数，默认 10MB
        backup_count: 备份文件数量，默认 7
        enable_console: 是否启用控制台日志，默认 True
        enable_file: 是否启用文件日志，默认 False
        enable_masking: 是否启用敏感数据脱敏，默认 True
        sensitive_patterns: 自定义敏感数据模式列表
        deduplication_interval: 去重时间窗口（秒），默认 2.0
        json_format_enabled: 是否启用JSON格式，默认 True
        template_logging_enabled: 是否启用模版内容日志输出，默认 True
    """
    global _LOG_CONFIG
    
    with _lock:
        # 获取根日志记录器
        root_logger = logging.getLogger()
        
        # 配置日志目录
        if log_dir is not None:
            _LOG_CONFIG["log_dir"] = Path(log_dir)
        
        # 创建日志目录
        if enable_file:
            if not _LOG_CONFIG["log_dir"].exists():
                _LOG_CONFIG["log_dir"].mkdir(parents=True)
            # 创建性能日志子目录
            if not _LOG_CONFIG["performance_log_dir"].exists():
                _LOG_CONFIG["performance_log_dir"].mkdir(parents=True)
            
        # 更新配置
        _LOG_CONFIG["log_level"] = log_level
        if log_format is not None:
            _LOG_CONFIG["log_format"] = log_format
        if max_bytes is not None:
            _LOG_CONFIG["max_bytes"] = max_bytes
        if backup_count is not None:
            _LOG_CONFIG["backup_count"] = backup_count
        if deduplication_interval is not None:
            _LOG_CONFIG["deduplication_interval"] = deduplication_interval
        _LOG_CONFIG["json_format_enabled"] = json_format_enabled
        _LOG_CONFIG["template_logging_enabled"] = template_logging_enabled
            
        # 配置敏感数据脱敏
        _LOG_CONFIG["enable_masking"] = enable_masking
        patterns = sensitive_patterns or _SENSITIVE_PATTERNS
        _LOG_CONFIG["sensitive_data_masker"] = SensitiveDataMasker(patterns) if enable_masking else None
        
        # 清理现有的处理器
        if _LOG_CONFIG["handlers"]:
            for handler in root_logger.handlers[:]:
                root_logger.removeHandler(handler)
                handler.close()
            _LOG_CONFIG["handlers"].clear()

        # 重置根日志记录器
        root_logger = logging.getLogger()
        root_logger.setLevel(_LOG_CONFIG["log_level"])

        # 创建格式化器
        console_formatter = ColoredFormatter(_LOG_CONFIG["log_format"])
        json_formatter = EnhancedJsonFormatter() if _LOG_CONFIG["json_format_enabled"] else MaskingFormatter(_LOG_CONFIG["log_format"])

        # 创建控制台处理器
        if enable_console:
            console_handler = logging.StreamHandler()
            if high_concurrency_mode:
                # 高并发模式下使用更简单的格式化器以提高性能
                console_formatter = logging.Formatter(
                    "%(asctime)s %(levelname)s %(name)s: %(message)s",
                    datefmt="%H:%M:%S"
                )
            else:
                console_formatter = ColoredFormatter(
                    _LOG_CONFIG["log_format"]
                ) if sys.stdout.isatty() else logging.Formatter(
                    _LOG_CONFIG["log_format"]
                )
            console_handler.setFormatter(console_formatter)
            console_handler.setLevel(_LOG_CONFIG["log_level"])
            
            # 添加智能过滤器
            console_handler.addFilter(IntelligentAppLogFilter())
            
            # 在高并发模式下增加去重过滤器
            if high_concurrency_mode:
                console_handler.addFilter(EnhancedDeduplicationLogFilter(
                    interval=_LOG_CONFIG["deduplication_interval"],
                    max_cache_size=2000  # 增加缓存大小
                ))
            
            root_logger.addHandler(console_handler)
            _LOG_CONFIG["handlers"]["console"] = console_handler

        # 创建文件处理器
        if enable_file:
            # 创建日志队列 - 在高并发模式下增加队列大小
            queue_size = 10000 if high_concurrency_mode else 5000
            log_queue = Queue(maxsize=queue_size)
            
            # 初始化文件处理器列表
            file_handlers = []
            
                )
            else:
                app_formatter = MaskingFormatter(
                    _LOG_CONFIG["log_format"]
                ) if not _LOG_CONFIG["json_format_enabled"] else JsonFormatter()
            app_handler.setFormatter(app_formatter)
            app_handler.setLevel(_LOG_CONFIG["log_level"])

            # 添加智能过滤器减少冗余信息
            app_handler.addFilter(AppLogFilter())
            app_handler.addFilter(IntelligentAppLogFilter())
            
            # 在高并发模式下调整去重过滤器
            dedup_interval = 1.0 if high_concurrency_mode else _LOG_CONFIG["deduplication_interval"]
            app_handler.addFilter(EnhancedDeduplicationLogFilter(
                interval=dedup_interval,
                max_cache_size=2000 if high_concurrency_mode else 1000
            ))
            file_handlers.append(app_handler)

            # 错误日志处理器 - 只记录ERROR及以上级别，关注系统异常、外部依赖失败、不可恢复错误
            error_handler = logging.handlers.RotatingFileHandler(
                str(_LOG_CONFIG["log_dir"] / "error.log"),
                maxBytes=_LOG_CONFIG["max_bytes"],
                backupCount=_LOG_CONFIG["backup_count"],
                encoding="utf-8"
            )
            # 使用专门的错误日志格式化器
            error_formatter = ErrorJsonFormatter()
            error_handler.setFormatter(error_formatter)
            error_handler.setLevel(logging.ERROR)  # 只记录ERROR/CRITICAL级别
            # 错误日志不使用去重过滤器，确保所有错误都被记录
            file_handlers.append(error_handler)

            # 会话日志处理器 - 记录与用户会话相关的所有操作，便于追踪单个session的完整链路
            session_handler = logging.handlers.RotatingFileHandler(
                str(_LOG_CONFIG["log_dir"] / "session.log"),
                maxBytes=_LOG_CONFIG["max_bytes"],
                backupCount=_LOG_CONFIG["backup_count"],
                encoding="utf-8"
            )
            # 使用专门的会话JSON格式化器
            session_formatter = SessionJsonFormatter()
            session_handler.setFormatter(session_formatter)
            session_handler.setLevel(logging.INFO)  # 只保留INFO及以上，DEBUG只在排查时临时打开
            session_handler.addFilter(SessionLogFilter())
            # 会话日志使用轻量级去重，避免丢失重要的会话事件
            session_handler.addFilter(EnhancedDeduplicationLogFilter(
                interval=1.0,  # 1秒去重窗口，比app.log更短
                max_cache_size=1000
            ))
            file_handlers.append(session_handler)
            
            # 创建队列监听器
            queue_listener = logging.handlers.QueueListener(
                log_queue, *file_handlers, respect_handler_level=True
            )
            queue_listener.start()
            
            # 创建队列处理器
            queue_handler = logging.handlers.QueueHandler(log_queue)
            queue_handler.setLevel(_LOG_CONFIG["log_level"])
            root_logger.addHandler(queue_handler)
            
            # 保存引用
            _LOG_CONFIG["queue_handler"] = queue_handler
            _LOG_CONFIG["queue_listener"] = queue_listener
            _LOG_CONFIG["handlers"]["queue"] = queue_handler
            _LOG_CONFIG["handlers"]["app"] = app_handler
            _LOG_CONFIG["handlers"]["error"] = error_handler
            _LOG_CONFIG["handlers"]["session"] = session_handler

        # 注册退出清理函数
        if not _LOG_CONFIG["initialized"]:
            atexit.register(_cleanup_logging)

        # 标记为已初始化
        _LOG_CONFIG["initialized"] = True
        logging.info(f"优化日志系统初始化完成，日志目录：{_LOG_CONFIG['log_dir']}，"
                    f"日志级别：{logging.getLevelName(_LOG_CONFIG['log_level'])}，"
                    f"文件日志：{'启用' if enable_file else '禁用'}，"
                    f"敏感数据脱敏：{'启用' if enable_masking else '禁用'}，"
                    f"高并发模式：{'启用' if high_concurrency_mode else '禁用'}")

def is_template_logging_enabled() -> bool:
    """
    检查是否启用了模版内容日志输出

    Returns:
        bool: 是否启用模版内容日志输出
    """
    return _LOG_CONFIG.get("template_logging_enabled", False)

def get_logger(
    name: str,
    session_id: str = None,
    stage: str = None
) -> logging.Logger:
    """
    获取日志记录器

    Args:
        name: 日志记录器名称
        session_id: 会话ID
        stage: 阶段名称

    Returns:
        配置好的日志记录器
    """
    # 确保日志系统已初始化
    if not _LOG_CONFIG["initialized"]:
        configure_logging()

    # 获取日志记录器
    logger = logging.getLogger(name)

    # 如果提供了会话ID或阶段，添加会话过滤器
    if session_id or stage:
        # 移除现有的会话过滤器
        for filter_obj in logger.filters[:]:
            if isinstance(filter_obj, SessionLogFilter):
                logger.removeFilter(filter_obj)

        # 添加新的会话过滤器
        logger.addFilter(SessionLogFilter(session_id, stage))

    return logger

def log_with_context(func):
    """
    带上下文的日志装饰器

    用法：
        @log_with_context
        def some_function(arg1, arg2, ...):
            ...
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        logger = get_logger(func.__module__)

        context = {}
        if "session_id" in kwargs:
            context["session_id"] = kwargs["session_id"]
        if "stage" in kwargs:
            context["stage"] = kwargs["stage"]

        arg_str = ", ".join([str(arg) for arg in args] + [f"{k}={v}" for k, v in kwargs.items()])
        logger.debug(f"调用 {func.__name__}({arg_str})", extra=context)

        try:
            result = func(*args, **kwargs)
            logger.debug(f"{func.__name__} 返回: {result}", extra=context)
            return result
        except Exception as e:
            logger.exception(f"{func.__name__} 异常: {str(e)}", extra=context)
            raise

    return wrapper

class BusinessLogger:
    """业务日志记录器 - 专门用于记录业务流程和关键事件"""

    def __init__(self, logger_name: str, session_id: str = None):
        self.logger = get_logger(logger_name, session_id=session_id)
        self.session_id = session_id

    def log_business_flow(self, stage: str, message: str, **context):
        """记录业务流程"""
        extra = {
            "type": "business_flow",
            "stage": stage,
            "session_id": self.session_id,
            **context
        }
        self.logger.info(f"[业务流程] {stage}: {message}", extra=extra)

    def log_external_dependency(self, service: str, operation: str, status: str, duration: float = None, **context):
        """记录外部依赖调用"""
        extra = {
            "type": "external_dependency",
            "service": service,
            "operation": operation,
            "status": status,
            "duration": duration,
            "session_id": self.session_id,
            **context
        }
        message = f"[外部依赖] {service}.{operation}: {status}"
        if duration:
            message += f" ({duration:.3f}s)"
        self.logger.info(message, extra=extra)

    def log_state_transition(self, from_state: str, to_state: str, trigger: str = None, **context):
        """记录状态切换"""
        extra = {
            "type": "state_transition",
            "from_state": from_state,
            "to_state": to_state,
            "trigger": trigger,
            "session_id": self.session_id,
            **context
        }
        message = f"[状态切换] {from_state} -> {to_state}"
        if trigger:
            message += f" (触发: {trigger})"
        self.logger.info(message, extra=extra)

    def log_user_interaction(self, action: str, details: str = None, user_id: str = None, **context):
        """记录用户交互"""
        extra = {
            "type": "user_interaction",
            "action": action,
            "user_id": user_id or "anonymous",
            "session_id": self.session_id,
            **context
        }
        message = f"[用户交互] {action}"
        if details:
            message += f": {details}"
        self.logger.info(message, extra=extra)

    def log_llm_call(self, model: str, operation: str, status: str, duration: float = None,
                     token_usage: dict = None, **context):
        """记录LLM调用"""
        extra = {
            "type": "llm_call",
            "model": model,
            "operation": operation,
            "status": status,
            "duration": duration,
            "token_usage": token_usage,
            "session_id": self.session_id,
            **context
        }
        message = f"[LLM调用] {model}.{operation}: {status}"
        if duration:
            message += f" ({duration:.3f}s)"
        if token_usage:
            message += f" [tokens: {token_usage}]"
        self.logger.info(message, extra=extra)

    def log_database_operation(self, operation: str, table: str, status: str, duration: float = None,
                              affected_rows: int = None, **context):
        """记录数据库操作"""
        extra = {
            "type": "database_operation",
            "operation": operation,
            "table": table,
            "status": status,
            "duration": duration,
            "affected_rows": affected_rows,
            "session_id": self.session_id,
            **context
        }
        message = f"[数据库] {operation} {table}: {status}"
        if duration:
            message += f" ({duration:.3f}s)"
        if affected_rows is not None:
            message += f" [影响行数: {affected_rows}]"
        self.logger.info(message, extra=extra)

    def log_error(self, error_type: str, message: str, exception: Exception = None, **context):
        """记录错误信息"""
        extra = {
            "type": "error",
            "error_type": error_type,
            "session_id": self.session_id,
            **context
        }
        log_message = f"[错误] {error_type}: {message}"
        if exception:
            self.logger.error(log_message, extra=extra, exc_info=True)
        else:
            self.logger.error(log_message, extra=extra)

    def log_debug(self, component: str, message: str, **context):
        """记录调试信息"""
        extra = {
            "type": "debug",
            "component": component,
            "session_id": self.session_id,
            **context
        }
        self.logger.debug(f"[调试] {component}: {message}", extra=extra)

class ErrorLogger:
    """专门的错误日志记录器 - 只记录ERROR及以上级别，关注系统异常、外部依赖失败、不可恢复错误"""

    def __init__(self, logger_name: str, session_id: str = None, user_id: str = None):
        self.logger = get_logger(logger_name, session_id=session_id)
        self.session_id = session_id
        self.user_id = user_id

    def log_system_error(self, component: str, operation: str, error: Exception,
                        error_code: str = None, **context):
        """记录系统异常"""
        extra = {
            "type": "system_error",
            "error_category": "system_exception",
            "component": component,
            "operation": operation,
            "error_code": error_code,
            "session_id": self.session_id,
            "user_id": self.user_id,
            **context
        }
        message = f"[系统异常] {component}.{operation}: {str(error)}"
        self.logger.error(message, extra=extra, exc_info=True)

    def log_external_dependency_failure(self, service: str, operation: str, error: Exception,
                                      endpoint: str = None, timeout: float = None, **context):
        """记录外部依赖失败"""
        extra = {
            "type": "external_dependency_failure",
            "error_category": "external_service",
            "external_service": service,
            "operation": operation,
            "endpoint": endpoint,
            "timeout": timeout,
            "session_id": self.session_id,
            "user_id": self.user_id,
            **context
        }
        message = f"[外部依赖失败] {service}.{operation}: {str(error)}"
        if endpoint:
            message += f" (端点: {endpoint})"
        if timeout:
            message += f" (超时: {timeout}s)"
        self.logger.error(message, extra=extra, exc_info=True)

    def log_database_error(self, operation: str, table: str, error: Exception,
                          query: str = None, **context):
        """记录数据库错误"""
        extra = {
            "type": "database_error",
            "error_category": "database",
            "operation": operation,
            "database_table": table,
            "query": query[:200] if query else None,  # 限制查询长度
            "session_id": self.session_id,
            "user_id": self.user_id,
            **context
        }
        message = f"[数据库错误] {operation} {table}: {str(error)}"
        self.logger.error(message, extra=extra, exc_info=True)

    def log_validation_error(self, component: str, field: str, value: str,
                           validation_rule: str, **context):
        """记录数据验证错误"""
        extra = {
            "type": "validation_error",
            "error_category": "validation",
            "component": component,
            "field": field,
            "validation_rule": validation_rule,
            "invalid_value": str(value)[:100] if value else None,  # 限制值长度
            "session_id": self.session_id,
            "user_id": self.user_id,
            **context
        }
        message = f"[验证错误] {component}.{field}: 值 '{value}' 不符合规则 '{validation_rule}'"
        self.logger.error(message, extra=extra)

    def log_authentication_error(self, auth_type: str, user_identifier: str,
                                reason: str, **context):
        """记录认证错误"""
        extra = {
            "type": "authentication_error",
            "error_category": "security",
            "auth_type": auth_type,
            "user_identifier": user_identifier,
            "failure_reason": reason,
            "session_id": self.session_id,
            **context
        }
        message = f"[认证错误] {auth_type} 认证失败: {reason} (用户: {user_identifier})"
        self.logger.error(message, extra=extra)

    def log_authorization_error(self, user_id: str, resource: str, action: str,
                              reason: str, **context):
        """记录授权错误"""
        extra = {
            "type": "authorization_error",
            "error_category": "security",
            "user_id": user_id,
            "resource": resource,
            "action": action,
            "denial_reason": reason,
            "session_id": self.session_id,
            **context
        }
        message = f"[授权错误] 用户 {user_id} 无权限执行 {action} 操作 (资源: {resource}): {reason}"
        self.logger.error(message, extra=extra)

    def log_configuration_error(self, config_file: str, config_key: str,
                              error: Exception, **context):
        """记录配置错误"""
        extra = {
            "type": "configuration_error",
            "error_category": "configuration",
            "config_file": config_file,
            "config_key": config_key,
            "session_id": self.session_id,
            **context
        }
        message = f"[配置错误] {config_file}.{config_key}: {str(error)}"
        self.logger.error(message, extra=extra, exc_info=True)

    def log_resource_exhaustion(self, resource_type: str, current_usage: str,
                              limit: str, **context):
        """记录资源耗尽错误"""
        extra = {
            "type": "resource_exhaustion",
            "error_category": "resource",
            "resource_type": resource_type,
            "current_usage": current_usage,
            "limit": limit,
            "session_id": self.session_id,
            "user_id": self.user_id,
            **context
        }
        message = f"[资源耗尽] {resource_type} 使用量 {current_usage} 超过限制 {limit}"
        self.logger.error(message, extra=extra)

    def log_business_logic_error(self, component: str, business_rule: str,
                               error_details: str, **context):
        """记录业务逻辑错误"""
        extra = {
            "type": "business_logic_error",
            "error_category": "business",
            "component": component,
            "business_rule": business_rule,
            "session_id": self.session_id,
            "user_id": self.user_id,
            **context
        }
        message = f"[业务逻辑错误] {component}: 违反业务规则 '{business_rule}' - {error_details}"
        self.logger.error(message, extra=extra)

    def log_critical_error(self, component: str, error: Exception,
                          impact: str, recovery_action: str = None, **context):
        """记录严重错误（CRITICAL级别）"""
        extra = {
            "type": "critical_error",
            "error_category": "critical",
            "component": component,
            "impact": impact,
            "recovery_action": recovery_action,
            "session_id": self.session_id,
            "user_id": self.user_id,
            **context
        }
        message = f"[严重错误] {component}: {str(error)} (影响: {impact})"
        if recovery_action:
            message += f" (恢复措施: {recovery_action})"
        self.logger.critical(message, extra=extra, exc_info=True)

class SessionLogger:
    """专门的会话日志记录器 - 记录与用户会话相关的所有操作，便于追踪单个session的完整链路"""

    def __init__(self, logger_name: str, session_id: str, user_id: str = None):
        self.logger = get_logger(logger_name, session_id=session_id)
        self.session_id = session_id
        self.user_id = user_id
        self.start_time = time.time()

    def log_session_start(self, user_agent: str = None, ip_address: str = None, **context):
        """记录会话开始"""
        extra = {
            "type": "session_start",
            "stage": "session_init",
            "session_id": self.session_id,
            "user_id": self.user_id,
            "user_agent": user_agent,
            "ip_address": ip_address,
            "start_time": self.start_time,
            "visual_marker": "🚀",
            "priority": "HIGH",
            **context
        }
        message = f"🚀 [会话开始] ═══════════════════════════════════════"
        message += f"\n    会话ID: {self.session_id}"
        if self.user_id:
            message += f"\n    用户: {self.user_id}"
        if ip_address:
            message += f"\n    IP: {ip_address}"
        message += f"\n═══════════════════════════════════════"
        self.logger.info(message, extra=extra)

    def log_user_input(self, message: str, input_type: str = "text", **context):
        """记录用户输入"""
        extra = {
            "type": "user_input",
            "stage": "user_interaction",
            "session_id": self.session_id,
            "user_id": self.user_id,
            "input_type": input_type,
            "input_length": len(message),
            "visual_marker": "👤",
            "priority": "HIGH",
            **context
        }
        preview = message[:100] + "..." if len(message) > 100 else message
        log_message = f"👤 [用户输入] {preview}"
        self.logger.info(log_message, extra=extra)

    def log_ai_response(self, response: str, model: str = None, duration: float = None, **context):
        """记录AI回复"""
        extra = {
            "type": "ai_response",
            "stage": "ai_generation",
            "session_id": self.session_id,
            "user_id": self.user_id,
            "model": model,
            "response_length": len(response),
            "generation_duration": duration,
            "visual_marker": "🤖",
            "priority": "HIGH",
            **context
        }
        preview = response[:500] + "..." if len(response) > 500 else response
        log_message = f"🤖 [AI回复] {preview}"
        if model:
            log_message += f" (模型: {model})"
        if duration:
            log_message += f" (耗时: {duration:.2f}s)"
        self.logger.info(log_message, extra=extra)

    def log_state_change(self, from_state: str, to_state: str, trigger: str = None, **context):
        """记录状态变更"""
        extra = {
            "type": "state_change",
            "stage": "state_transition",
            "session_id": self.session_id,
            "user_id": self.user_id,
            "from_state": from_state,
            "to_state": to_state,
            "trigger": trigger,
            **context
        }
        message = f"[状态变更] {from_state} -> {to_state}"
        if trigger:
            message += f" (触发: {trigger})"
        self.logger.info(message, extra=extra)

    def log_business_node(self, node_name: str, node_type: str, result: str = None, **context):
        """记录关键业务节点"""
        extra = {
            "type": "business_node",
            "stage": node_type,
            "session_id": self.session_id,
            "user_id": self.user_id,
            "node_name": node_name,
            "node_type": node_type,
            "result": result,
            **context
        }
        message = f"[业务节点] {node_name} ({node_type})"
        if result:
            message += f": {result}"
        self.logger.info(message, extra=extra)

    def log_llm_call(self, model: str, scenario: str, prompt_length: int,
                     response_length: int = None, duration: float = None,
                     token_usage: dict = None, **context):
        """记录LLM调用"""
        extra = {
            "type": "llm_call",
            "stage": "llm_processing",
            "session_id": self.session_id,
            "user_id": self.user_id,
            "model": model,
            "scenario": scenario,
            "prompt_length": prompt_length,
            "response_length": response_length,
            "duration": duration,
            "token_usage": token_usage,
            **context
        }
        message = f"[LLM调用] {model} ({scenario})"
        if duration:
            message += f" 耗时: {duration:.2f}s"
        if token_usage:
            message += f" tokens: {token_usage.get('total_tokens', 'N/A')}"
        self.logger.info(message, extra=extra)

    def log_model_usage(self, scenario: str, model_name: str, provider: str, **context):
        """记录模型使用情况 - 用于追踪不同场景使用的具体模型"""
        extra = {
            "type": "model_usage",
            "stage": "model_selection",
            "session_id": self.session_id,
            "user_id": self.user_id,
            "scenario": scenario,
            "model_name": model_name,
            "provider": provider,
            **context
        }
        message = f"[模型使用] 场景: {scenario} -> 模型: {model_name} ({provider})"
        self.logger.info(message, extra=extra)


    def log_intent_recognition(self, intent: str, confidence: float, entities: dict = None, **context):
        """记录意图识别"""
        extra = {
            "type": "intent_recognition",
            "stage": "intent_analysis",
            "session_id": self.session_id,
            "user_id": self.user_id,
            "intent": intent,
            "confidence": confidence,
            "entities": entities,
            **context
        }
        message = f"[意图识别] {intent} (置信度: {confidence:.2f})"
        if entities:
            message += f" 实体: {entities}"
        self.logger.info(message, extra=extra)

    def log_domain_classification(self, domain_id: str, domain_name: str, confidence: float, **context):
        """记录领域分类"""
        extra = {
            "type": "domain_classification",
            "stage": "domain_analysis",
            "session_id": self.session_id,
            "user_id": self.user_id,
            "domain_id": domain_id,
            "domain_name": domain_name,
            "confidence": confidence,
            **context
        }
        message = f"[领域分类] {domain_name} ({domain_id}) 置信度: {confidence:.2f}"
        self.logger.info(message, extra=extra)

    def log_information_extraction(self, extracted_count: int, total_points: int,
                                 extraction_result: dict = None, **context):
        """记录信息提取"""
        extra = {
            "type": "information_extraction",
            "stage": "info_extraction",
            "session_id": self.session_id,
            "user_id": self.user_id,
            "extracted_count": extracted_count,
            "total_points": total_points,
            "extraction_result": extraction_result,
            **context
        }
        message = f"[信息提取] 成功提取 {extracted_count}/{total_points} 个关注点"
        self.logger.info(message, extra=extra)

    def log_session_end(self, reason: str = "normal", duration: float = None, **context):
        """记录会话结束"""
        if duration is None:
            duration = time.time() - self.start_time

        extra = {
            "type": "session_end",
            "stage": "session_cleanup",
            "session_id": self.session_id,
            "user_id": self.user_id,
            "end_reason": reason,
            "total_duration": duration,
            **context
        }
        message = f"[会话结束] 会话 {self.session_id} 结束"
        message += f" (原因: {reason}, 总时长: {duration:.2f}s)"
        self.logger.info(message, extra=extra)

    def log_error_in_session(self, error_type: str, error_message: str, stage: str = None, **context):
        """记录会话中的错误"""
        extra = {
            "type": "session_error",
            "stage": stage or "unknown",
            "session_id": self.session_id,
            "user_id": self.user_id,
            "error_type": error_type,
            "visual_marker": "❌",
            "priority": "CRITICAL",
            **context
        }
        message = f"❌ [会话错误] ⚠️  {error_type}: {error_message}"
        message += f"\n    阶段: {stage or 'unknown'}"
        message += f"\n    会话: {self.session_id}"
        self.logger.error(message, extra=extra)

    def log_business_quality_metrics(self, completeness_score: float = None,
                                   confidence_score: float = None,
                                   user_satisfaction_prediction: float = None,
                                   document_quality_score: float = None,
                                   stage: str = None, **context):
        """记录业务质量指标"""
        extra = {
            "type": "quality_metrics",
            "stage": stage or "quality_assessment",
            "session_id": self.session_id,
            "user_id": self.user_id,
            "completeness_score": completeness_score,
            "confidence_score": confidence_score,
            "user_satisfaction_prediction": user_satisfaction_prediction,
            "document_quality_score": document_quality_score,
            **context
        }
        message = f"[质量指标] 完整度: {completeness_score}, 置信度: {confidence_score}"
        if user_satisfaction_prediction:
            message += f", 用户满意度预测: {user_satisfaction_prediction}"
        self.logger.info(message, extra=extra)

    def log_user_experience_metrics(self, user_wait_time: float = None,
                                   processing_efficiency: float = None,
                                   response_relevance: float = None,
                                   stage: str = None, **context):
        """记录用户体验指标"""
        extra = {
            "type": "user_experience",
            "stage": stage or "ux_assessment",
            "session_id": self.session_id,
            "user_id": self.user_id,
            "user_wait_time": user_wait_time,
            "processing_efficiency": processing_efficiency,
            "response_relevance": response_relevance,
            **context
        }
        message = f"[用户体验] 等待时间: {user_wait_time}s"
        if processing_efficiency:
            message += f", 处理效率: {processing_efficiency}"
        if response_relevance:
            message += f", 回复相关性: {response_relevance}"
        self.logger.info(message, extra=extra)

    def log_error_recovery(self, error_type: str, recovery_action: str,
                          retry_count: int = 0, fallback_used: bool = False,
                          stage: str = None, **context):
        """记录错误恢复过程"""
        extra = {
            "type": "error_recovery",
            "stage": stage or "error_handling",
            "session_id": self.session_id,
            "user_id": self.user_id,
            "error_type": error_type,
            "error_recovery_action": recovery_action,
            "retry_count": retry_count,
            "fallback_used": fallback_used,
            **context
        }
        message = f"[错误恢复] {error_type} -> {recovery_action}"
        if retry_count > 0:
            message += f" (重试: {retry_count}次)"
        if fallback_used:
            message += " (使用降级方案)"
        self.logger.info(message, extra=extra)

def error_handler(component: str = None, operation: str = None,
                 error_category: str = "system_exception",
                 reraise: bool = True, default_return=None):
    """
    错误处理装饰器 - 自动捕获异常并记录到error.log

    Args:
        component: 组件名称
        operation: 操作名称
        error_category: 错误分类
        reraise: 是否重新抛出异常
        default_return: 异常时的默认返回值
    """
    def decorator(func):
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            error_logger = ErrorLogger(func.__module__)
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                # 记录错误
                comp = component or func.__qualname__
                op = operation or func.__name__

                if error_category == "external_service":
                    error_logger.log_external_dependency_failure(
                        service=comp, operation=op, error=e
                    )
                elif error_category == "database":
                    error_logger.log_database_error(
                        operation=op, table=comp, error=e
                    )
                else:
                    error_logger.log_system_error(
                        component=comp, operation=op, error=e,
                        error_code=getattr(e, 'code', None)
                    )

                if reraise:
                    raise
                return default_return

        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            error_logger = ErrorLogger(func.__module__)
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # 记录错误
                comp = component or func.__qualname__
                op = operation or func.__name__

                if error_category == "external_service":
                    error_logger.log_external_dependency_failure(
                        service=comp, operation=op, error=e
                    )
                elif error_category == "database":
                    error_logger.log_database_error(
                        operation=op, table=comp, error=e
                    )
                else:
                    error_logger.log_system_error(
                        component=comp, operation=op, error=e,
                        error_code=getattr(e, 'code', None)
                    )

                if reraise:
                    raise
                return default_return

        # 根据函数类型返回对应的包装器
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator

def database_error_handler(table: str = None, operation: str = None):
    """数据库错误处理装饰器"""
    return error_handler(
        component=table,
        operation=operation,
        error_category="database"
    )

