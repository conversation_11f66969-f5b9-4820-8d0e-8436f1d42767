#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
复合意图处理器

专门处理复合意图，如知识库查询 + 业务需求的组合处理
"""

import logging
from typing import List, Dict, Any
from backend.handlers.base_action_handler import BaseAction<PERSON>andler, ActionContext, ActionResult


class CompositeHandler(BaseActionHandler):
    """复合意图处理器"""
    
    def __init__(self, conversation_flow, rag_agent=None):
        super().__init__(conversation_flow)
        self.rag_agent = rag_agent
        self.logger = logging.getLogger(self.__class__.__name__)
        
    @property
    def supported_actions(self) -> List[str]:
        """返回支持的actions列表"""
        return [
            "handle_composite_knowledge_requirement",
        ]

    async def can_handle(self, action: str, context) -> bool:
        """检查是否可以处理指定的action"""
        return action in self.supported_actions
    
    async def handle(self, context: ActionContext) -> ActionResult:
        """处理复合意图相关的Action"""
        try:
            self.logger.info(f"处理复合意图Action: {context.action}")
            
            if context.action == "handle_composite_knowledge_requirement":
                return await self._handle_composite_knowledge_requirement(context)
            else:
                return self.create_error_result(f"不支持的复合意图action: {context.action}")
                
        except Exception as e:
            self.logger.error(f"处理复合意图Action失败: {e}")
            return self.create_error_result(f"复合意图处理失败: {e}")
    
    async def _handle_composite_knowledge_requirement(self, context: ActionContext) -> ActionResult:
        """
        处理复合意图：知识库查询 + 业务需求
        
        执行流程：
        1. 先执行知识库查询
        2. 然后生成业务需求引导
        3. 将两部分结合生成复合回复
        """
        try:
            self.logger.info(f"开始处理复合意图：RAG回复 + 继续原流程")
            self.logger.info(f"用户消息: {context.message}")

            # Step 1: 执行RAG查询并立即发送第一条回复
            self.logger.info("Step 1: 执行RAG查询")
            rag_answer = await self._query_knowledge_base(context.message)
            self.logger.info(f"Step 1 完成: RAG查询结果长度 {len(rag_answer) if rag_answer else 0}")

            # Step 2: 提取用户的功能需求部分，继续原有流程
            self.logger.info("Step 2: 提取功能需求，继续原有需求收集流程")
            requirement_message = self._extract_requirement_from_message(context.message)
            self.logger.info(f"提取的功能需求: {requirement_message}")

            # 创建新的context，只包含功能需求部分
            requirement_context = self._create_requirement_context(context, requirement_message)

            # 调用原有的需求处理逻辑
            original_response = await self._call_original_requirement_handler(requirement_context)
            self.logger.info(f"Step 2 完成: 原有需求处理完成")

            # 临时方案：组合两条回复，直到实现真正的双回复机制
            self.logger.info("🎉 复合意图处理完成，组合RAG回复和需求收集回复")

            combined_response = self._create_dual_response(rag_answer, original_response)

            # 返回组合回复
            return ActionResult(
                success=True,
                content=combined_response,
                next_state=original_response.next_state if original_response else "COLLECTING_INFO",
                metadata={
                    "action_type": "composite_dual_response_combined",
                    "rag_content_length": len(rag_answer) if rag_answer else 0,
                    "requirement_message": requirement_message,
                    "original_handler_success": original_response is not None and original_response.success if original_response else False
                }
            )
            
        except Exception as e:
            self.logger.error(f"复合意图处理失败: {e}")
            return self.create_error_result(f"复合意图处理失败: {e}")
    
    async def _query_knowledge_base(self, user_message: str) -> str:
        """执行知识库查询"""
        try:
            # Step 1: 智能提取问题部分
            question = await self._extract_question_with_llm(user_message)
            if not question:
                self.logger.info("未检测到问题，跳过RAG查询")
                return ""

            self.logger.info(f"提取的问题: {question}")

            # Step 2: 获取RAG代理
            rag_agent = self.rag_agent
            if not rag_agent:
                self.logger.info("从AgentFactory获取RAG代理")
                try:
                    from backend.agents.factory import agent_factory
                    rag_agent = agent_factory.get_rag_knowledge_base_agent()
                    if rag_agent:
                        self.logger.info("成功从AgentFactory获取RAG代理")
                        self.rag_agent = rag_agent
                except Exception as e:
                    self.logger.warning(f"从AgentFactory获取RAG代理失败: {e}")

            # Step 3: 执行RAG查询
            if rag_agent:
                self.logger.info(f"调用RAG代理查询知识库: {question}")
                rag_result = await rag_agent.query(question)  # 只查询问题部分

                # 处理不同的返回格式
                if hasattr(rag_result, 'success') and rag_result.success and hasattr(rag_result, 'answer'):
                    self.logger.info(f"RAG查询成功，获得答案长度: {len(rag_result.answer)}")
                    return rag_result.answer
                elif isinstance(rag_result, str):
                    self.logger.info(f"RAG查询成功，获得字符串答案长度: {len(rag_result)}")
                    return rag_result
                else:
                    self.logger.warning(f"RAG查询返回格式异常: {type(rag_result)}")
                    return ""
            else:
                self.logger.warning("无法获取RAG代理")
                return ""

        except Exception as e:
            self.logger.error(f"知识库查询失败: {e}")
            return ""
    

    
    async def _call_original_requirement_handler(self, context: ActionContext) -> ActionResult:
        """调用原有的需求处理逻辑"""
        try:
            # 获取RequirementHandler
            from backend.handlers.requirement_handler import RequirementHandler

            requirement_handler = RequirementHandler(self.conversation_flow)

            # 创建一个新的context，action改为原有的需求处理action
            original_context = ActionContext(
                action="process_answer_and_ask_next",  # 原有的需求处理action
                message=context.message,
                session_id=context.session_id,
                user_id=context.user_id,
                decision_result=context.decision_result,
                history=context.history,
                current_state=context.current_state,
                current_domain=context.current_domain,
                current_category=context.current_category,
                conversation_flow=context.conversation_flow
            )

            self.logger.info(f"调用RequirementHandler处理: {original_context.action}")

            # 调用原有的处理逻辑
            result = await requirement_handler.handle(original_context)

            self.logger.info(f"RequirementHandler处理完成: success={result.success}")
            return result

        except Exception as e:
            self.logger.error(f"调用原有需求处理逻辑失败: {e}")
            # 返回一个默认的处理结果
            return ActionResult(
                success=False,
                content="继续收集您的需求信息...",
                next_state="COLLECTING_INFO",
                metadata={"error": str(e)}
            )

    def _combine_rag_and_original_response(self, rag_answer: str, original_response: ActionResult) -> str:
        """组合RAG答案和原有需求处理回复"""
        try:
            if not rag_answer:
                self.logger.warning("RAG答案为空，只返回原有回复")
                return original_response.content if original_response and original_response.content else "继续收集您的需求信息..."

            if not original_response or not original_response.content:
                self.logger.warning("原有回复为空，只返回RAG答案")
                return rag_answer

            # 组合RAG答案和原有回复
            combined = f"""📚 **关于您的询问**
{rag_answer.strip()}

---

{original_response.content.strip()}"""

            self.logger.info("成功组合RAG答案和原有需求处理回复")
            return combined

        except Exception as e:
            self.logger.error(f"组合回复失败: {e}")
            fallback = f"{rag_answer}\n\n继续收集您的需求信息..." if rag_answer else "继续收集您的需求信息..."
            return fallback

    async def _extract_question_with_llm(self, user_message: str) -> str:
        """使用LLM智能提取问题部分"""
        try:
            self.logger.info(f"使用LLM提取问题: {user_message}")

            # 构建问题提取的prompt
            prompt = f"""你是一个智能文本分析助手，请从用户输入中提取问题部分。

用户输入：{user_message}

任务：
1. 识别用户输入中是否包含问题（疑问句）
2. 如果包含问题，请提取出完整的问题部分
3. 如果不包含问题，请返回空字符串

要求：
- 只返回问题部分，不要其他内容
- 保持问题的完整性和原意
- 如果有多个问题，提取最主要的一个

示例：
输入："我需要开发网站，你们支持哪些技术栈？"
输出："你们支持哪些技术栈？"

输入："用户注册、商品展示、在线支付"
输出：""

请分析上述用户输入："""

            # 获取LLM服务
            if hasattr(self.conversation_flow, 'llm_service'):
                llm_service = self.conversation_flow.llm_service

                # 调用LLM进行问题提取
                response = await llm_service.call_llm(
                    messages=[{"role": "user", "content": prompt}],
                    scenario="question_extraction",  # 使用轻量级模型
                    max_tokens=100,
                    temperature=0.1  # 低温度，确保准确性
                )

                extracted_question = response.get("content", "").strip()

                # 验证提取结果
                if extracted_question and len(extracted_question) > 0:
                    # 简单验证是否是问句
                    if any(marker in extracted_question for marker in ['？', '?', '吗', '呢', '什么', '如何', '怎么', '哪些', '多少']):
                        self.logger.info(f"LLM成功提取问题: {extracted_question}")
                        return extracted_question
                    else:
                        self.logger.info("LLM提取的内容不是问句，返回空")
                        return ""
                else:
                    self.logger.info("LLM未提取到问题")
                    return ""

            else:
                self.logger.warning("无法获取LLM服务，使用简单规则提取")
                return self._extract_question_with_rules(user_message)

        except Exception as e:
            self.logger.error(f"LLM问题提取失败: {e}")
            # 回退到规则提取
            return self._extract_question_with_rules(user_message)

    def _extract_question_with_rules(self, message: str) -> str:
        """使用规则提取问题（回退方案）"""
        try:
            # 按句号分割，查找问句
            parts = message.split('。')
            for part in reversed(parts):  # 从后往前找
                part = part.strip()
                if part and ('？' in part or '?' in part):
                    return part

            # 如果没有句号，直接检查是否是问句
            if '？' in message or '?' in message:
                return message.strip()

            return ""

        except Exception as e:
            self.logger.error(f"规则问题提取失败: {e}")
            return ""

    async def _send_immediate_rag_response(self, context: ActionContext, rag_answer: str):
        """立即发送RAG回复（第一条回复）"""
        try:
            self.logger.info("发送RAG回复（第一条回复）")

            # 通过conversation_flow发送立即回复
            if hasattr(self.conversation_flow, 'send_immediate_response'):
                await self.conversation_flow.send_immediate_response(
                    session_id=context.session_id,
                    user_id=context.user_id,
                    content=rag_answer
                )
            else:
                # 如果没有立即发送功能，记录日志
                self.logger.warning("conversation_flow不支持立即发送，RAG回复将在后续处理")

        except Exception as e:
            self.logger.error(f"发送RAG回复失败: {e}")

    def _extract_requirement_from_message(self, message: str) -> str:
        """从复合消息中提取功能需求部分"""
        try:
            # 简单的提取逻辑：去掉问句部分
            # 例如："用户注册、商品展示、在线支付。你们平台支持哪些支付？"
            # 提取为："用户注册、商品展示、在线支付"

            # 按句号分割，取第一部分
            parts = message.split('。')
            if len(parts) > 1:
                requirement_part = parts[0].strip()
                self.logger.info(f"提取功能需求部分: '{requirement_part}'")
                return requirement_part

            # 如果没有句号，按问号分割
            parts = message.split('？')
            if len(parts) > 1:
                requirement_part = parts[0].strip()
                self.logger.info(f"提取功能需求部分: '{requirement_part}'")
                return requirement_part

            # 如果都没有，返回原消息
            self.logger.info("未找到明显的分割点，返回原消息")
            return message

        except Exception as e:
            self.logger.error(f"提取功能需求失败: {e}")
            return message

    def _create_requirement_context(self, original_context: ActionContext, requirement_message: str) -> ActionContext:
        """创建只包含功能需求的新context"""
        return ActionContext(
            action="process_answer_and_ask_next",
            message=requirement_message,  # 只包含功能需求部分
            session_id=original_context.session_id,
            user_id=original_context.user_id,
            decision_result=original_context.decision_result,
            history=original_context.history,
            current_state=original_context.current_state,
            current_domain=original_context.current_domain,
            current_category=original_context.current_category,
            conversation_flow=original_context.conversation_flow
        )

    def _create_dual_response(self, rag_answer: str, original_response: ActionResult) -> str:
        """创建双回复的组合内容"""
        try:
            # 如果没有RAG答案（用户没有问问题），只返回需求收集回复
            if not rag_answer or len(rag_answer.strip()) == 0:
                self.logger.info("无RAG答案，只返回需求收集回复")
                return original_response.content if original_response else "继续收集您的需求信息..."

            # 如果没有需求收集回复，只返回RAG答案
            if not original_response or not original_response.content:
                self.logger.info("无需求收集回复，只返回RAG答案")
                return rag_answer

            # 组合两条回复，明确标识
            combined = f"""📚 **关于您的询问**
{rag_answer.strip()}

---

{original_response.content.strip()}"""

            self.logger.info("成功组合RAG回复和需求收集回复")
            return combined

        except Exception as e:
            self.logger.error(f"组合双回复失败: {e}")
            return original_response.content if original_response else "继续收集您的需求信息..."

    async def _update_session_state(self, context: ActionContext, new_state: str):
        """更新会话状态"""
        try:
            # 这里可以调用会话管理器更新状态
            # 暂时通过日志记录
            self.logger.info(f"会话状态更新: {context.session_id} -> {new_state}")
        except Exception as e:
            self.logger.error(f"更新会话状态失败: {e}")
