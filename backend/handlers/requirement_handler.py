#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
@File    :   requirement_handler.py
@Time    :   2025/05/13
<AUTHOR>   Your Name
@Description : 需求采集处理器 - 处理需求采集相关的actions
"""

from typing import List
from .base_action_handler import BaseActionHandler, ActionContext, ActionResult
from backend.config.unified_config_loader import get_unified_config


class RequirementHandler(BaseActionHandler):
    """需求采集处理器"""
    
    @property
    def supported_actions(self) -> List[str]:
        return [
            "start_requirement_gathering",
            "start_requirement_collection",  # 添加对决策引擎返回的动作的支持
            "start_focused_requirement_gathering",
            "continue_requirement_gathering",
            "process_answer_and_ask_next",
            "skip_question_and_ask_next",
            "rephrase_and_inquire",
            "apologize_and_request_refinement",
            "provide_suggestions",
            "request_clarification"
        ]
    
    async def can_handle(self, action: str, context: ActionContext) -> bool:
        return action in self.supported_actions
    
    async def handle(self, context: ActionContext) -> ActionResult:
        """处理需求采集相关的action"""
        
        if not await self.validate_context(context):
            return self.create_error_result("上下文验证失败")
        
        try:
            if context.action in ["start_requirement_gathering", "start_requirement_collection", "start_focused_requirement_gathering"]:
                return await self._handle_start_gathering(context)
            elif context.action == "continue_requirement_gathering":
                return await self._handle_continue_gathering(context)
            elif context.action == "process_answer_and_ask_next":
                return await self._handle_process_answer(context)
            elif context.action == "skip_question_and_ask_next":
                return await self._handle_skip_question(context)
            elif context.action == "rephrase_and_inquire":
                return await self._handle_rephrase(context)
            elif context.action == "apologize_and_request_refinement":
                return await self._handle_apology_request(context)
            elif context.action == "provide_suggestions":
                return await self._handle_provide_suggestions(context)
            elif context.action == "request_clarification":
                return await self._handle_request_clarification(context)
            else:
                return self.create_error_result(f"不支持的action: {context.action}")
                
        except Exception as e:
            self.logger.error(f"处理action {context.action} 失败: {e}", exc_info=True)
            return self.create_error_result(str(e))
    
    async def _handle_start_gathering(self, context: ActionContext) -> ActionResult:
        """处理开始需求采集"""
        try:
            # 检查是否需要结合知识库查询
            knowledge_content = await self._check_and_query_knowledge_base(context)

            # 调用无状态的_process_intent方法, 该方法现在返回一个字典
            result_dict = await self.conversation_flow._process_intent(
                message=context.message,
                session_id=context.session_id,
                user_id=context.user_id,
                decision_result=context.decision_result
            )

            # 如果有知识库内容，将其与业务流程回复结合
            final_content = result_dict.get("content", "")
            if knowledge_content:
                final_content = self._combine_knowledge_and_business_response(
                    knowledge_content, final_content, context.message
                )

            # 将领域和分类结果打包到metadata中
            return self.create_success_result(
                content=final_content,
                action_type="start_gathering",
                domain_result=result_dict.get("domain_result"),
                category_result=result_dict.get("category_result"),
                focus_points_status=result_dict.get("focus_points_status")
            )

        except Exception as e:
            self.logger.error(f"开始需求采集失败: {e}", exc_info=True)
            return self.create_error_result(f"开始需求采集失败: {e}")
    
    async def _handle_continue_gathering(self, context: ActionContext) -> ActionResult:
        """处理继续需求采集"""
        try:
            # 调用无状态的_process_intent方法, 该方法现在返回一个字典
            result_dict = await self.conversation_flow._process_intent(
                message=context.message,
                session_id=context.session_id,
                user_id=context.user_id,
                decision_result=context.decision_result
            )

            # 将领域和分类结果打包到metadata中
            return self.create_success_result(
                content=result_dict.get("content"),
                action_type="continue_gathering",
                domain_result=result_dict.get("domain_result"),
                category_result=result_dict.get("category_result"),
                focus_points_status=result_dict.get("focus_points_status")
            )

        except Exception as e:
            self.logger.error(f"继续需求采集失败: {e}", exc_info=True)
            return self.create_error_result(f"继续需求采集失败: {e}")
    
    async def _handle_process_answer(self, context: ActionContext) -> ActionResult:
        """处理用户回答并提出下一个问题"""
        try:
            self.logger.info(f"处理用户回答，用户情绪: {context.emotion}")
            result = await self.conversation_flow.handle_process_answer_and_ask_next(
                message=context.message,
                session_id=context.session_id,
                user_id=context.user_id,
                history=context.history,
                decision_result=context.decision_result
            )
            
            return self.create_success_result(
                content=result,
                action_type="process_answer"
            )
            
        except Exception as e:
            self.logger.error(f"处理用户回答失败: {e}")
            return self.create_error_result(f"处理用户回答失败: {e}")
    
    async def _handle_skip_question(self, context: ActionContext) -> ActionResult:
        """处理跳过问题"""
        try:
            result = await self.conversation_flow.handle_skip_question_and_ask_next(
                message=context.message,
                session_id=context.session_id,
                user_id=context.user_id,
                decision_result=context.decision_result
            )
            
            return self.create_success_result(
                content=result,
                action_type="skip_question"
            )
            
        except Exception as e:
            self.logger.error(f"跳过问题失败: {e}")
            return self.create_error_result(f"跳过问题失败: {e}")
    
    async def _handle_rephrase(self, context: ActionContext) -> ActionResult:
        """处理重新表述问题"""
        try:
            result = await self.conversation_flow.handle_rephrase_and_inquire(
                message=context.message,
                session_id=context.session_id,
                decision_result=context.decision_result
            )
            
            return self.create_success_result(
                content=result,
                action_type="rephrase"
            )
            
        except Exception as e:
            self.logger.error(f"重新表述问题失败: {e}")
            return self.create_error_result(f"重新表述问题失败: {e}")
    
    async def _handle_apology_request(self, context: ActionContext) -> ActionResult:
        """处理道歉并请求补充"""
        try:
            result = await self.conversation_flow.handle_apology_and_request(
                message=context.message,
                session_id=context.session_id,
                decision_result=context.decision_result
            )
            
            return self.create_success_result(
                content=result,
                action_type="apology_request"
            )
            
        except Exception as e:
            self.logger.error(f"道歉并请求补充失败: {e}")
            return self.create_error_result(f"道歉并请求补充失败: {e}")
    
    async def _handle_provide_suggestions(self, context: ActionContext) -> ActionResult:
        """处理提供建议"""
        try:
            result = await self.conversation_flow.handle_provide_suggestions(
                session_id=context.session_id,
                message=context.message,
                user_id=context.user_id,
                history=context.history
            )
            
            return self.create_success_result(
                content=result,
                action_type="provide_suggestions"
            )
            
        except Exception as e:
            self.logger.error(f"提供建议失败: {e}")
            return self.create_error_result(f"提供建议失败: {e}")
    
    async def _handle_request_clarification(self, context: ActionContext) -> ActionResult:
        """处理请求澄清"""
        try:
            result = await self.conversation_flow.handle_request_clarification(
                message=context.message,
                session_id=context.session_id,
                decision_result=context.decision_result
            )
            
            return self.create_success_result(
                content=result,
                action_type="request_clarification"
            )
            
        except Exception as e:
            self.logger.error(f"请求澄清失败: {e}")
            return self.create_error_result(f"请求澄清失败: {e}")

    async def _check_and_query_knowledge_base(self, context: ActionContext) -> str:
        """检查是否需要查询知识库，并返回相关内容"""
        try:
            # 检查消息中是否包含知识库查询相关的关键词
            message_lower = context.message.lower()
            knowledge_keywords = [
                "支持哪些", "有哪些功能", "提供什么", "能用什么",
                "如何使用", "怎么使用", "使用方法", "操作步骤",
                "什么是", "介绍一下", "功能说明", "如何操作",
                "收费标准", "价格说明", "套餐介绍"
            ]

            has_knowledge_query = any(keyword in message_lower for keyword in knowledge_keywords)

            if has_knowledge_query:
                self.logger.info(f"检测到知识库查询需求，执行RAG查询: {context.message}")

                # 获取RAG代理
                from backend.agents.factory import agent_factory
                rag_agent = agent_factory.get_rag_knowledge_base_agent()

                if rag_agent:
                    rag_result = await rag_agent.query(context.message)
                    if hasattr(rag_result, 'success') and rag_result.success and hasattr(rag_result, 'answer'):
                        self.logger.info(f"知识库查询成功，获得答案长度: {len(rag_result.answer)}")
                        return rag_result.answer
                    else:
                        self.logger.info("知识库查询无结果")
                else:
                    self.logger.warning("无法获取RAG代理")

            return ""

        except Exception as e:
            self.logger.error(f"知识库查询失败: {e}")
            return ""

    def _combine_knowledge_and_business_response(self, knowledge_content: str, business_content: str, user_message: str) -> str:
        """将知识库内容与业务流程回复结合"""
        try:
            # 构建复合回复
            combined_response = f"{knowledge_content}\n\n"

            # 添加引导到业务流程的过渡语句
            transition_phrases = [
                "基于您提到的需求，",
                "了解了这些信息后，",
                "结合您的具体情况，",
                "为了更好地帮助您，"
            ]

            # 选择合适的过渡语句
            transition = transition_phrases[0]  # 简单选择第一个

            combined_response += f"{transition}{business_content}"

            self.logger.info("成功结合知识库内容和业务流程回复")
            return combined_response

        except Exception as e:
            self.logger.error(f"结合回复内容失败: {e}")
            # 如果结合失败，至少返回业务内容
            return business_content or knowledge_content

# 获取关注点优先级配置
p0_required = get_unified_config().get_business_rule("business_rules.focus_point_priority.p0", True)
p1_required = get_unified_config().get_business_rule("business_rules.focus_point_priority.p1", True)
p2_required = get_unified_config().get_business_rule("business_rules.focus_point_priority.p2", False)

# 判断是否需要采集该关注点
def is_collection_required(focus_point):
    priority = focus_point.get("priority", "p0")
    if priority == "p0":
        return p0_required
    elif priority == "p1":
        return p1_required
    elif priority == "p2":
        return p2_required
    return True  # 默认情况下必须采集
