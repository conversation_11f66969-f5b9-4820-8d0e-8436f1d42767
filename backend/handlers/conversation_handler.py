#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
会话管理处理器 (Conversation Handler)

核心功能：
    1. 处理会话生命周期相关的操作
    2. 管理会话状态转换
    3. 提供标准化的action处理接口

支持的操作类型：
    - reset_conversation: 重置当前会话(清空上下文)
    - restart_conversation: 重启会话(重新初始化)
    - finalize_and_reset: 完成当前会话并重置
    - respond_with_greeting: 响应问候消息

设计原则：
    1. 每个action对应一个独立处理方法
    2. 统一错误处理和日志记录
    3. 状态管理标准化
    4. 自包含业务逻辑，不依赖conversation_flow
"""

from typing import List
from datetime import datetime
from .base_action_handler import BaseActionHandler, ActionContext, ActionResult
from backend.agents.session_context import SessionContextManager, ConversationState
from backend.data.db.message_manager import MessageManager
from backend.data.db.focus_point_manager import FocusPointManager
from backend.data.db.database_manager import DatabaseManager
from backend.config.unified_config_loader import get_unified_config
from backend.services.conversation_history_service import ConversationHistoryService


class ConversationHandler(BaseActionHandler):
    """
    自包含的会话管理处理器

    重构后的设计：
    - 不再依赖conversation_flow，直接操作数据库和服务
    - 自包含所有会话管理的业务逻辑
    - 通过依赖注入获取必要的服务
    """

    def __init__(self,
                 db_manager: DatabaseManager,
                 session_context_manager: SessionContextManager,
                 message_manager: MessageManager,
                 focus_point_manager: FocusPointManager,
                 history_service: ConversationHistoryService,
                 reply_factory=None,
                 llm_client=None):
        """
        初始化会话处理器

        Args:
            db_manager: 数据库管理器
            session_context_manager: 会话上下文管理器
            message_manager: 消息管理器
            focus_point_manager: 关注点管理器
            history_service: 对话历史服务
            reply_factory: 回复生成工厂（可选）
            llm_client: LLM客户端（可选）
        """
        super().__init__(None)  # 不需要conversation_flow
        self.db_manager = db_manager
        self.session_context_manager = session_context_manager
        self.message_manager = message_manager
        self.focus_point_manager = focus_point_manager
        self.history_service = history_service
        self.reply_factory = reply_factory
        self.llm_client = llm_client
    
    @property
    def supported_actions(self) -> List[str]:
        """获取处理器支持的action类型列表
        
        返回:
            List[str]: 支持的action名称列表，包含:
                - reset_conversation: 重置会话
                - restart_conversation: 重启会话
                - finalize_and_reset: 完成并重置
                - respond_with_greeting: 响应问候
        """
        return [
            "reset_conversation",
            "restart_conversation",
            "finalize_and_reset",
            "respond_with_greeting",
            "send_greeting"
        ]
    
    async def can_handle(self, action: str, context: ActionContext) -> bool:
        """判断是否能处理指定的action
        
        参数:
            action (str): 要检查的action名称
            context (ActionContext): 当前action上下文
            
        返回:
            bool: 如果action在supported_actions列表中则返回True
        """
        return action in self.supported_actions
    
    async def handle(self, context: ActionContext) -> ActionResult:
        """处理会话相关的action
        
        主要流程:
        1. 验证上下文有效性
        2. 根据action类型路由到对应的处理方法
        3. 统一错误捕获和日志记录
        
        参数:
            context (ActionContext): 包含action执行所需的所有上下文信息
            
        返回:
            ActionResult: 包含处理结果和状态信息
            
        异常:
            捕获所有异常并转换为标准错误结果返回
        """
        
        if not await self.validate_context(context):
            return self.create_error_result("上下文验证失败")
        
        try:
            if context.action == "reset_conversation":
                return await self._handle_reset(context)
            elif context.action == "restart_conversation":
                return await self._handle_restart(context)
            elif context.action == "finalize_and_reset":
                return await self._handle_finalize(context)
            elif context.action == "respond_with_greeting":
                return await self._handle_greeting(context)
            elif context.action == "send_greeting":
                return await self._handle_greeting(context)  # 使用相同的处理逻辑
            else:
                return self.create_error_result(f"不支持的action: {context.action}")
                
        except Exception as e:
            self.logger.error(f"处理action {context.action} 失败: {e}", exc_info=True)
            return self.create_error_result(str(e))
    
    async def _handle_reset(self, context: ActionContext) -> ActionResult:
        """处理重置会话action

        业务逻辑:
        1. 重置会话状态为IDLE
        2. 清理关注点状态
        3. 清理消息历史
        4. 更新数据库状态

        参数:
            context (ActionContext): 包含会话ID、消息等上下文

        返回:
            ActionResult: 标准化结果对象，包含:
                - content: 处理结果内容
                - next_state: 下一状态(IDLE)
                - action_type: 操作类型(reset)
        """
        try:
            session_id = context.session_id
            user_id = context.user_id

            self.logger.info(f"开始重置会话 - session_id: {session_id}, user_id: {user_id}")

            # 1. 重置关注点状态
            await self.focus_point_manager.reset_focus_points_status(session_id, user_id)
            self.logger.debug(f"关注点状态已重置 - session_id: {session_id}")

            # 2. 更新会话状态为IDLE
            await self._update_conversation_state(session_id, user_id, ConversationState.IDLE)
            self.logger.debug(f"会话状态已更新为IDLE - session_id: {session_id}")

            # 3. 清理会话历史消息
            await self.message_manager.clear_conversation_messages(session_id, user_id)
            self.logger.debug(f"会话历史已清理 - session_id: {session_id}")

            # 4. 清理会话相关数据（领域、分类等）
            await self._clear_conversation_data(session_id, user_id)

            self.logger.info(f"会话重置完成 - session_id: {session_id}")

            # 生成重置确认消息
            content = get_unified_config().get_message_template("system.session.reset_complete", session_id=session_id)

            return self.create_success_result(
                content=content,
                next_state="IDLE",
                action_type="reset"
            )

        except Exception as e:
            self.logger.error(f"重置会话失败: {e}", exc_info=True)
            return self.create_error_result(f"重置会话失败: {e}")
    
    async def _handle_restart(self, context: ActionContext) -> ActionResult:
        """处理重启会话action

        业务逻辑:
        1. 完全重置会话状态（类似reset但更彻底）
        2. 清理所有会话相关数据
        3. 重新初始化会话
        4. 生成重新开始的确认消息

        参数:
            context (ActionContext): 包含会话ID、消息等上下文

        返回:
            ActionResult: 标准化结果对象，包含:
                - content: 处理结果内容
                - next_state: 下一状态(IDLE)
                - action_type: 操作类型(restart)
        """
        try:
            session_id = context.session_id
            user_id = context.user_id

            self.logger.info(f"开始重启会话 - session_id: {session_id}, user_id: {user_id}")

            # 1. 执行完整的重置操作
            await self._perform_full_reset(session_id, user_id)

            # 2. 重新初始化会话
            await self._initialize_fresh_session(session_id, user_id)

            self.logger.info(f"会话重启完成 - session_id: {session_id}")

            # 3. 生成重启确认消息
            if self.reply_factory and self.llm_client:
                # 使用动态回复生成
                prompt_instruction = get_unified_config().get_message_template("prompts.restart.instruction")
                content = await self.reply_factory.generate_greeting_reply(
                    prompt_instruction=prompt_instruction,
                    user_message=context.message,
                    session_id=session_id
                )
            else:
                # 使用静态模板
                content = get_unified_config().get_message_template("system.session.reset_complete")

            return self.create_success_result(
                content=content,
                next_state="IDLE",
                action_type="restart"
            )

        except Exception as e:
            self.logger.error(f"重启会话失败: {e}", exc_info=True)
            return self.create_error_result(f"重启会话失败: {e}")
    
    async def _handle_finalize(self, context: ActionContext) -> ActionResult:
        """处理完成并重置action
        
        业务逻辑:
        1. 调用conversation_flow的handle_finalize_and_reset方法
        2. 返回标准化的成功结果
        3. 将会话状态设置为IDLE
        
        参数:
            context (ActionContext): 包含会话ID、消息等上下文
            
        返回:
            ActionResult: 标准化结果对象，包含:
                - content: 处理结果内容
                - next_state: 下一状态(IDLE)
                - action_type: 操作类型(finalize)
        """
        try:
            session_id = context.session_id
            user_id = context.user_id

            self.logger.info(f"开始完成并重置会话 - session_id: {session_id}, user_id: {user_id}")

            # 1. 保存完成状态到数据库
            await self._save_completion_status(session_id, user_id)

            # 2. 执行重置操作
            await self._perform_full_reset(session_id, user_id)

            self.logger.info(f"会话完成并重置 - session_id: {session_id}")

            # 3. 生成完成确认消息
            content = get_unified_config().get_message_template("confirmation.document_finalized")

            return self.create_success_result(
                content=content,
                next_state="IDLE",
                action_type="finalize"
            )

        except Exception as e:
            self.logger.error(f"完成并重置失败: {e}", exc_info=True)
            return self.create_error_result(f"完成并重置失败: {e}")
    
    async def _handle_greeting(self, context: ActionContext) -> ActionResult:
        """处理问候action
        
        业务逻辑:
        1. 生成问候回复（优先使用动态生成）
        2. 返回标准化的成功结果
        3. 不改变当前会话状态
        
        参数:
            context (ActionContext): 包含会话ID、消息等上下文
            
        返回:
            ActionResult: 标准化结果对象，包含:
                - content: 问候响应内容
                - action_type: 操作类型(greeting)
        """
        try:
            session_id = context.session_id
            user_id = context.user_id

            self.logger.info(f"开始处理问候 - session_id: {session_id}, user_id: {user_id}")

            # 🔍 调试日志：检查组件初始化状态
            self.logger.info(f"[调试] reply_factory 状态: {self.reply_factory is not None} (类型: {type(self.reply_factory).__name__ if self.reply_factory else 'None'})")
            self.logger.info(f"[调试] llm_client 状态: {self.llm_client is not None} (类型: {type(self.llm_client).__name__ if self.llm_client else 'None'})")
            self.logger.info(f"[调试] 条件检查结果: {bool(self.reply_factory and self.llm_client)}")

            # 生成问候回复
            content = None
            if self.reply_factory and self.llm_client:
                self.logger.info(f"[调试] 开始尝试动态回复生成...")
                try:
                    # 使用动态回复生成
                    prompt_instruction = "用户向你问候，请友好回应并简要介绍你是一个AI需求采集助手。"
                    self.logger.info(f"[调试] 调用 generate_greeting_reply，用户消息: '{context.message}'")
                    content = await self.reply_factory.generate_greeting_reply(
                        prompt_instruction=prompt_instruction,
                        user_message=context.message,
                        session_id=session_id
                    )
                    self.logger.info(f"✅ 动态回复生成成功: {content[:50] if content else 'None'}...")
                except Exception as e:
                    self.logger.warning(f"❌ 动态回复生成失败: {e}，使用静态模板")
                    self.logger.exception(f"[调试] 动态回复生成异常详情:")
                    content = None
            else:
                self.logger.info(f"[调试] 跳过动态回复生成，使用静态模板")

            if not content:
                # 使用静态模板
                content = get_unified_config().get_message_template("system.welcome",
                    default="您好！我是AI需求分析师，专门帮助您梳理和分析项目需求。请告诉我您想要做什么项目？")
                self.logger.info(f"使用静态模板: {content[:50] if content else 'None'}...")

            # 确保content不为空
            if not content:
                content = "您好！我是AI需求分析师，很高兴为您服务！请告诉我您的项目需求。"
                self.logger.warning("使用兜底问候消息")

            self.logger.info(f"问候处理完成 - session_id: {session_id}, content_length: {len(content) if content else 0}")

            return self.create_success_result(
                content=content,
                action_type="greeting"
            )

        except Exception as e:
            self.logger.error(f"处理问候失败: {e}", exc_info=True)
            return self.create_error_result(f"处理问候失败: {e}")

    # ==================== 辅助方法 ====================

    async def _update_conversation_state(self, session_id: str, user_id: str, state: ConversationState):
        """
        更新会话状态

        注意：ConversationState 是流程状态，不直接存储在数据库中。
        数据库中的 status 字段用于会话生命周期管理。
        流程状态通过数据状态推断（如是否有草稿文档、是否有领域分类等）。

        这里我们只更新 updated_at 时间戳，实际的状态通过数据清理来实现。
        """
        try:
            # 更新会话的最后更新时间
            await self.db_manager.execute_update(
                get_unified_config().get_database_query("conversations.update_last_activity"),
                (datetime.now().isoformat(), datetime.now().isoformat(), session_id, user_id)
            )
            self.logger.debug(f"会话时间戳已更新: {session_id} -> {state.name}")
        except Exception as e:
            self.logger.error(f"更新会话时间戳失败: {e}")
            raise

    async def _clear_conversation_data(self, session_id: str, user_id: str):
        """清理会话相关数据（领域、分类等）"""
        try:
            await self.db_manager.execute_update(
                """
                UPDATE conversations
                SET domain_id = NULL, category_id = NULL, updated_at = ?
                WHERE conversation_id = ? AND user_id = ?
                """,
                (datetime.now().isoformat(), session_id, user_id)
            )
            self.logger.debug(f"会话数据已清理: {session_id}")
        except Exception as e:
            self.logger.error(f"清理会话数据失败: {e}")
            raise

    async def _perform_full_reset(self, session_id: str, user_id: str):
        """执行完整的会话重置"""
        try:
            # 1. 重置关注点状态
            await self.focus_point_manager.reset_focus_points_status(session_id, user_id)

            # 2. 更新会话状态为IDLE
            await self._update_conversation_state(session_id, user_id, ConversationState.IDLE)

            # 3. 清理会话历史消息
            await self.message_manager.clear_conversation_messages(session_id, user_id)

            # 4. 清理会话相关数据
            await self._clear_conversation_data(session_id, user_id)

            self.logger.debug(f"完整重置完成: {session_id}")
        except Exception as e:
            self.logger.error(f"完整重置失败: {e}")
            raise

    async def _initialize_fresh_session(self, session_id: str, user_id: str):
        """初始化全新的会话"""
        try:
            # 确保会话记录存在
            await self.message_manager.ensure_conversation_exists(session_id, user_id)

            # 设置初始状态
            await self._update_conversation_state(session_id, user_id, ConversationState.IDLE)

            self.logger.debug(f"全新会话初始化完成: {session_id}")
        except Exception as e:
            self.logger.error(f"初始化全新会话失败: {e}")
            raise

    async def _save_completion_status(self, session_id: str, user_id: str):
        """保存会话完成状态"""
        try:
            # 记录完成时间并更新状态
            completion_time = datetime.now().isoformat()

            await self.db_manager.execute_update(
                """
                UPDATE conversations
                SET status = 'completed', updated_at = ?
                WHERE conversation_id = ? AND user_id = ?
                """,
                (completion_time, session_id, user_id)
            )

            self.logger.debug(f"完成状态已保存: {session_id}")
        except Exception as e:
            self.logger.error(f"保存完成状态失败: {e}")
            # 这个错误不应该阻止重置流程，所以不抛出异常
