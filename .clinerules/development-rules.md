## Brief overview
  These guidelines outline core development principles and practices for this project, focusing on configuration-driven development, code quality, and strict prohibitions to ensure maintainability and consistency.

## 🎯 核心原则

### 配置驱动开发
- 所有可变值必须通过配置管理，禁止硬编码
- 使用统一配置服务 `get_unified_config()`
- 配置键使用层级结构，如 `thresholds.confidence.default`
- 为所有配置提供合理默认值

### 代码质量原则
- 遵循DRY原则，避免重复代码
- 使用精确导入，避免通配符导入 `from module import *`
- 每个函数/类单一职责
- 使用依赖注入而非直接创建依赖

## 🚫 严格禁止事项

### 配置相关禁止事项
```python
# ❌ 禁止：硬编码配置值
capability_keywords = ["能做什么", "有什么功能", "功能", "能力"]
temperature = 0.7
max_tokens = 4000

# ❌ 禁止：重复调用配置（性能问题）
def process_request():
    config1 = get_unified_config()  # 第一次调用
    template = config1.get_message_template("greeting.basic")
    config2 = get_unified_config()  # 重复调用，应该复用
    keywords = config2.get_config_value("keywords.greeting")

# ❌ 禁止：缺少回退机制
keywords = config.get_config_value("some.path")  # 如果配置不存在会崩溃
# 应该有默认值：keywords = config.get_config_value("some.path") or ["默认值"]

# ❌ 禁止：直接读取已整合的配置文件
with open('message_templates.yaml') as f:  # 该文件已整合到 unified_config.yaml
    templates = yaml.load(f)
```

### 代码质量禁止事项
```python
# ❌ 禁止：未使用的导入
import os  # 但代码中没有使用os
from typing import Dict, List, Optional  # 只使用了List

# ❌ 禁止：重复的代码逻辑
def process_user_data():
    if not user_id:
        return "用户ID不能为空"  # 相同的验证逻辑
    # ...

def handle_user_request():
    if not user_id:
        return "用户ID不能为空"  # 相同的验证逻辑
    # ...

# ❌ 禁止：通配符导入
from utils import *

# ❌ 禁止：硬编码中文字符串
return "抱歉，系统出现错误"
print("用户登录成功")
raise Exception("配置文件不存在")
```

## ✅ 推荐做法

### 配置管理最佳实践
```python
# ✅ 推荐：使用统一配置服务
class MyService:
    def __init__(self):
        # 在初始化时缓存配置
        self.config = get_unified_config()
    
    def process(self):
        # 使用缓存的配置
        temperature = self.config.get_threshold("confidence.default", 0.7)
        max_tokens = self.config.get_threshold("limits.max_tokens", 4000)

# ✅ 推荐：使用配置化的消息模板
return self.config.get_message_template("error.system_error")
logger.info(self.config.get_message_template("info.user_login_success"))
```

### 代码质量最佳实践
```python
# ✅ 推荐：精确导入
from typing import List, Dict
from backend.config.unified_config_loader import get_unified_config

# ✅ 推荐：提取公共逻辑
def validate_user_id(user_id: str) -> str:
    """统一的用户ID验证逻辑"""
    if not user_id:
        return "用户ID不能为空"
    return None

# ✅ 推荐：使用依赖注入
class UserService:
    def __init__(self, config_service, validator):
        self.config_service = config_service
        self.validator = validator
```

## 🔧 技术实施规范

### 代码修改前准备
- 使用 `codebase-retrieval` 工具获取详细的代码上下文
- 询问所有相关符号、类、方法的具体实现细节
- 确保理解现有代码结构和依赖关系
- 使用 `str-replace-editor` 而非重写整个文件

### 包管理规范
- 始终使用包管理器安装依赖，禁止手动编辑配置文件
- JavaScript: 使用 `npm install/uninstall`、`yarn add/remove`
- Python: 使用 `pip install/uninstall`、`poetry add/remove`
- 避免版本冲突和依赖问题

## 🔍 代码审查检查清单

### 配置相关检查
- [ ] 是否有硬编码的配置值？
- [ ] 是否使用了统一配置服务？
- [ ] 配置是否在初始化时缓存？
- [ ] 配置键是否使用了标准命名？
- [ ] 是否有重复的配置定义？
- [ ] 是否实现了多层回退机制？

### 代码质量检查
- [ ] 是否有未使用的导入？
- [ ] 是否有重复的代码逻辑？
- [ ] 是否使用了通配符导入？
- [ ] 是否遵循了单一职责原则？
- [ ] 错误处理是否统一？
- [ ] 中文字符串是否都配置化了？

### 架构一致性检查
- [ ] 是否使用了统一的决策引擎？
- [ ] 组件创建是否标准化？
- [ ] 是否正确使用了依赖注入？
- [ ] 接口定义是否清晰？

### 协作技术检查
- [ ] 是否在修改前获取了足够的代码上下文？
- [ ] 是否使用了正确的编辑工具？
- [ ] 是否遵循了包管理规范？
- [ ] 是否保持了代码风格一致性？
