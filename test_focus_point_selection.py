#!/usr/bin/env python3
"""
测试关注点选择逻辑的修复效果
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

from backend.agents.conversation_flow.state_manager import StateManager
from backend.data.db.focus_point_manager import FocusPointManager
from backend.data.db.database_manager import DatabaseManager

async def test_focus_point_selection():
    """测试关注点选择逻辑"""
    
    # 初始化组件
    db_path = "backend/data/aidatabase.db"
    db_manager = DatabaseManager(db_path)
    focus_point_manager = FocusPointManager(db_manager)
    state_manager = StateManager(focus_point_manager, db_manager)
    
    # 测试数据
    session_id = "a22a956f-ec40-439b-b328-380648c7552e"
    user_id = "a22a956f-ec40-439b-b328-380648c7552e"
    
    # 模拟关注点定义（按照数据库中的实际数据）
    focus_points = [
        {"id": "GZD_016", "name": "要放主办方信息吗", "priority": "P2"},
        {"id": "GZD_017", "name": "什么时候要", "priority": "P1"},
        {"id": "GZD_018", "name": "预算多少", "priority": "P1"},
        {"id": "GZD_019", "name": "其他想法", "priority": "P2"}
    ]
    
    print("🔍 测试关注点选择逻辑")
    print("=" * 50)
    
    # 1. 加载当前状态
    print("1. 当前关注点状态:")
    statuses = await focus_point_manager.load_focus_points_status(session_id, user_id)
    for point in focus_points:
        point_id = point["id"]
        status_info = statuses.get(point_id, {})
        status = status_info.get("status", "pending")
        value = status_info.get("value", "")
        print(f"   {point_id} ({point['name']}, {point['priority']}): {status} - '{value}'")
    
    print()
    
    # 2. 测试选择逻辑
    print("2. 测试关注点选择:")
    selected_point = await state_manager.get_next_pending_point(session_id, user_id, focus_points)
    
    if selected_point:
        print(f"   ✅ 选择的关注点: {selected_point['id']} ({selected_point['name']}, {selected_point['priority']})")
        
        # 验证选择是否正确
        expected_id = "GZD_018"  # 应该选择GZD_018（预算多少，P1）
        if selected_point['id'] == expected_id:
            print(f"   🎉 选择正确！应该选择 {expected_id}")
        else:
            print(f"   ❌ 选择错误！应该选择 {expected_id}，但选择了 {selected_point['id']}")
    else:
        print("   ❌ 没有找到待处理的关注点")
    
    print()
    
    # 3. 验证排序逻辑
    print("3. 验证排序逻辑:")
    priority_map = {"P0": 0, "P1": 1, "P2": 2}
    sorted_points = sorted(focus_points, key=lambda x: (priority_map.get(x["priority"], 3), x["id"]))
    
    print("   排序后的关注点顺序:")
    for i, point in enumerate(sorted_points):
        status_info = statuses.get(point["id"], {})
        status = status_info.get("status", "pending")
        marker = "👉" if status == "pending" and point["priority"] in ["P0", "P1"] else "  "
        print(f"   {marker} {i+1}. {point['id']} ({point['name']}, {point['priority']}) - {status}")

if __name__ == "__main__":
    asyncio.run(test_focus_point_selection())
