# Python
## Byte-compiled / optimized / DLL files
__pycache__/
*.pyc
*.pyo
*.pyd

## C extensions
*.so

## Distribution / packaging
build/
dist/
*.egg-info/

# Virtual Environments
.env
.venv
env/
.venv_autogen/

# IDE & Editor Files
.idea/
.vscode/
*.swp
*.swo
*~
.DS_Store
*/.DS_Store

# Log files
*.log
logs/
logs_backup/

# Database files
*.db
*_backup_*.db
aidatabase_backup_*.db

# Generated Reports
bandit-report.json
hardcode_report.json
hardcode_report.txt
summary.md
coverage.xml

# Node.js / JavaScript
node_modules/

# Test files and coverage
.test/
.coverage
backend/tests/.coverage
htmlcov/
reports/

# Local directories and files
.history/
.roo/
.trae/
# Specific test files and backup configs
backend/tests/test_document_intent_recognition.py
backend/tests/README_conversation_flow_tests.md
backend/tests/run_conversation_flow_tests.py
backend/tests/test_conversation_flow_constants.py
backend/tests/test_conversation_flow_error_handling.py
backend/tests/test_conversation_flow_handler_fix.py
backend/tests/test_conversation_flow_state_management.py
backend/tests/test_conversation_flow.py
backend/tests/test_conversation_history_consistency.py
backup_old_configs/
# Archive documents (already in docs/archive/)
docs/archive/
# External documentation (not part of this project)
docs/由己帮助/

# Claude AI settings
.claude/
docs/unused_code_report.json
docs/dependency_analysis.json
test_composite_handler.py
test_composite_intent.py
test_focus_point_selection.py
test_intent_fix.py
test_intent_recognition.py
backend/utils/test_helper.py
