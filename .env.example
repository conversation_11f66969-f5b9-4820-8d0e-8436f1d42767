# 智能需求采集系统 - 环境变量配置示例
# 复制此文件为 .env 并填入实际的配置值

# =============================================================================
# API 服务配置
# =============================================================================

# API 服务端口
API_PORT=8000

# API 服务主机
API_HOST=0.0.0.0

# 调试模式 (development/production)
DEBUG=true

# 日志级别 (DEBUG/INFO/WARNING/ERROR)
LOG_LEVEL=INFO

# =============================================================================
# 数据库配置
# =============================================================================

# 数据库文件路径
DATABASE_PATH=backend/data/aidatabase.db

# =============================================================================
# AI 模型配置
# =============================================================================

# DeepSeek API 配置
DEEPSEEK_API_KEY=your-deepseek-api-key-here
DEEPSEEK_API_BASE=https://api.deepseek.com/v1

# 豆包 API 配置
DOUBAO_API_KEY=your-doubao-api-key-here
DOUBAO_BASE_URL=https://ark.cn-beijing.volces.com/api/v3

# 通义千问 API 配置
QWEN_API_KEY=your-qwen-api-key-here
QWEN_BASE_URL=https://dashscope.aliyuncs.com/api/v1

# OpenAI API配置
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_API_BASE=https://api.openai.com/v1

# Google (Gemini) API配置
GEMINI_API_KEY=your-gemini-api-key-here

# Anthropic API配置
ANTHROPIC_API_KEY=your-anthropic-api-key-here
ANTHROPIC_API_BASE=https://api.anthropic.com

# Azure OpenAI配置（如果使用）
AZURE_OPENAI_API_KEY=your-azure-openai-key-here
AZURE_OPENAI_API_BASE=your-azure-openai-endpoint-here
AZURE_OPENAI_API_VERSION=2023-05-15

# 默认使用的模型提供商 (deepseek/doubao/qwen/openai/gemini/anthropic)
DEFAULT_MODEL_PROVIDER=deepseek

# 模型超时时间 (秒)
MODEL_TIMEOUT=30

# 最大重试次数
MODEL_MAX_RETRIES=3

# =============================================================================
# 性能配置
# =============================================================================

# 三层识别系统配置
ENABLE_KEYWORD_ACCELERATION=true
ENABLE_SEMANTIC_MATCHING=true
ENABLE_INTENT_RECOGNITION=true

# 缓存配置
ENABLE_CACHE=true
CACHE_TTL=3600

# 并发配置
MAX_CONCURRENT_REQUESTS=100
REQUEST_TIMEOUT=60

# 高并发模式 (生产环境高负载时启用)
HIGH_CONCURRENCY_MODE=false

# =============================================================================
# 系统优化配置 (可选，用于性能调优和调试)
# =============================================================================

# 初始化跟踪 (用于检测重复初始化问题)
ENABLE_INIT_TRACKING=true

# 配置缓存 (提高配置加载性能)
ENABLE_CONFIG_CACHE=true

# 重复检测阈值 (超过此次数时发出警告)
DUPLICATE_WARNING_THRESHOLD=2

# 统计报告间隔 (每隔多少次访问报告一次)
STATS_REPORT_INTERVAL=100

# 优化日志级别 (DEBUG/INFO/WARNING/ERROR)
OPTIMIZATION_LOG_LEVEL=INFO

# =============================================================================
# 安全配置
# =============================================================================

# JWT 密钥 (请使用强密码)
JWT_SECRET_KEY=your_jwt_secret_key_here

# JWT 过期时间 (小时)
JWT_EXPIRE_HOURS=24

# CORS 允许的源
CORS_ORIGINS=http://localhost:5173,http://localhost:3000

# =============================================================================
# 监控配置
# =============================================================================

# 启用性能监控
ENABLE_MONITORING=true

# 监控数据保留天数
MONITORING_RETENTION_DAYS=30

# 启用错误追踪
ENABLE_ERROR_TRACKING=true

# =============================================================================
# 前端配置
# =============================================================================

# 前端服务端口
FRONTEND_PORT=5173

# API 基础 URL
VITE_API_BASE_URL=http://localhost:8000

# =============================================================================
# 开发配置
# =============================================================================

# 启用开发工具
ENABLE_DEV_TOOLS=true

# 启用详细日志
ENABLE_VERBOSE_LOGGING=false

# 启用 SQL 查询日志
ENABLE_SQL_LOGGING=false

# =============================================================================
# 部署配置
# =============================================================================

# 部署环境 (development/staging/production)
ENVIRONMENT=development

# 服务器配置
SERVER_NAME=localhost
SERVER_PORT=8000
