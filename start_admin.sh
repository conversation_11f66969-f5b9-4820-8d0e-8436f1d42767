#!/bin/bash
# 启动完整的后台管理系统（后端API + 前端界面）

set -euo pipefail

echo "🚀 启动智能需求采集系统 - 完整后台管理系统..."
echo "📍 项目路径: $(pwd)"
echo ""

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装"
    exit 1
fi

# 检查Node.js环境
if ! command -v npm &> /dev/null; then
    echo "❌ Node.js/npm 未安装"
    exit 1
fi

# 确保在项目根目录
cd "$(dirname "$0")"

# 检查并安装后端依赖
echo "📦 检查后端依赖..."
cd admin-backend

if [ ! -f "requirements_installed" ]; then
    echo "📦 安装后端依赖..."
    pip install -r requirements.txt
    if [ $? -eq 0 ]; then
        touch requirements_installed
        echo "✅ 后端依赖安装完成"
    else
        echo "❌ 后端依赖安装失败"
        exit 1
    fi
else
    echo "✅ 后端依赖已安装"
fi

# 检查并安装前端依赖
echo "📦 检查前端依赖..."
cd ../admin-frontend

if [ ! -d "node_modules" ]; then
    echo "📦 安装前端依赖..."
    npm install
    if [ $? -eq 0 ]; then
        echo "✅ 前端依赖安装完成"
    else
        echo "❌ 前端依赖安装失败"
        exit 1
    fi
else
    echo "✅ 前端依赖已安装"
fi

cd ../admin-backend

# 测试知识库服务
echo ""
echo "🧪 测试知识库管理服务..."
python3 test_knowledge_base.py
if [ $? -ne 0 ]; then
    echo "⚠️  知识库服务测试失败，但继续启动后台管理系统"
    echo "   请检查知识库配置和ChromaDB连接"
fi

# 启动后台管理系统
echo ""
echo "🎯 启动后台管理系统..."
echo "🔗 后端API地址: http://localhost:8002"
echo "📚 API文档地址: http://localhost:8002/docs"
echo "🖥️  前端管理界面: http://localhost:3000"
echo ""
echo "💡 提示：知识库管理功能已集成到前端管理界面中"
echo "💡 如需独立的知识库仪表板，请运行: ./start_knowledge_dashboard.sh"
echo ""

# 启动后端服务（后台运行）
echo "🚀 启动后端API服务..."
python3 main.py &
BACKEND_PID=$!
sleep 3

# 启动前端服务（前台运行）
echo "🚀 启动前端管理界面..."
cd ../admin-frontend
npm start

# 清理函数（当前端停止时自动清理后端）
cleanup() {
    echo ""
    echo "� 正在停止后端服务..."
    kill $BACKEND_PID 2>/dev/null
    echo "✅ 后台管理系统已停止"
    exit 0
}

# 捕获退出信号
trap cleanup SIGINT SIGTERM
