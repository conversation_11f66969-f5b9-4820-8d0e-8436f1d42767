# ============================================================================
# 智能需求采集系统 - 依赖包列表
# ============================================================================

# Web框架和API
fastapi==0.115.12
fastapi-limiter==0.1.6
uvicorn==0.34.2
starlette==0.46.2
python-multipart==0.0.20

# 异步支持
aiohttp==3.11.18
aiosqlite==0.21.0
anyio==4.9.0
asyncer==0.0.8

# 数据库
SQLAlchemy==2.0.40

# 缓存和存储
aioredis==2.0.1
redis==6.2.0
diskcache==5.6.3

# AI和机器学习
openai==1.78.0
google-generativeai==0.8.5
pyautogen==0.9.0
chromadb==0.4.24
sentence-transformers==2.2.2

# 数据处理和验证
pydantic==2.11.4
pydantic_core==2.33.2
PyYAML==6.0.2
python-dotenv==1.1.0

# HTTP客户端
httpx==0.28.1
requests==2.32.3

# JWT认证
PyJWT==2.9.0

# 开发工具
black==25.1.0
mypy==1.15.0
autopep8==2.3.2
ruff==0.11.9

# 测试
pytest==8.3.5
pytest-asyncio==0.26.0
pytest-cov==6.1.1

# 系统监控
psutil==7.0.0

# 其他支持库
aiohappyeyeballs==2.6.1
aiosignal==1.3.2
annotated-types==0.7.0
async-timeout==5.0.1
attrs==25.3.0
cachetools==5.5.2
certifi==2025.4.26
charset-normalizer==3.4.2
circuitbreaker==2.1.3
click==8.1.8
coverage==7.8.0
distro==1.9.0
docker==7.1.0
frozenlist==1.6.0
google-ai-generativelanguage==0.6.15
google-api-core==2.25.0rc1
google-api-python-client==2.169.0
google-auth==2.40.1
google-auth-httplib2==0.2.0
googleapis-common-protos==1.70.0
grpcio==1.71.0
grpcio-status==1.71.0
h11==0.16.0
httpcore==1.0.9
httplib2==0.22.0
idna==3.10
iniconfig==2.1.0
jiter==0.9.0
multidict==6.4.3
mypy_extensions==1.1.0
packaging==25.0
pathspec==0.12.1
platformdirs==4.3.8
pluggy==1.5.0
propcache==0.3.1
proto-plus==1.26.1
protobuf==5.29.4
pyasn1==0.6.1
pyasn1_modules==0.4.2
pycodestyle==2.13.0
pyparsing==3.2.3
regex==2024.11.6
rsa==4.9.1
sniffio==1.3.1
typing-inspection==0.4.0
typing_extensions==4.13.2
uritemplate==4.1.1
urllib3==2.4.0
wsproto==1.2.0
yarl==1.20.0
