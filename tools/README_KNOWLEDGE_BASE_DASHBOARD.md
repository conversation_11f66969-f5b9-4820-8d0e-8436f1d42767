# 知识库管理仪表板使用指南

## 概述
这是一个基于Streamlit的ChromaDB知识库管理界面，专门为您项目中的向量数据库设计。提供直观的Web界面来管理和查询知识库内容。

## 功能特点
- 📊 **概览统计**: 实时显示知识库文档数量、角色分布、分类统计
- 📖 **文档浏览**: 按角色、分类筛选，支持内容搜索
- 🔍 **查询测试**: 实时相似度查询，支持角色过滤和阈值调整
- 📈 **统计分析**: 内容长度分布、角色-分类交叉分析
- 🔧 **系统状态**: 实时监控ChromaDB连接和配置状态

## 安装和启动

### 启动方法
```bash
# 安装依赖
pip install streamlit pandas plotly chromadb

# 启动应用
streamlit run tools/knowledge_base_dashboard.py
```

### 方法3：Docker方式（可选）
```bash
# 创建Dockerfile（可选）
cat > Dockerfile << EOF
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 8501
CMD ["streamlit", "run", "tools/knowledge_base_dashboard.py", "--server.port=8501", "--server.address=0.0.0.0"]
EOF

# 构建和运行
docker build -t kb-dashboard .
docker run -p 8501:8501 kb-dashboard
```

## 使用说明

### 1. 概览页面
- 显示知识库总体统计信息
- 角色分布饼图
- 分类分布柱状图

### 2. 文档浏览
- **筛选功能**: 按角色、分类筛选文档
- **搜索功能**: 全文搜索文档内容
- **详细信息**: 展开查看完整文档内容和元数据

### 3. 查询测试
- **查询输入**: 输入任意查询文本
- **参数调整**: 
  - 返回结果数量（1-20）
  - 相似度阈值（0-1）
  - 角色过滤（全部/company/developer/general）
- **结果展示**: 显示相似度分数和相关文档片段

### 4. 统计分析
- **内容长度分布**: 直方图显示文档长度分布
- **交叉分析**: 角色-分类热力图
- **详细统计**: JSON格式显示关键指标

### 5. 系统状态
- **连接状态**: ChromaDB连接检查
- **配置信息**: 显示当前配置参数
- **健康检查**: 系统组件状态监控

## 配置说明

仪表板会自动读取项目配置：
- **数据库路径**: `backend/data/chroma_db`
- **集合名称**: `hybrid_knowledge_base`
- **嵌入模型**: `moka-ai/m3e-base`

如需修改配置，请编辑：
```
backend/config/knowledge_base_config.py
```

## 故障排除

### 常见问题

1. **连接失败**
   - 检查ChromaDB文件是否存在：`backend/data/chroma_db/`
   - 确认嵌入模型已下载：`moka-ai/m3e-base`

2. **依赖问题**
   ```bash
   pip install --upgrade streamlit chromadb pandas plotly
   ```

3. **端口冲突**
   - 默认端口：8501
   - 修改端口：`streamlit run tools/knowledge_base_dashboard.py --server.port=8502`

4. **权限问题**
   ```bash
   chmod +x tools/run_knowledge_base_dashboard.sh
   ```

## 高级功能

### 批量操作
可以通过修改代码添加：
- 批量删除文档
- 批量更新元数据
- 导出查询结果

### 自定义查询
在查询测试页面，可以：
- 使用高级查询语法
- 组合多个过滤条件
- 调整相似度算法参数

## 技术架构
- **前端**: Streamlit (Python)
- **数据库**: ChromaDB
- **可视化**: Plotly
- **数据处理**: Pandas

## 扩展建议
1. 添加文档上传功能
2. 集成文档预处理管道
3. 支持多种嵌入模型切换
4. 添加用户权限管理
5. 实现实时更新通知

## 联系支持
如有问题，请检查日志或联系开发团队。
