#!/usr/bin/env python3
"""
知识库管理功能测试脚本

用于测试知识库管理服务的基本功能
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from admin_services.knowledge_base_service import KnowledgeBaseService


async def test_knowledge_base_service():
    """测试知识库管理服务"""
    print("🧪 开始测试知识库管理服务...")
    
    try:
        # 初始化服务
        print("📦 初始化知识库服务...")
        service = KnowledgeBaseService()
        print("✅ 知识库服务初始化成功")
        
        # 测试系统状态
        print("\n🔍 测试系统状态...")
        status = await service.get_system_status()
        print(f"✅ 系统状态获取成功:")
        print(f"   - ChromaDB状态: {status['chromadb_status']}")
        print(f"   - 文档数量: {status['document_count']}")
        print(f"   - 组件状态: {status['components']}")
        
        # 测试概览统计
        print("\n📊 测试概览统计...")
        stats = await service.get_overview_stats()
        print(f"✅ 概览统计获取成功:")
        print(f"   - 总文档数: {stats['total_documents']}")
        print(f"   - 唯一文档数: {stats['unique_documents']}")
        print(f"   - 角色类型: {stats['role_types']}")
        print(f"   - 分类数量: {stats['categories']}")
        
        # 测试文档列表
        print("\n📄 测试文档列表...")
        docs = await service.get_documents(limit=5)
        print(f"✅ 文档列表获取成功:")
        print(f"   - 总数: {docs['total']}")
        print(f"   - 当前页文档数: {len(docs['documents'])}")
        
        # 测试筛选选项
        print("\n🔍 测试筛选选项...")
        filters = await service.get_available_filters()
        print(f"✅ 筛选选项获取成功:")
        print(f"   - 可用角色: {filters['roles']}")
        print(f"   - 可用分类: {filters['categories']}")
        
        # 测试知识库查询（如果有文档的话）
        if stats['total_documents'] > 0:
            print("\n🔍 测试知识库查询...")
            query_result = await service.query_knowledge_base(
                query_text="测试查询",
                top_k=3,
                similarity_threshold=0.5
            )
            print(f"✅ 知识库查询成功:")
            print(f"   - 查询内容: {query_result['query']}")
            print(f"   - 结果数量: {query_result['total_results']}")
        
        # 测试统计分析
        print("\n📈 测试统计分析...")
        statistics = await service.get_statistics()
        print(f"✅ 统计分析获取成功:")
        if 'total_documents' in statistics:
            print(f"   - 总文档数: {statistics['total_documents']}")
            if 'content_length_stats' in statistics:
                print(f"   - 平均长度: {statistics['content_length_stats']['mean']}")
        
        print("\n🎉 所有测试通过！知识库管理服务运行正常。")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    print("=" * 60)
    print("知识库管理服务测试")
    print("=" * 60)
    
    success = await test_knowledge_base_service()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 测试完成：知识库管理服务正常")
        sys.exit(0)
    else:
        print("❌ 测试失败：知识库管理服务异常")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
