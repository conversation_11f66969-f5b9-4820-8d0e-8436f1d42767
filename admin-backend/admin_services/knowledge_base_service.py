"""
知识库管理服务

提供知识库管理的核心业务逻辑，包括文档管理、查询测试、统计分析等功能。
遵循开发规范，使用统一配置服务和依赖注入。
"""

import logging
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import pandas as pd
import chromadb
from chromadb.utils import embedding_functions

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from backend.config.knowledge_base_config import get_knowledge_base_config_manager
from backend.config.unified_config_loader import get_unified_config
from admin_utils.exceptions import DatabaseError


class KnowledgeBaseService:
    """知识库管理服务"""
    
    def __init__(self):
        """初始化服务"""
        self.logger = logging.getLogger(__name__)
        self.unified_config = get_unified_config()
        self.config_manager = get_knowledge_base_config_manager()
        self.config = self.config_manager.get_config() if self.config_manager else None
        
        # ChromaDB相关
        self.chroma_client = None
        self.collection = None
        self.embedding_function = None
        
        # 初始化ChromaDB连接
        self._initialize_chromadb()
    
    def _initialize_chromadb(self):
        """初始化ChromaDB连接"""
        try:
            if not self.config:
                raise DatabaseError("知识库配置不可用")
            
            chroma_path = self.config.chroma_db.get('path', 'backend/data/chroma_db')
            collection_name = self.config.chroma_db.get('collection_name', 'hybrid_knowledge_base')
            embedding_model = self.config.chroma_db.get('embedding_model', 'moka-ai/m3e-base')

            # 修正路径：如果是相对路径，需要相对于项目根目录
            import os
            if not os.path.isabs(chroma_path):
                # 获取项目根目录（admin-backend的父目录）
                project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                chroma_path = os.path.join(project_root, chroma_path)

            # 创建客户端
            self.chroma_client = chromadb.PersistentClient(path=chroma_path)
            
            # 设置嵌入函数
            self.embedding_function = embedding_functions.SentenceTransformerEmbeddingFunction(
                model_name=embedding_model
            )
            
            # 尝试获取集合，如果不存在则创建
            try:
                self.collection = self.chroma_client.get_collection(
                    name=collection_name,
                    embedding_function=self.embedding_function
                )
                self.logger.info("成功连接到现有ChromaDB集合")
            except Exception as get_error:
                # 如果集合不存在，尝试创建
                try:
                    self.collection = self.chroma_client.create_collection(
                        name=collection_name,
                        embedding_function=self.embedding_function
                    )
                    self.logger.info("成功创建新的ChromaDB集合")
                except Exception as create_error:
                    self.logger.error(f"创建ChromaDB集合失败: {create_error}")
                    self.collection = None
                    raise DatabaseError(f"ChromaDB集合创建失败: {create_error}")

        except Exception as e:
            self.logger.error(f"连接ChromaDB失败: {e}")
            self.collection = None
            raise DatabaseError(f"ChromaDB连接失败: {e}")
    
    async def get_overview_stats(self) -> Dict[str, Any]:
        """获取知识库概览统计"""
        try:
            if not self.collection:
                raise DatabaseError("ChromaDB连接不可用")
            
            # 获取基本统计
            total_docs = self.collection.count()
            
            # 获取所有文档元数据
            all_results = self.collection.get(include=["metadatas"])
            
            stats = {
                "total_documents": total_docs,
                "unique_documents": 0,
                "role_types": 0,
                "categories": 0,
                "role_distribution": {},
                "category_distribution": {}
            }
            
            if all_results and all_results.get('metadatas'):
                df = pd.DataFrame(all_results['metadatas'])
                
                # 唯一文档数
                if 'doc_id' in df.columns:
                    stats["unique_documents"] = df['doc_id'].nunique()
                
                # 角色分布
                if 'role' in df.columns:
                    role_counts = df['role'].value_counts()
                    stats["role_types"] = len(role_counts)
                    stats["role_distribution"] = role_counts.to_dict()
                
                # 分类分布
                if 'category' in df.columns:
                    category_counts = df['category'].value_counts()
                    stats["categories"] = len(category_counts)
                    stats["category_distribution"] = category_counts.to_dict()
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取概览统计失败: {e}")
            raise DatabaseError(f"获取统计数据失败: {e}")
    
    async def get_documents(self, role: Optional[str] = None, category: Optional[str] = None, 
                           search_term: Optional[str] = None, page: int = 1, 
                           limit: int = 20) -> Dict[str, Any]:
        """获取文档列表"""
        try:
            if not self.collection:
                raise DatabaseError("ChromaDB连接不可用")
            
            # 获取所有文档
            all_results = self.collection.get(include=["documents", "metadatas"])
            
            if not all_results or not all_results.get('documents'):
                return {
                    "documents": [],
                    "total": 0,
                    "page": page,
                    "limit": limit,
                    "total_pages": 0
                }
            
            # 创建DataFrame进行筛选
            df = pd.DataFrame({
                'document': all_results['documents'],
                'metadata': all_results['metadatas'],
                'id': all_results['ids']
            })
            
            # 展开元数据
            metadata_df = pd.json_normalize(df['metadata'].tolist())
            df = pd.concat([df, metadata_df], axis=1)
            
            # 应用筛选条件
            if role and 'role' in df.columns:
                df = df[df['role'] == role]
            
            if category and 'category' in df.columns:
                df = df[df['category'] == category]
            
            if search_term:
                df = df[df['document'].str.contains(search_term, case=False, na=False)]
            
            # 分页
            total = len(df)
            total_pages = (total + limit - 1) // limit
            start_idx = (page - 1) * limit
            end_idx = start_idx + limit
            
            df_page = df.iloc[start_idx:end_idx]
            
            # 转换为返回格式
            documents = []
            for _, row in df_page.iterrows():
                doc = {
                    "id": row['id'],
                    "content": row['document'],
                    "doc_id": row.get('doc_id', 'N/A'),
                    "title": row.get('title', '未命名文档'),
                    "role": row.get('role', 'N/A'),
                    "category": row.get('category', 'N/A'),
                    "source_path": row.get('source_path', 'N/A'),
                    "chunk_index": row.get('chunk_index', 0),
                    "content_preview": row['document'][:200] + "..." if len(row['document']) > 200 else row['document']
                }
                documents.append(doc)
            
            return {
                "documents": documents,
                "total": total,
                "page": page,
                "limit": limit,
                "total_pages": total_pages
            }
            
        except Exception as e:
            self.logger.error(f"获取文档列表失败: {e}")
            raise DatabaseError(f"获取文档失败: {e}")
    
    async def query_knowledge_base(self, query_text: str, top_k: int = 5, 
                                  similarity_threshold: float = 0.7, 
                                  role_filter: Optional[str] = None) -> Dict[str, Any]:
        """查询知识库"""
        try:
            if not self.collection:
                raise DatabaseError("ChromaDB连接不可用")
            
            # 构建查询参数
            query_params = {
                "query_texts": [query_text],
                "n_results": top_k,
                "include": ["documents", "metadatas", "distances"]
            }
            
            # 添加角色过滤
            if role_filter and role_filter != "全部":
                query_params["where"] = {"role": {"$eq": role_filter}}
            
            # 执行查询
            results = self.collection.query(**query_params)
            
            # 处理结果
            query_results = []
            if results['documents'][0]:
                for i, (doc, metadata, distance) in enumerate(zip(
                    results['documents'][0],
                    results['metadatas'][0],
                    results['distances'][0]
                )):
                    # ChromaDB使用的是距离，距离越小相似度越高
                    # 对于余弦距离，相似度 = 1 - 距离
                    similarity_score = max(0, 1 - distance)

                    # 调试信息
                    self.logger.info(f"查询结果 {i+1}: 距离={distance}, 相似度={similarity_score}, 阈值={similarity_threshold}")

                    if similarity_score >= similarity_threshold:
                        result = {
                            "rank": i + 1,
                            "content": doc,
                            "content_preview": doc[:300] + "..." if len(doc) > 300 else doc,
                            "similarity_score": round(similarity_score, 3),
                            "metadata": {
                                "title": metadata.get('title', '未命名'),
                                "role": metadata.get('role', 'N/A'),
                                "category": metadata.get('category', 'N/A'),
                                "source_path": metadata.get('source_path', 'N/A'),
                                "doc_id": metadata.get('doc_id', 'N/A')
                            }
                        }
                        query_results.append(result)
                    else:
                        self.logger.info(f"结果 {i+1} 被过滤：相似度 {similarity_score} < 阈值 {similarity_threshold}")
            
            return {
                "query": query_text,
                "results": query_results,
                "total_results": len(query_results),
                "parameters": {
                    "top_k": top_k,
                    "similarity_threshold": similarity_threshold,
                    "role_filter": role_filter
                }
            }
            
        except Exception as e:
            self.logger.error(f"知识库查询失败: {e}")
            raise DatabaseError(f"查询失败: {e}")
    
    async def get_statistics(self) -> Dict[str, Any]:
        """获取统计分析数据"""
        try:
            if not self.collection:
                raise DatabaseError("ChromaDB连接不可用")
            
            # 获取所有数据
            all_results = self.collection.get(include=["metadatas", "documents"])
            
            if not all_results or not all_results.get('metadatas'):
                return {"message": "没有数据可供分析"}
            
            # 创建DataFrame
            df = pd.DataFrame(all_results['metadatas'])
            content_df = pd.DataFrame({'content': all_results['documents']})
            df = pd.concat([df, content_df], axis=1)
            
            # 内容长度分析
            df['content_length'] = df['content'].str.len()
            
            stats = {
                "total_documents": len(df),
                "content_length_stats": {
                    "mean": int(df['content_length'].mean()),
                    "max": int(df['content_length'].max()),
                    "min": int(df['content_length'].min()),
                    "std": int(df['content_length'].std())
                },
                "content_length_distribution": df['content_length'].describe().to_dict()
            }
            
            # 角色统计
            if 'role' in df.columns:
                role_stats = df['role'].value_counts()
                stats["role_statistics"] = role_stats.to_dict()
            
            # 分类统计
            if 'category' in df.columns:
                category_stats = df['category'].value_counts()
                stats["category_statistics"] = category_stats.to_dict()
            
            # 角色-分类交叉分析
            if 'role' in df.columns and 'category' in df.columns:
                pivot = pd.crosstab(df['role'], df['category'])
                stats["role_category_matrix"] = pivot.to_dict()
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取统计分析失败: {e}")
            raise DatabaseError(f"统计分析失败: {e}")
    
    async def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        try:
            status = {
                "chromadb_status": "disconnected",
                "collection_info": {},
                "config_status": {},
                "document_count": 0
            }
            
            # ChromaDB状态
            if self.collection:
                try:
                    document_count = self.collection.count()
                    status["chromadb_status"] = "connected"
                    status["document_count"] = document_count
                    status["collection_info"] = {
                        "name": self.collection.name,
                        "document_count": document_count
                    }
                except Exception as e:
                    status["chromadb_status"] = f"error: {e}"
            
            # 配置状态
            if self.config:
                status["config_status"] = {
                    "database_path": self.config.chroma_db.get('path', 'N/A'),
                    "collection_name": self.config.chroma_db.get('collection_name', 'N/A'),
                    "embedding_model": self.config.chroma_db.get('embedding_model', 'N/A'),
                    "knowledge_base_enabled": self.config_manager.is_knowledge_base_enabled() if self.config_manager else False
                }
            
            # 组件状态
            status["components"] = {
                "config_manager": self.config_manager is not None,
                "chroma_client": self.chroma_client is not None,
                "embedding_function": self.embedding_function is not None,
                "collection": self.collection is not None
            }
            
            return status
            
        except Exception as e:
            self.logger.error(f"获取系统状态失败: {e}")
            raise DatabaseError(f"获取系统状态失败: {e}")
    
    async def get_available_filters(self) -> Dict[str, List[str]]:
        """获取可用的筛选选项"""
        try:
            if not self.collection:
                raise DatabaseError("ChromaDB连接不可用")
            
            all_results = self.collection.get(include=["metadatas"])
            
            filters = {
                "roles": [],
                "categories": []
            }
            
            if all_results and all_results.get('metadatas'):
                df = pd.DataFrame(all_results['metadatas'])
                
                if 'role' in df.columns:
                    filters["roles"] = df['role'].unique().tolist()
                
                if 'category' in df.columns:
                    filters["categories"] = df['category'].unique().tolist()
            
            return filters
            
        except Exception as e:
            self.logger.error(f"获取筛选选项失败: {e}")
            raise DatabaseError(f"获取筛选选项失败: {e}")
