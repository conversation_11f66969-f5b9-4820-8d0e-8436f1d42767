f   c   t       =       C   r   o   s   s   E   n   t   r   o   p   y   L   o   s   s   (   )   
                                                   l   o   s   s       =       l   o   s   s   _   f   c   t   (   l   o   g   i   t   s   .   v   i   e   w   (   -   1   ,       s   e   l   f   .   c   o   n   f   i   g   .   n   u   m   _   l   a   b   e   l   s   )   ,       l   a   b   e   l   s   .   v   i   e   w   (   -   1   )   )   
   
                                   i   f       n   o   t       r   e   t   u   r   n   _   d   i   c   t   :   
                                                   o   u   t   p   u   t       =       (   l   o   g   i   t   s   ,   )       +       o   u   t   p   u   t   s   [   _   H   I   D   D   E   N   _   S   T   A   T   E   S   _   S   T   A   R   T   _   P   O   S   I   T   I   O   N   :   ]   
                                                   r   e   t   u   r   n       (   (   l   o   s   s   ,   )       +       o   u   t   p   u   t   )       i   f       l   o   s   s       i   s       n   o   t       N   o   n   e       e   l   s   e       o   u   t   p   u   t   
   
                                   r   e   t   u   r   n       S   e   q   u   e   n   c   e   C   l   a   s   s   i   f   i   e   r   O   u   t   p   u   t   (   
                                                   l   o   s   s   =   l   o   s   s   ,   
                                                   l   o   g   i   t   s   =   l   o   g   i   t   s   ,   
                                                   h   i   d   d   e   n   _   s   t   a   t   e   s   =   o   u   t   p   u   t   s   .   h   i   d   d   e   n   _   s   t   a   t   e   s   ,   
                                                   a   t   t   e   n   t   i   o   n   s   =   o   u   t   p   u   t   s   .   a   t   t   e   n   t   i   o   n   s   ,   
                                   )   
   
   
   @   a   u   t   o   _   d   o   c   s   t   r   i   n   g   
   c   l   a   s   s       U   n   i   S   p   e   e   c   h   S   a   t   F   o   r   A   u   d   i   o   F   r   a   m   e   C   l   a   s   s   i   f   i   c   a   t   i   o   n   (   U   n   i   S   p   e   e   c   h   S   a   t   P   r   e   T   r   a   i   n   e   d   M   o   d   e   l   )   :   
                   d   e   f       _   _   i   n   i   t   _   _   (   s   e   l   f   ,       c   o   n   f   i   g   )   :   
                                   s   u   p   e   r   (   )   .   _   _   i   n   i   t   _   _   (   c   o   n   f   i   g   )   
   
                                   i   f       h   a   s   a   t   t   r   (   c   o   n   f   i   g   ,       "   a   d   d   _   a   d   a   p   t   e   r   "   )       a   n   d       c   o   n   f   i   g   .   a   d   d   _   a   d   a   p   t   e   r   :   
                                                   r   a   i   s   e       V   a   l   u   e   E   r   r   o   r   (   
                                                                   "   A   u   d   i   o       f   r   a   m   e       c   l   a   s   s   i   f   i   c   a   t   i   o   n       d   o   e   s       n   o   t       s   u   p   p   o   r   t       t   h   e       u   s   e       o   f       U   n   i   S   p   e   e   c   h   S   a   t       a   d   a   p   t   e   r   s       (   c   o   n   f   i   g   .   a   d   d   _   a   d   a   p   t   e   r   =   T   r   u   e   )   "   
                                                   )   
                                   s   e   l   f   .   u   n   i   s   p   e   e   c   h   _   s   a   t       =       U   n   i   S   p   e   e   c   h   S   a   t   M   o   d   e   l   (   c   o   n   f   i   g   )   
                                   n   u   m   _   l   a   y   e   r   s       =       c   o   n   f   i   g   .   n   u   m   _   h   i   d   d   e   n   _   l   a   y   e   r   s       +       1           #       t   r   a   n   s   f   o   r   m   e   r       l   a   y   e   r   s       +       i   n   p   u   t       e   m   b   e   d   d   i   n   g   s   
                                   i   f       c   o   n   f   i   g   .   u   s   e   _   w   e   i   g   h   t   e   d   _   l   a   y   e   r   _   s   u   m   :   
                                                   s   e   l   f   .   l   a   y   e   r   _   w   e   i   g   h   t   s       =       n   n   .   P   a   r   a   m   e   t   e   r   (   t   o   r   c   h   .   o   n   e   s   (   n   u   m   _   l   a   y   e   r   s   )       /       n   u   m   _   l   a   y   e   r   s   )   
                                   s   e   l   f   .   c   l   a   s   s   i   f   i   e   r       =       n   n   .   L   i   n   e   a   r   (   c   o   n   f   i   g   .   h   i   d   d   e   n   _   s   i   z   e   ,       c   o   n   f   i   g   .   n   u   m   _   l   a   b   e   l   s   )   
                                   s   e   l   f   .   n   u   m   _   l   a   b   e   l   s       =       c   o   n   f   i   g   .   n   u   m   _   l   a   b   e   l   s   
   
                                   s   e   l   f   .   i   n   i   t   _   w   e   i   g   h   t   s   (   )   
   
                   d   e   f       f   r   e   e   z   e   _   f   e   a   t   u   r   e   _   e   x   t   r   a   c   t   o   r   (   s   e   l   f   )   :   
                                   "   "   "   
                                   C   a   l   l   i   n   g       t   h   i   s       f   u   n   c   t   i   o   n       w   i   l   l       d   i   s   a   b   l   e       t   h   e       g   r   a   d   i   e   n   t       c   o   m   p   u   t   a   t   i   o   n       f   o   r       t   h   e       f   e   a   t   u   r   e       e   n   c   o   d   e   r       s   o       t   h   a   t       i   t   s       p   a   r   a   m   e   t   e   r       w   i   l   l   
                                   n   o   t       b   e       u   p   d   a   t   e   d       d   u   r   i   n   g       t   r   a   i   n   i   n   g   .   
                                   "   "   "   
                                   w   a   r   n   i   n   g   s   .   w   a   r   n   (   
                                                   "   T   h   e       m   e   t   h   o   d       `   f   r   e   e   z   e   _   f   e   a   t   u   r   e   _   e   x   t   r   a   c   t   o   r   `       i   s       d   e   p   r   e   c   a   t   e   d       a   n   d       w   i   l   l       b   e       r   e   m   o   v   e   d       i   n       T   r   a   n   s   f   o   r   m   e   r   s       v   5   .       "   
                                                   "   P   l   e   a   s   e       u   s   e       t   h   e       e   q   u   i   v   a   l   e   n   t       `   f   r   e   e   z   e   _   f   e   a   t   u   r   e   _   e   n   c   o   d   e   r   `       m   e   t   h   o   d       i   n   s   t   e   a   d   .   "   ,   
                                                   F   u   t   u   r   e   W   a   r   n   i   n   g   ,   
                                   )   
                                   s   e   l   f   .   f   r   e   e   z   e   _   f   e   a   t   u   r   e   _   e   n   c   o   d   e   r   (   )   
   
                   d   e   f       f   r   e   e   z   e   _   f   e   a   t   u   r   e   _   e   n   c   o   d   e   r   (   s   e   l   f   )   :   
                                   "   "   "   
                                   C   a   l   l   i   n   g       t   h   i   s       f   u   n   c   t   i   o   n       w   i   l   l       d   i   s   a   b   l   e       t   h   e       g   r   a   d   i   e   n   t       c   o   m   p   u   t   a   t   i   o   n       f   o   r       t   h   e       f   e   a   t   u   r   e       e   n   c   o   d   e   r       s   o       t   h   a   t       i   t   s       p   a   r   a   m   e   t   e   r       w   i   l   l   
                                   n   o   t       b   e       u   p   d   a   t   e   d       d   u   r   i   n   g       t   r   a   i   n   i   n   g   .   
                                   "   "   "   
                                   s   e   l   f   .   u   n   i   s   p   e   e   c   h   _   s   a   t   .   f   e   a   t   u   r   e   _   e   x   t   r   a   c   t   o   r   .   _   f   r   e   e   z   e   _   p   a   r   a   m   e   t   e   r   s   (   )   
   
                   d   e   f       f   r   e   e   z   e   _   b   a   s   e   _   m   o   d   e   l   (   s   e   l   f   )   :   
                                   "   "   "   
                                   C   a   l   l   i   n   g       t   h   i   s       f   u   n   c   t   i   o   n       w   i   l   l       d   i   s   a   b   l   e       t   h   e       g   r   a   d   i   e   n   t       c   o   m   p   u   t   a   t   i   o   n       f   o   r       t   h   e       b   a   s   e       m   o   d   e   l       s   o       t   h   a   t       i   t   s       p   a   r   a   m   e   t   e   r   s       w   i   l   l       n   o   t   
                                   b   e       u   p   d   a   t   e   d       d   u   r   i   n   g       t   r   a   i   n   i   n   g   .       O   n   l   y       t   h   e       c   l   a   s   s   i   f   i   c   a   t   i   o   n       h   e   a   d       w   i   l   l       b   e       u   p   d   a   t   e   d   .   
                                   "   "   "   
                                   f   o   r       p   a   r   a   m       i   n       s   e   l   f   .   u   n   i   s   p   e   e   c   h   _   s   a   t   .   p   a   r   a   m   e   t   e   r   s   (   )   :   
                                                   p   a   r   a   m   .   r   e   q   u   i   r   e   s   _   g   r   a   d       =       F   a   l   s   e   
   
                   @   a   u   t   o   _   d   o   c   s   t   r   i   n   g   
                   d   e   f       f   o   r   w   a   r   d   (   
                                   s   e   l   f   ,   
                                   i   n   p   u   t   _   v   a   l   u   e   s   :       O   p   t   i   o   n   a   l   [   t   o   r   c   h   .   T   e   n   s   o   r   ]   ,   
                                   a   t   t   e   n   t   i   o   n   _   m   a   s   k   :       O   p   t   i   o   n   a   l   [   t   o   r   c   h   .   T   e   n   s   o   r   ]       =       N   o   n   e   ,   
                                   l   a   b   e   l   s   :       O   p   t   i   o   n   a   l   [   t   o   r   c   h   .   T   e   n   s   o   r   ]       =       N   o   n   e   ,   
                                   o   u   t   p   u   t   _   a   t   t   e   n   t   i   o   n   s   :       O   p   t   i   o   n   a   l   [   b   o   o   l   ]       =       N   o   n   e   ,   
                                   o   u   t   p   u   t   _   h   i   d   d   e   n   _   s   t   a   t   e   s   :       O   p   t   i   o   n   a   l   [   b   o   o   l   ]       =       N   o   n   e   ,   
                                   r   e   t   u   r   n   _   d   i   c   t   :       O   p   t   i   o   n   a   l   [   b   o   o   l   ]       =       N   o   n   e   ,   
                   )       -   >       U   n   i   o   n   [   t   u   p   l   e   ,       T   o   k   e   n   C   l   a   s   s   i   f   i   e   r   O   u   t   p   u   t   ]   :   
                                   r   "   "   "   
                                   i   n   p   u   t   _   v   a   l   u   e   s       (   `   t   o   r   c   h   .   F   l   o   a   t   T   e   n   s   o   r   `       o   f       s   h   a   p   e       `   (   b   a   t   c   h   _   s   i   z   e   ,       s   e   q   u   e   n   c   e   _   l   e   n   g   t   h   )   `   )   :   
                                                   F   l   o   a   t       v   a   l   u   e   s       o   f       i   n   p   u   t       r   a   w       s   p   e   e   c   h       w   a   v   e   f   o   r   m   .       V   a   l   u   e   s       c   a   n       b   e       o   b   t   a   i   n   e   d       b   y       l   o   a   d   i   n   g       a       `   .   f   l   a   c   `       o   r       `   .   w   a   v   `       a   u   d   i   o       f   i   l   e   
                                                   i   n   t   o       a   n       a   r   r   a   y       o   f       t   y   p   e       `   l   i   s   t   [   f   l   o   a   t   ]   `       o   r       a       `   n   u   m   p   y   .   n   d   a   r   r   a   y   `   ,       *   e   .   g   .   *       v   i   a       t   h   e       s   o   u   n   d   f   i   l   e       l   i   b   r   a   r   y       (   `   p   i   p       i   n   s   t   a   l   l   
                                                   s   o   u   n   d   f   i   l   e   `   )   .       T   o       p   r   e   p   a   r   e       t   h   e       a   r   r   a   y       i   n   t   o       `   i   n   p   u   t   _   v   a   l   u   e   s   `   ,       t   h   e       [   `   A   u   t   o   P   r   o   c   e   s   s   o   r   `   ]       s   h   o   u   l   d       b   e       u   s   e   d       f   o   r       p   a   d   d   i   n   g       a   n   d   
                                                   c   o   n   v   e   r   s   i   o   n       i   n   t   o       a       t   e   n   s   o   r       o   f       t   y   p   e       `   t   o   r   c   h   .   F   l   o   a   t   T   e   n   s   o   r   `   .       S   e   e       [   `   U   n   i   S   p   e   e   c   h   S   a   t   P   r   o   c   e   s   s   o   r   .   _   _   c   a   l   l   _   _   `   ]       f   o   r       d   e   t   a   i   l   s   .   
                                   l   a   b   e   l   s       (   `   t   o   r   c   h   .   L   o   n   g   T   e   n   s   o   r   `       o   f       s   h   a   p   e       `   (   b   a   t   c   h   _   s   i   z   e   ,   )   `   ,       *   o   p   t   i   o   n   a   l   *   )   :   
                                                   L   a   b   e   l   s       f   o   r       c   o   m   p   u   t   i   n   g       t   h   e       s   e   q   u   e   n   c   e       c   l   a   s   s   i   f   i   c   a   t   i   o   n   /   r   e   g   r   e   s   s   i   o   n       l   o   s   s   .       I   n   d   i   c   e   s       s   h   o   u   l   d       b   e       i   n       `   [   0   ,       .   .   .   ,   
                                                   c   o   n   f   i   g   .   n   u   m   _   l   a   b   e   l   s       -       1   ]   `   .       I   f       `   c   o   n   f   i   g   .   n   u   m   _   l   a   b   e   l   s       =   =       1   `       a       r   e   g   r   e   s   s   i   o   n       l   o   s   s       i   s       c   o   m   p   u   t   e   d       (   M   e   a   n   -   S   q   u   a   r   e       l   o   s   s   )   ,       I   f   
                                                   `   c   o   n   f   i   g   .   n   u   m   _   l   a   b   e   l   s       >       1   `       a       c   l   a   s   s   i   f   i   c   a   t   i   o   n       l   o   s   s       i   s       c   o   m   p   u   t   e   d       (   C   r   o   s   s   -   E   n   t   r   o   p   y   )   .   
                                   "   "   "   
   
                                   r   e   t   u   r   n   _   d   i   c   t       =       r   e   t   u   r   n   _   d   i   c   t       i   f       r   e   t   u   r   n   _   d   i   c   t       i   s       n   o   t       N   o   n   e       e   l   s   e       s   e   l   f   .   c   o   n   f   i   g   .   u   s   e   _   r   e   t   u   r   n   _   d   i   c   t   
                                   o   u   t   p   u   t   _   h   i   d   d   e   n   _   s   t   a   t   e   s       =       T   r   u   e       i   f       s   e   l   f   .   c   o   n   f   i   g   .   u   s   e   _   w   e   i   g   h   t   e   d   _   l   a   y   e   r   _   s   u   m       e   l   s   e       o   u   t   p   u   t   _   h   i   d   d   e   n   _   s   t   a   t   e   s   
   
                                   o   u   t   p   u   t   s       =       s   e   l   f   .   u   n   i   s   p   e   e   c   h   _   s   a   t   (   
                                                   i   n   p   u   t   _   v   a   l   u   e   s   ,   
                                                   a   t   t   e   n   t   i   o   n   _   m   a   s   k   =   a   t   t   e   n   t   i   o   n   _   m   a   s   k   ,   
                                                   o   u   t   p   u   t   _   a   t   t   e   n   t   i   o   n   s   =   o   u   t   p   u   t   _   a   t   t   e   n   t   i   o   n   s   ,   
                                                   o   u   t   p   u   t   _   h   i   d   d   e   n   _   s   t   a   t   e   s   =   o   u   t   p   u   t   _   h   i   d   d   e   n   _   s   t   a   t   e   s   ,   
                                                   r   e   t   u   r   n   _   d   i   c   t   =   r   e   t   u   r   n   _   d   i   c   t   ,   
                                   )   
   
                                   i   f       s   e   l   f   .   c   o   n   f   i   g   .   u   s   e   _   w   e   i   g   h   t   e   d   _   l   a   y   e   r   _   s   u   m   :   
                                                   h   i   d   d   e   n   _   s   t   a   t   e   s       =       o   u   t   p   u   t   s   [   _   H   I   D   D   E   N   _   S   T   A   T   E   S   _   S   T   A   R   T   _   P   O   S   I   T   I   O   N   ]   
                                                   h   i   d   d   e   n   _   s   t   a   t   e   s       =       t   o   r   c   h   .   s   t   a   c   k   (   h   i   d   d   e   n   _   s   t   a   t   e   s   ,       d   i   m   =   1   )   
                                                   n   o   r   m   _   w   e   i   g   h   t   s       =       n   n   .   f   u   n   c   t   i   o   n   a   l   .   s   o   f   t   m   a   x   (   s   e   l   f   .   l   a   y   e   r   _   w   e   i   g   h   t   s   ,       d   i   m   =   -   1   )   
                                                   h   i   d   d   e   n   _   s   t   a   t   e   s       =       (   h   i   d   d   e   n   _   s   t   a   t   e   s       *       n   o   r   m   _   w   e   i   g   h   t   s   .   v   i   e   w   (   -   1   ,       1   ,       1   )   )   .   s   u   m   (   d   i   m   =   1   )   
                                   e   l   s   e   :   
                                                   h   i   d   d   e   n   _   s   t   a   t   e   s       =       o   u   t   p   u   t   s   [   0   ]   
   
                                   l   o   g   i   t   s       =       s   e   l   f   .   c   l   a   s   s   i   f   i   e   r   (   h   i   d   d   e   n   _   s   t   a   t   e   s   )   
   
                                   l   o   s   s       =       N   o   n   e   
                                   i   f       l   a   b   e   l   s       i   s       n   o   t       N   o   n   e   :   
                                                   l   o   s   s   _   f   c   t       =       C   r   o   s   s   E   n   t   r   o   p   y   L   o   s   s   (   )   
                                                   l   o   s   s       =       l   o   s   s   _   f   c   t   (   l   o   g   i   t   s   .   v   i   e   w   (   -   1   ,       s   e   l   f   .   n   u   m   _   l   a   b   e   l   s   )   ,       t   o   r   c   h   .   a   r   g   m   a   x   (   l   a   b   e   l   s   .   v   i   e   w   (   -   1   ,       s   e   l   f   .   n   u   m   _   l   a   b   e   l   s   )   ,       a   x   i   s   =   1   )   )   
   
                                   i   f       n   o   t       r   e   t   u   r   n   _   d   i   c   t   :   
                                                   o   u   t   p   u   t       =       (   l   o   g   i   t   s   ,   )       +       o   u   t   p   u   t   s   [   _   H   I   D   D   E   N   _   S   T   A   T   E   S   _   S   T   A   R   T   _   P   O   S   I   T   I   O   N   :   ]   
                                                   r   e   t   u   r   n       o   u   t   p   u   t   
   
                                   r   e   t   u   r   n       T   o   k   e   n   C   l   a   s   s   i   f   i   e   r   O   u   t   p   u   t   (   
                                                   l   o   s   s   =   l   o   s   s   ,   
                                                   l   o   g   i   t   s   =   l   o   g   i   t   s   ,   
                                                   h   i   d   d   e   n   _   s   t   a   t   e   s   =   o   u   t   p   u   t   s   .   h   i   d   d   e   n   _   s   t   a   t   e   s   ,   
                                                   a   t   t   e   n   t   i   o   n   s   =   o   u   t   p   u   t   s   .   a   t   t   e   n   t   i   o   n   s   ,   
                                   )   
   
   
   c   l   a   s   s       A   M   S   o   f   t   m   a   x   L   o   s   s   (   n   n   .   M   o   d   u   l   e   )   :   
                   d   e   f       _   _   i   n   i   t   _   _   (   s   e   l   f   ,       i   n   p   u   t   _   d   i   m   ,       n   u   m   _   l   a   b   e   l   s   ,       s   c   a   l   e   =   3   0   .   0   ,       m   a   r   g   i   n   =   0   .   4   )   :   
                                   s   u   p   e   r   (   )   .   _   _   i   n   i   t   _   _   (   )   
                                   s   e   l   f   .   s   c   a   l   e       =       s   c   a   l   e   
                                   s   e   l   f   .   m   a   r   g   i   n       =       m   a   r   g   i   n   
                                   s   e   l   f   .   n   u   m   _   l   a   b   e   l   s       =       n   u   m   _   l   a   b   e   l   s   
                                   s   e   l   f   .   w   e   i   g   h   t       =       n   n   .   P   a   r   a   m   e   t   e   r   (   t   o   r   c   h   .   r   a   n   d   n   (   i   n   p   u   t   _   d   i   m   ,       n   u   m   _   l   a   b   e   l   s   )   ,       r   e   q   u   i   r   e   s   _   g   r   a   d   =   T   r   u   e   )   
                                   s   e   l   f   .   l   o   s   s       =       n   n   .   C   r   o   s   s   E   n   t   r   o   p   y   L   o   s   s   (   )   
   
                   d   e   f       f   o   r   w   a   r   d   (   s   e   l   f   ,       h   i   d   d   e   n   _   s   t   a   t   e   s   ,       l   a   b   e   l   s   )   :   
                                   l   a   b   e   l   s       =       l   a   b   e   l   s   .   f   l   a   t   t   e   n   (   )   
                                   w   e   i   g   h   t       =       n   n   .   f   u   n   c   t   i   o   n   a   l   .   n   o   r   m   a   l   i   z   e   (   s   e   l   f   .   w   e   i   g   h   t   ,       d   i   m   =   0   )   
                                   h   i   d   d   e   n   _   s   t   a   t   e   s       =       n   n   .   f   u   n   c   t   i   o   n   a   l   .   n   o   r   m   a   l   i   z   e   (   h   i   d   d   e   n   _   s   t   a   t   e   s   ,       d   i   m   =   1   )   
                                   c   o   s   _   t   h   e   t   a       =       t   o   r   c   h   .   m   m   (   h   i   d   d   e   n   _   s   t   a   t   e   s   ,       w   e   i   g   h   t   )   
                                   p   s   i       =       c   o   s   _   t   h   e   t   a       -       s   e   l   f   .   m   a   r   g   i   n   
   
                                   o   n   e   h   o   t       =       n   n   .   f   u   n   c   t   i   o   n   a   l   .   o   n   e   _   h   o   t   (   l   a   b   e   l   s   ,       s   e   l   f   .   n   u   m   _   l   a   b   e   l   s   )   
                                   l   o   g   i   t   s       =       s   e   l   f   .   s   c   a   l   e       *       t   o   r   c   h   .   w   h   e   r   e   (   o   n   e   h   o   t   .   b   o   o   l   (   )   ,       p   s   i   ,       c   o   s   _   t   h   e   t   a   )   
                                   l   o   s   s       =       s   e   l   f   .   l   o   s   s   (   l   o   g   i   t   s   ,       l   a   b   e   l   s   )   
   
                                   r   e   t   u   r   n       l   o   s   s   
   
   
   c   l   a   s   s       T   D   N   N   L   a   y   e   r   (   n   n   .   M   o   d   u   l   e   )   :   
                   d   e   f       _   _   i   n   i   t   _   _   (   s   e   l   f   ,       c   o   n   f   i   g   ,       l   a   y   e   r   _   i   d   =   0   )   :   
                                   s   u   p   e   r   (   )   .   _   _   i   n   i   t   _   _   (   )   
                                   s   e   l   f   .   i   n   _   c   o   n   v   _   d   i   m       =       c   o   n   f   i   g   .   t   d   n   n   _   d   i   m   [   l   a   y   e   r   _   i   d       -       1   ]       i   f       l   a   y   e   r   _   i   d       >       0       e   l   s   e       c   o   n   f   i   g   .   t   d   n   n   _   d   i   m   [   l   a   y   e   r   _   i   d   ]   
                                   s   e   l   f   .   o   u   t   _   c   o   n   v   _   d   i   m       =       c   o   n   f   i   g   .   t   d   n   n   _   d   i   m   [   l   a   y   e   r   _   i   d   ]   
                                   s   e   l   f   .   k   e   r   n   e   l   _   s   i   z   e       =       c   o   n   f   i   g   .   t   d   n   n   _   k   e   r   n   e   l   [   l   a   y   e   r   _   i   d   ]   
                                   s   e   l   f   .   d   i   l   a   t   i   o   n       =       c   o   n   f   i   g   .   t   d   n   n   _   d   i   l   a   t   i   o   n   [   l   a   y   e   r   _   i   d   ]   
   
                                   s   e   l   f   .   k   e   r   n   e   l       =       n   n   .   L   i   n   e   a   r   (   s   e   l   f   .   i   n   _   c   o   n   v   _   d   i   m       *       s   e   l   f   .   k   e   r   n   e   l   _   s   i   z   e   ,       s   e   l   f   .   o   u   t   _   c   o   n   v   _   d   i   m   )   
                                   s   e   l   f   .   a   c   t   i   v   a   t   i   o   n       =       n   n   .   R   e   L   U   (   )   
   
                   d   e   f       f   o   r   w   a   r   d   (   s   e   l   f   ,       h   i   d   d   e   n   _   s   t   a   t   e   s   :       t   o   r   c   h   .   T   e   n   s   o   r   )       -   >       t   o   r   c   h   .   T   e   n   s   o   r   :   
                                   i   f       i   s   _   p   e   f   t   _   a   v   a   i   l   a   b   l   e   (   )   :   
                                                   f   r   o   m       p   e   f   t   .   t   u   n   e   r   s   .   l   o   r   a       i   m   p   o   r   t       L   o   r   a   L   a   y   e   r   
   
                                   i   f       i   s   _   p   e   f   t   _   a   v   a   i   l   a   b   l   e   (   )   :   
                                                   i   f       i   s   i   n   s   t   a   n   c   e   (   s   e   l   f   .   k   e   r   n   e   l   ,       L   o   r   a   L   a   y   e   r   )   :   
                                                                   w   a   r   n   i   n   g   s   .   w   a   r   n   (   
                                                                                   "   D   e   t   e   c   t   e   d       L   o   R   A       o   n       T   D   N   N   L   a   y   e   r   .       L   o   R   A       w   e   i   g   h   t   s       w   o   n   '   t       b   e       a   p   p   l   i   e   d       d   u   e       t   o       o   p   t   i   m   i   z   a   t   i   o   n   .       "   
                                                                                   "   Y   o   u       s   h   o   u   l   d       e   x   c   l   u   d   e       T   D   N   N   L   a   y   e   r       f   r   o   m       L   o   R   A   '   s       t   a   r   g   e   t       m   o   d   u   l   e   s   .   "   ,   
                                                                   )   
   
                                   #       f   o   r       b   a   c   k   w   a   r   d       c   o   m   p   a   t   i   b   i   l   i   t   y   ,       w   e       k   e   e   p       n   n   .   L   i   n   e   a   r       b   u   t       c   a   l   l       F   .   c   o   n   v   1   d       f   o   r       s   p   e   e   d       u   p   
                                   h   i   d   d   e   n   _   s   t   a   t   e   s       =       h   i   d   d   e   n   _   s   t   a   t   e   s   .   t   r   a   n   s   p   o   s   e   (   1   ,       2   )   
                                   w   e   i   g   h   t       =       s   e   l   f   .   k   e   r   n   e   l   .   w   e   i   g   h   t   .   v   i   e   w   (   s   e   l   f   .   o   u   t   _   c   o   n   v   _   d   i   m   ,       s   e   l   f   .   k   e   r   n   e   l   _   s   i   z   e   ,       s   e   l   f   .   i   n   _   c   o   n   v   _   d   i   m   )   .   t   r   a   n   s   p   o   s   e   (   1   ,       2   )   
                                   h   i   d   d   e   n   _   s   t   a   t   e   s       =       n   n   .   f   u   n   c   t   i   o   n   a   l   .   c   o   n   v   1   d   (   h   i   d   d   e   n   _   s   t   a   t   e   s   ,       w   e   i   g   h   t   ,       s   e   l   f   .   k   e   r   n   e   l   .   b   i   a   s   ,       d   i   l   a   t   i   o   n   =   s   e   l   f   .   d   i   l   a   t   i   o   n   )   
                                   h   i   d   d   e   n   _   s   t   a   t   e   s       =       h   i   d   d   e   n   _   s   t   a   t   e   s   .   t   r   a   n   s   p   o   s   e   (   1   ,       2   )   
   
                                   h   i   d   d   e   n   _   s   t   a   t   e   s       =       s   e   l   f   .   a   c   t   i   v   a   t   i   o   n   (   h   i   d   d   e   n   _   s   t   a   t   e   s   )   
                                   r   e   t   u   r   n       h   i   d   d   e   n   _   s   t   a   t   e   s   
   
   
   @   a   u   t   o   _   d   o   c   s   t   r   i   n   g   (   
                   c   u   s   t   o   m   _   i   n   t   r   o   =   "   "   "   
                   U   n   i   S   p   e   e   c   h   S   a   t       M   o   d   e   l       w   i   t   h       a   n       X   V   e   c   t   o   r       f   e   a   t   u   r   e       e   x   t   r   a   c   t   i   o   n       h   e   a   d       o   n       t   o   p       f   o   r       t   a   s   k   s       l   i   k   e       S   p   e   a   k   e   r       V   e   r   i   f   i   c   a   t   i   o   n   .   
                   "   "   "   
   )   
   c   l   a   s   s       U   n   i   S   p   e   e   c   h   S   a   t   F   o   r   X   V   e   c   t   o   r   (   U   n   i   S   p   e   e   c   h   S   a   t   P   r   e   T   r   a   i   n   e   d   M   o   d   e   l   )   :   
                   d   e   f       _   _   i   n   i   t   _   _   (   s   e   l   f   ,       c   o   n   f   i   g   )   :   
                                   s   u   p   e   r   (   )   .   _   _   i   n   i   t   _   _   (   c   o   n   f   i   g   )   
   
                                   s   e   l   f   .   u   n   i   s   p   e   e   c   h   _   s   a   t       =       U   n   i   S   p   e   e   c   h   S   a   t   M   o   d   e   l   (   c   o   n   f   i   g   )   
                                   n   u   m   _   l   a   y   e   r   s       =       c   o   n   f   i   g   .   n   u   m   _   h   i   d   d   e   n   _   l   a   y   e   r   s       +       1           #       t   r   a   n   s   f   o   r   m   e   r       l   a   y   e   r   s       +       i   n   p   u   t       e   m   b   e   d   d   i   n   g   s   
                                   i   f       c   o   n   f   i   g   .   u   s   e   _   w   e   i   g   h   t   e   d   _   l   a   y   e   r   _   s   u   m   :   
                                                   s   e   l   f   .   l   a   y   e   r   _   w   e   i   g   h   t   s       =       n   n   .   P   a   r   a   m   e   t   e   r   (   t   o   r   c   h   .   o   n   e   s   (   n   u   m   _   l   a   y   e   r   s   )       /       n   u   m   _   l   a   y   e   r   s   )   
                                   s   e   l   f   .   p   r   o   j   e   c   t   o   r       =       n   n   .   L   i   n   e   a   r   (   c   o   n   f   i   g   .   h   i   d   d   e   n   _   s   i   z   e   ,       c   o   n   f   i   g   .   t   d   n   n   _   d   i   m   [   0   ]   )   
   
                                   t   d   n   n   _   l   a   y   e   r   s       =       [   T   D   N   N   L   a   y   e   r   (   c   o   n   f   i   g   ,       i   )       f   o   r       i       i   n       r   a   n   g   e   (   l   e   n   (   c   o   n   f   i   g   .   t   d   n   n   _   d   i   m   )   )   ]   
                                   s   e   l   f   .   t   d   n   n       =       n   n   .   M   o   d   u   l   e   L   i   s   t   (   t   d   n   n   _   l   a   y   e   r   s   )   
   
                                   s   e   l   f   .   f   e   a   t   u   r   e   _   e   x   t   r   a   c   t   o   r       =       n   n   .   L   i   n   e   a   r   (   c   o   n   f   i   g   .   t   d   n   n   _   d   i   m   [   -   1   ]       *       2   ,       c   o   n   f   i   g   .   x   v   e   c   t   o   r   _   o   u   t   p   u   t   _   d   i   m   )   
                                   s   e   l   f   .   c   l   a   s   s   i   f   i   e   r       =       n   n   .   L   i   n   e   a   r   (   c   o   n   f   i   g   .   x   v   e   c   t   o   r   _   o   u   t   p   u   t   _   d   i   m   ,       c   o   n   f   i   g   .   x   v   e   c   t   o   r   _   o   u   t   p   u   t   _   d   i   m   )   
   
                                   s   e   l   f   .   o   b   j   e   c   t   i   v   e       =       A   M   S   o   f   t   m   a   x   L   o   s   s   (   c   o   n   f   i   g   .   x   v   e   c   t   o   r   _   o   u   t   p   u   t   _   d   i   m   ,       c   o   n   f   i   g   .   n   u   m   _   l   a   b   e   l   s   )   
   
                                   s   e   l   f   .   i   n   i   t   _   w   e   i   g   h   t   s   (   )   
   
                   d   e   f       f   r   e   e   z   e   _   f   e   a   t   u   r   e   _   e   x   t   r   a   c   t   o   r   (   s   e   l   f   )   :   
                                   "   "   "   
                                   C   a   l   l   i   n   g       t   h   i   s       f   u   n   c   t   i   o   n       w   i   l   l       d   i   s   a   b   l   e       t   h   e       g   r   a   d   i   e   n   t       c   o   m   p   u   t   a   t   i   o   n       f   o   r       t   h   e       f   e   a   t   u   r   e       e   n   c   o   d   e   r       s   o       t   h   a   t       i   t   s       p   a   r   a   m   e   t   e   r       w   i   l   l   
                                   n   o   t       b   e       u   p   d   a   t   e   d       d   u   r   i   n   g       t   r   a   i   n   i   n   g   .   
                                   "   "   "   
                                   w   a   r   n   i   n   g   s   .   w   a   r   n   (   
                                                   "   T   h   e       m   e   t   h   o   d       `   f   r   e   e   z   e   _   f   e   a   t   u   r   e   _   e   x   t   r   a   c   t   o   r   `       i   s       d   e   p   r   e   c   a   t   e   d       a   n   d       w   i   l   l       b   e       r   e   m   o   v   e   d       i   n       T   r   a   n   s   f   o   r   m   e   r   s       v   5   .       "   
                                                   "   P   l   e   a   s   e       u   s   e       t   h   e       e   q   u   i   v   a   l   e   n   t       `   f   r   e   e   z   e   _   f   e   a   t   u   r   e   _   e   n   c   o   d   e   r   `       m   e   t   h   o   d       i   n   s   t   e   a   d   .   "   ,   
                                                   F   u   t   u   r   e   W   a   r   n   i   n   g   ,   
                                   )   
                                   s   e   l   f   .   f   r   e   e   z   e   _   f   e   a   t   u   r   e   _   e   n   c   o   d   e   r   (   )   
   
                   d   e   f       f   r   e   e   z   e   _   f   e   a   t   u   r   e   _   e   n   c   o   d   e   r   (   s   e   l   f   )   :   
                                   "   "   "   
                                   C   a   l   l   i   n   g       t   h   i   s       f   u   n   c   t   i   o   n       w   i   l   l       d   i   s   a   b   l   e       t   h   e       g   r   a   d   i   e   n   t       c   o   m   p   u   t   a   t   i   o   n       f   o   r       t   h   e       f   e   a   t   u   r   e       e   n   c   o   d   e   r       s   o       t   h   a   t       i   t   s       p   a   r   a   m   e   t   e   r       w   i   l   l   
                                   n   o   t       b   e       u   p   d   a   t   e   d       d   u   r   i   n   g       t   r   a   i   n   i   n   g   .   
                                   "   "   "   
                                   s   e   l   f   .   u   n   i   s   p   e   e   c   h   _   s   a   t   .   f   e   a   t   u   r   e   _   e   x   t   r   a   c   t   o   r   .   _   f   r   e   e   z   e   _   p   a   r   a   m   e   t   e   r   s   (   )   
   
                   d   e   f       f   r   e   e   z   e   _   b   a   s   e   _   m   o   d   e   l   (   s   e   l   f   )   :   
                                   "   "   "   
                                   C   a   l   l   i   n   g       t   h   i   s       f   u   n   c   t   i   o   n       w   i   l   l       d   i   s   a   b   l   e       t   h   e       g   r   a   d   i   e   n   t       c   o   m   p   u   t   a   t   i   o   n       f   o   r       t   h   e       b   a   s   e       m   o   d   e   l       s   o       t   h   a   t       i   t   s       p   a   r   a   m   e   t   e   r   s       w   i   l   l       n   o   t   
                                   b   e       u   p   d   a   t   e   d       d   u   r   i   n   g       t   r   a   i   n   i   n   g   .       O   n   l   y       t   h   e       c   l   a   s   s   i   f   i   c   a   t   i   o   n       h   e   a   d       w   i   l   l       b   e       u   p   d   a   t   e   d   .   
                                   "   "   "   
                                   f   o   r       p   a   r   a   m       i   n       s   e   l   f   .   u   n   i   s   p   e   e   c   h   _   s   a   t   .   p   a   r   a   m   e   t   e   r   s   (   )   :   
                                                   p   a   r   a   m   .   r   e   q   u   i   r   e   s   _   g   r   a   d       =       F   a   l   s   e   
   
                   d   e   f       _   g   e   t   _   t   d   n   n   _   o   u   t   p   u   t   _   l   e   n   g   t   h   s   (   s   e   l   f   ,       i   n   p   u   t   _   l   e   n   g   t   h   s   :       U   n   i   o   n   [   t   o   r   c   h   .   L   o   n   g   T   e   n   s   o   r   ,       i   n   t   ]   )   :   
                                   "   "   "   
                                   C   o   m   p   u   t   e   s       t   h   e       o   u   t   p   u   t       l   e   n   g   t   h       o   f       t   h   e       T   D   N   N       l   a   y   e   r   s   
                                   "   "   "   
   
                                   d   e   f       _   c   o   n   v   _   o   u   t   _   l   e   n   g   t   h   (   i   n   p   u   t   _   l   e   n   g   t   h   ,       k   e   r   n   e   l   _   s   i   z   e   ,       s   t   r   i   d   e   )   :   
               