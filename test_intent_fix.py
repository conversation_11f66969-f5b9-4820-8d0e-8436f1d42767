#!/usr/bin/env python3
"""
测试意图识别修复效果
"""

import requests
import json
import time

def test_intent_recognition():
    """测试意图识别修复效果"""
    
    base_url = "http://localhost:8000"
    
    print("🔍 测试意图识别修复效果")
    print("=" * 50)
    
    # 步骤1：先发送一个业务需求，进入COLLECTING_INFO状态
    print("步骤1: 发送业务需求，进入COLLECTING_INFO状态")
    init_response = requests.post(
        f"{base_url}/chat",
        json={
            "message": "我想设计一张海报",
            "user_id": "test_user"
        },
        timeout=30
    )

    if init_response.status_code != 200:
        print(f"❌ 初始化失败: {init_response.status_code}")
        return

    init_result = init_response.json()
    session_id = init_result.get('session_id')
    print(f"✅ 会话创建成功: {session_id}")
    print(f"AI回复: {init_result.get('response', '')[:100]}...")
    print()

    # 等待一下
    time.sleep(1)

    # 步骤2：在COLLECTING_INFO状态下询问价格差异
    test_message = "这些价格有什么不同"

    print(f"步骤2: 测试消息: '{test_message}'")
    print("期望结果: 在COLLECTING_INFO状态下应识别为process_answer")
    print()

    # 发送请求
    try:
        response = requests.post(
            f"{base_url}/chat",
            json={
                "message": test_message,
                "user_id": "test_user",
                "session_id": session_id
            },
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 请求成功")
            print(f"AI回复: {result.get('response', 'No response')}")
            print()
            
            # 检查日志以确认意图识别结果
            print("请检查日志中的意图识别结果...")
            print("查找关键词: '[LLM意图识别] JSON解析成功'")
            
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求出错: {e}")

if __name__ == "__main__":
    test_intent_recognition()
